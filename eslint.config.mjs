import { defineConfig, globalIgnores } from 'eslint/config';
import tsParser from '@typescript-eslint/parser';
import typescriptEslintEslintPlugin from '@typescript-eslint/eslint-plugin';
import prettier from 'eslint-plugin-prettier';
import reactHooks from 'eslint-plugin-react-hooks';
import globals from 'globals';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';
import { fixupPluginRules, fixupConfigRules } from '@eslint/compat';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import importPlugin from 'eslint-plugin-import';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default defineConfig([
  {
    languageOptions: {
      parser: tsParser,
      ecmaVersion: 2020,
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: { jsx: true },
      },
      globals: {
        ...globals.node,
        ...globals.browser,
      },
    },

    plugins: {
      '@typescript-eslint': fixupPluginRules(typescriptEslintEslintPlugin),
      prettier: fixupPluginRules(prettier),
      'react-hooks': fixupPluginRules(reactHooks),
      import: fixupPluginRules(importPlugin),
    },

    extends: fixupConfigRules(
      compat.extends(
        'plugin:@typescript-eslint/recommended',
        'plugin:prettier/recommended',
        'plugin:react-hooks/recommended',
        'plugin:import/typescript',
        'next/core-web-vitals'
      )
    ),

    rules: {
      'react/react-in-jsx-scope': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      'import/named': 'error',
      'import/no-unresolved': 'error',
      'import/default': 'error',
      'prettier/prettier': ['error', { endOfLine: 'auto' }],
    },

    ignores: ['node_modules', '.next', 'dist', '.vscode', 'coverage', '*.log'],
  },

  globalIgnores(
    [
      '**/node_modules/**',
      '**/.next/**',
      '**/dist/**',
      '**/build/**',
      '**/.vscode/**',
      '**/coverage/**',
      '**/*.log',
      'next-env.d.ts',
    ],
    'Ignore generated and unnecessary files'
  ),
]);
