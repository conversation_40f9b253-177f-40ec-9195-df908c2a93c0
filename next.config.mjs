import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
import withBundleAnalyzer from '@next/bundle-analyzer';

const nextConfig = {
  eslint: {
    dirs: ['src'],
  },

  reactStrictMode: true,

  outputFileTracingRoot: __dirname,

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: process.env.NEXT_PUBLIC_POPLA_IMAGE_OPTIMIZATION_DOMAIN,
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'popla.fra1.digitaloceanspaces.com',
        pathname: '/**',
      },
    ],
    unoptimized: true,
  },

  async rewrites() {
    return [
      {
        source: '/.well-known/assetlinks.json',
        destination: '/.well-known/assetlinks',
      },
    ];
  },

  logging: {
    fetches: {
      fullUrl: true,
    },
  },
};

export default withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
})(nextConfig);
