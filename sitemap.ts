import type { MetadataRoute } from 'next';

const routes: string[] = ['', '/about', '/events'];

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const staticRoutesSitemap = routes.map((route) => ({
    url: `${process.env.NEXTAUTH_URL}${route}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: route === '' ? 1 : 0.8,
  }));
  return [...staticRoutesSitemap];
}
