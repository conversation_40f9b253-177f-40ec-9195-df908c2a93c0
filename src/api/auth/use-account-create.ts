import type { AxiosError, AxiosResponse } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { ACCOUNT_CREATE_URL } from './constants';
import type { AccountCreationPayload, AccountCreationResponse } from './types';

export const useCreateAccount = createMutation<
  AccountCreationResponse,
  AccountCreationPayload,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: ACCOUNT_CREATE_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});

export const apiCreateAccountUser = async (
  payload: AccountCreationPayload | null
) =>
  HTTPS_BASE.post<AxiosResponse<AccountCreationResponse, any>>(
    ACCOUNT_CREATE_URL,
    payload
  );
