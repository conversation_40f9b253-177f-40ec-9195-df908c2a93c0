import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { USER_PASSWORD_CHANGE_URL } from '../users';

export type Variables = {
  oldPassword: string;
  password: string;
};

export const useChangePassword = createMutation<undefined, Variables, Error>({
  mutationFn: async ({ oldPassword, password }) =>
    HTTPS_BASE({
      url: USER_PASSWORD_CHANGE_URL,
      method: 'POST',
      data: { oldPassword, password },
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
