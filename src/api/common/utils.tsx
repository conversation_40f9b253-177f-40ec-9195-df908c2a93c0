import type {
  GetNextPageParamFunction,
  GetPreviousPageParamFunction,
} from '@tanstack/react-query';

import type { PaginateQuery } from './types';

type KeyParams = {
  [key: string]: any;
};
export const DEFAULT_LIMIT = 10;

export function getQueryKey<T extends KeyParams>(key: string, params?: T) {
  return [key, ...(params ? [params] : [])];
}

// for infinite query pages to flatList data
export function normalizePages<T>(pages?: PaginateQuery<T>[]): T[] {
  return pages
    ? pages.reduce((prev: T[], current) => [...prev, ...current.results], [])
    : [];
}

import { IEventCategories, ISingleEvent, type IEvent } from '@/api/events';
import {
  HomeDataResponse,
  UserFavouriteType,
  UserTicketsResponse,
} from '@/api/users';
import {
  DJLiveSession,
  GetSessionViewersResponse,
  LiveSessionInfoResponse,
  LiveSessionResponse,
  SpotifyTracksResponse,
  TrendingDjResponse,
  UserLiveSession,
} from '@/api/session';
import { UserObjectData } from '@/api';

// export function getUrlParameters(
//   url: string | null
// ): Record<string, string> | null {
//   if (!url) return null;

//   const queryString = url.split('?')[1];
//   if (!queryString) return {};

//   return queryString.split('&').reduce((acc: Record<string, string>, param) => {
//     const [key, value] = param.split('=');
//     if (key) {
//       acc[decodeURIComponent(key)] = decodeURIComponent(value || '');
//     }
//     return acc;
//   }, {});
// }

// a function that accept a url and return params as an object
export function getUrlParameters(
  url: string | null
): { [k: string]: string } | null {
  if (url === null) {
    return null;
  }

  const regex = /[?&]([^=#]+)=([^&#]*)/g;
  const params = {};
  let match;

  while ((match = regex.exec(url))) {
    if (match[1] !== null) {
      //@ts-expect-error -abc
      params[match[1]] = match[2];
    }
  }
  return params;
}

export const getPreviousPageParam: GetNextPageParamFunction<
  unknown,
  PaginateQuery<unknown>
> = (page) => getUrlParameters(page.previous)?.offset ?? null;

export const getNextPageParam: GetPreviousPageParamFunction<
  unknown,
  PaginateQuery<unknown>
> = (page) => getUrlParameters(page.next)?.offset ?? null;

/**
 * Constructs a URL query string from an object of parameters
 * - Handles primitive values, arrays, objects, and nested structures
 * - Supports customizable encoding and serialization options
 * - Properly handles null and undefined values
 *
 * @param params - Object containing query parameters
 * @param options - Configuration options for query string construction
 * @returns Formatted query string starting without the '?' character
 */
export const constructQueryStrings = <T extends Record<string, any>>(
  params: T,
  options: {
    /**
     * Whether to encode parameter names and values using encodeURIComponent
     * @default true
     */
    encode?: boolean;

    /**
     * How to handle null or undefined values
     * - 'ignore': Skip these parameters (default)
     * - 'empty': Include as empty string (e.g., 'param=')
     * - 'null': Include as the string 'null' (e.g., 'param=null')
     * @default 'ignore'
     */
    nullHandling?: 'ignore' | 'empty' | 'null';

    /**
     * Custom value transformer function
     * Allows custom handling of specific value types
     */
    valueTransformer?: (key: string, value: any) => string | string[] | null;

    /**
     * Whether to include array indices in parameter names
     * - true: 'items[0]=value&items[1]=value'
     * - false: 'items=value&items=value' (default)
     * @default false
     */
    arrayIndices?: boolean;

    /**
     * Separator for array values
     * If provided, array values will be joined using this separator
     * instead of creating multiple parameters
     * Example: { arrayFormat: ',' } → 'colors=red,green,blue'
     */
    arrayFormat?: string;
  } = {}
): string => {
  const {
    encode = true,
    nullHandling = 'ignore',
    valueTransformer,
    arrayIndices = false,
    arrayFormat,
  } = options;

  // Internal recursive function to handle nested objects
  const processParams = (
    obj: Record<string, any>,
    prefix = ''
  ): [string, string][] => {
    const result: [string, string][] = [];

    for (const [key, value] of Object.entries(obj)) {
      // Skip null/undefined based on options
      if (value === null || value === undefined) {
        if (nullHandling === 'ignore') continue;
        if (nullHandling === 'empty') {
          result.push([prefix ? `${prefix}[${key}]` : key, '']);
        } else if (nullHandling === 'null') {
          result.push([prefix ? `${prefix}[${key}]` : key, 'null']);
        }
        continue;
      }

      // Apply custom transformer if provided
      if (valueTransformer) {
        const transformed = valueTransformer(
          prefix ? `${prefix}[${key}]` : key,
          value
        );
        if (transformed === null) continue;

        if (typeof transformed === 'string') {
          result.push([prefix ? `${prefix}[${key}]` : key, transformed]);
          continue;
        } else if (Array.isArray(transformed)) {
          transformed.forEach((item) => {
            result.push([prefix ? `${prefix}[${key}]` : key, item]);
          });
          continue;
        }
      }

      // Handle different value types
      if (Array.isArray(value)) {
        // Use specified array format if provided
        if (arrayFormat) {
          result.push([
            prefix ? `${prefix}[${key}]` : key,
            value.join(arrayFormat),
          ]);
        } else {
          // Process array items
          value.forEach((item, index) => {
            if (item === null || item === undefined) {
              if (nullHandling === 'ignore') return;
              item = nullHandling === 'null' ? 'null' : '';
            }

            const paramName = arrayIndices
              ? `${prefix ? `${prefix}[${key}]` : key}[${index}]`
              : prefix
                ? `${prefix}[${key}]`
                : key;

            if (typeof item === 'object' && item !== null) {
              // For object items in array
              result.push(...processParams(item, paramName));
            } else {
              result.push([paramName, String(item)]);
            }
          });
        }
      } else if (typeof value === 'object') {
        // Process nested objects
        result.push(
          ...processParams(value, prefix ? `${prefix}[${key}]` : key)
        );
      } else {
        // Handle simple values
        result.push([prefix ? `${prefix}[${key}]` : key, String(value)]);
      }
    }

    return result;
  };

  // Process parameters and build the query string
  return processParams(params)
    .map(([key, value]) => {
      const encodedKey = encode ? encodeURIComponent(key) : key;
      const encodedValue = encode ? encodeURIComponent(value) : value;
      return `${encodedKey}=${encodedValue}`;
    })
    .join('&');
};

export const toQueryString = (data: Record<string, any>) => {
  return (
    '?' +
    Object.entries(data)
      .map(
        ([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
      )
      .join('&')
  );
};

const addUrl = (url: string | null | undefined, urls: string[]) => {
  if (url && typeof url === 'string' && url.trim()) {
    urls.push(url.trim());
  }
};

export const extractHomeDataImageUrls = (data: HomeDataResponse): string[] => {
  const urls: string[] = [];

  if (data.user?.profileImageUrl) {
    urls.push(data.user.profileImageUrl);
  }

  data.trendingDJs?.forEach((dj) => {
    if (dj.profileImageUrl) {
      urls.push(dj.profileImageUrl);
    }
  });

  data.djSessions?.forEach((session) => {
    if (session.bannerUrl) {
      urls.push(session.bannerUrl);
    }
    if (session.dj) {
      urls.push(session.dj.profileImageUrl);
    }
  });

  return [...new Set(urls)].filter(Boolean);
};

export const extractEventImageUrls = (events: IEvent[]): string[] => {
  const urls: string[] = [];

  if (Array.isArray(events)) {
    events.forEach((event) => {
      addUrl(event.bannerUrl, urls);

      if (Array.isArray(event.media)) {
        event.media.forEach((mediaItem) => {
          if (mediaItem && typeof mediaItem === 'object' && mediaItem.uri) {
            addUrl(mediaItem.uri, urls);
          }
        });
      }
    });
  }

  return [...new Set(urls)];
};

const isEventArray = (
  data: IEvent[] | UserObjectData[],
  type: UserFavouriteType
): data is IEvent[] => {
  return type === 'EVENT';
};

const isUserArray = (
  data: IEvent[] | UserObjectData[],
  type: string
): data is UserObjectData[] => {
  return type === 'ACCOUNT';
};

export const extractFavoritesImageUrls = (
  data: IEvent[] | UserObjectData[],
  type: UserFavouriteType
): string[] => {
  const urls: string[] = [];

  if (!Array.isArray(data) || data.length === 0) return urls;

  if (isEventArray(data, type)) {
    data.forEach((event) => {
      addUrl(event.bannerUrl, urls);

      event.media?.forEach((mediaItem) => {
        if (mediaItem && typeof mediaItem === 'object' && mediaItem.uri) {
          addUrl(mediaItem.uri, urls);
        }
      });
    });
  } else if (isUserArray(data, type)) {
    data.forEach((user) => {
      addUrl(user.profileImageUrl, urls);
    });
  }

  return [...new Set(urls)];
};

export const extractSessionImageUrls = (
  djSessions: LiveSessionResponse[]
): string[] => {
  const urls: string[] = [];

  if (Array.isArray(djSessions)) {
    djSessions.forEach((session) => {
      addUrl(session.bannerUrl, urls);

      if (session.dj) {
        addUrl(session.dj.profileImageUrl, urls);
      }
    });
  }

  return [...new Set(urls)];
};

export const extractSingleEventImageUrls = (data: ISingleEvent): string[] => {
  const urls: string[] = [];

  if (!data) return urls;

  addUrl(data.bannerUrl, urls);

  if (Array.isArray(data.media)) {
    data.media.forEach((mediaItem) => {
      addUrl(mediaItem, urls);
    });
  }

  if (data.organizer) {
    addUrl(data.organizer.profileImageUrl, urls);
  }

  return [...new Set(urls)].filter(Boolean);
};

export const extractLoggedInUserImageUrls = (
  data: UserObjectData
): string[] => {
  const urls: string[] = [];

  if (!data || typeof data !== 'object') return urls;

  addUrl(data.profileImageUrl, urls);

  return [...new Set(urls)].filter(Boolean);
};

export const extractEventCategoryImageUrls = (
  data: IEventCategories[]
): string[] => {
  if (!Array.isArray(data)) return [];
  return [
    ...new Set(
      data
        .map((c) => c.logo)
        .filter((logo): logo is string => typeof logo === 'string')
    ),
  ];
};

export const extractSessionViewerImageUrls = (
  data: GetSessionViewersResponse[]
): string[] => {
  if (!Array.isArray(data)) return [];
  return [
    ...new Set(
      data.map((viewer) => viewer.quester?.profileImageUrl).filter(Boolean)
    ),
  ];
};

export const extractLiveSessionImageUrls = (
  data: LiveSessionInfoResponse
): string[] => {
  const urls: string[] = [];

  if (!data || typeof data !== 'object') return urls;

  if (data.djSession?.bannerUrl) {
    addUrl(data.djSession.bannerUrl, urls);
  }

  if (data.djSession?.dj?.profileImageUrl) {
    addUrl(data.djSession.dj.profileImageUrl, urls);
  }

  if (Array.isArray(data.profileImageUrls)) {
    urls.push(...data.profileImageUrls.filter(Boolean));
  }

  if (Array.isArray(data.users)) {
    urls.push(
      ...(data.users
        .map((user) => user.profileImageUrl)
        .filter(Boolean) as string[])
    );
  }

  return [...new Set(urls)];
};

export const extractNearbyUserImageUrls = (
  data: UserObjectData[]
): string[] => {
  const urls: string[] = [];

  if (!Array.isArray(data)) return urls;

  for (const user of data) {
    if (user?.profileImageUrl) {
      urls.push(user.profileImageUrl);
    }
  }

  return [...new Set(urls)];
};

export const extractTrendingCreatorImageUrls = (
  data: TrendingDjResponse[]
): string[] => {
  if (!Array.isArray(data)) return [];
  return [
    ...new Set(data.map((item) => item?.profileImageUrl).filter(Boolean)),
  ];
};

export const extractUserTicketsImageUrls = (
  data: UserTicketsResponse
): string[] => {
  if (!data || !Array.isArray(data.eventsWithTicket)) return [];

  const urls: string[] = [];

  for (const event of data.eventsWithTicket) {
    if (event?.bannerUrl) urls.push(event.bannerUrl);
  }

  return [...new Set(urls)].filter(Boolean);
};

export const extractLiveSessionQueueImageUrls = (
  data: DJLiveSession & UserLiveSession
): string[] => {
  if (!data) return [];

  const urls: string[] = [];

  const queues = [
    ...(data.playQueue || []),
    ...(data.playedSongs || []),
    ...(data.requests || []),
    ...(data.queue || []),
  ];

  for (const q of queues) {
    if (q?.song?.coverUrl) urls.push(q.song.coverUrl);
    if (Array.isArray(q?.requestedBy)) {
      for (const user of q.requestedBy) {
        if (user?.profileImageUrl) urls.push(user.profileImageUrl);
      }
    }
  }

  return [...new Set(urls)].filter(Boolean);
};

export const extractSpotifyTrackImageUrls = (
  data: SpotifyTracksResponse
): string[] => {
  if (!data || !Array.isArray(data.tracks?.items)) return [];

  const urls: string[] = [];

  for (const track of data.tracks.items) {
    if (Array.isArray(track.album?.images)) {
      for (const image of track.album.images) {
        if (image?.url) urls.push(image.url);
      }
    }
  }

  return [...new Set(urls)].filter(Boolean);
};
