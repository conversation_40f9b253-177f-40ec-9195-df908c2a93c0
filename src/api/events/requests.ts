import type { AxiosResponse } from 'axios';
import { HTTPS_BASE } from '@/api';
import {
  ALL_EVENT_URL,
  BASE_EVENTS_URL,
  BASE_USER_URL,
  CREATE_EVENT_URL,
  EVENT_CATEGORIES_URL,
  EVENT_DETAILS_URL,
  EVENT_REGISTRATION_URL,
  FEATURED_EVENTS_URL,
  SEARCH_EVENTS_URL,
  UPCOMING_EVENT_URL,
  USER_EVENT_URL,
  USER_FAVORITES_EVENTS_URL,
  USER_TICKET_BY_ID,
  USER_TICKET_URL,
  USER_UPDATE_FAVORITES_EVENTS_URL,
} from '@/api';
import {
  AllEventsQueryParams,
  CreateEventPayload,
  CreateEventResponse,
  EditEventResponse,
  EventsSearchResultResponse,
  GetAllEventsResponse,
  GetEventCategoriesResponse,
  GetEventResponse,
  GetMyEventsResponse,
  GetTrendingEventsResponse,
  PurchaseEventTicketResponse,
  TrendingEventsQueryParams,
} from '@/api';

export const isAConfigOptionSet = <T extends object>(config: T): boolean => {
  return Object.keys(config).some(
    (key) => config[key as keyof T] !== undefined
  );
};

export const apiGetMyEvent = async (payload: { id: string | null }) =>
  HTTPS_BASE.get<GetEventResponse>(`${CREATE_EVENT_URL}/${payload.id}`);

export const apiGetEventDetails = async (payload: {
  slug: string;
  deviceId?: string;
  userId?: string;
}) =>
  HTTPS_BASE.get<GetEventResponse>(
    `${EVENT_DETAILS_URL}/${payload.slug}?deviceId=${payload.deviceId}&userId=${payload.userId}`
  );

export const apiGetEventCategories = async () =>
  HTTPS_BASE.get<GetEventCategoriesResponse>(`${EVENT_CATEGORIES_URL}`);

export const apiGetAllUpcomingEvent = async (config?: AllEventsQueryParams) => {
  if (!config) {
    return HTTPS_BASE.get(`${UPCOMING_EVENT_URL}`);
  }
  const url = `${UPCOMING_EVENT_URL}?${new URLSearchParams(
    config as any
  ).toString()}`;
  return HTTPS_BASE.get<GetAllEventsResponse>(url);
};

export const apiGetAllEvent = async (config?: AllEventsQueryParams) => {
  if (!config) {
    return HTTPS_BASE.get(`${ALL_EVENT_URL}`);
  }
  const url = `${ALL_EVENT_URL}?${new URLSearchParams(config as any).toString()}`;
  return HTTPS_BASE.get<GetAllEventsResponse>(url);
};

export const apiGetUserEvent = async (id: string, token: string) => {
  return HTTPS_BASE.get<GetAllEventsResponse>(`${USER_EVENT_URL(id)}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export const apiGetUserTickets = async (id: string, token: string) => {
  return HTTPS_BASE.get(`${USER_TICKET_URL(id)}`, {
    headers: {
      'Cache-Control': 'no-store',
      Authorization: `Bearer ${token}`,
    },
  });
};

export const apiGetEventById = async (eventId: string, token: string) => {
  return HTTPS_BASE.get(`${USER_TICKET_BY_ID(eventId)}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export const apiGetUserFavoritesEvents = async (token: string) => {
  return HTTPS_BASE.get(`${USER_FAVORITES_EVENTS_URL}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export const apiGetUpcomingEvent = async () =>
  HTTPS_BASE.get<GetAllEventsResponse>(`${UPCOMING_EVENT_URL}`);

export const apiMyEvents = async (payload: { id: string }) => {
  return HTTPS_BASE.get<GetMyEventsResponse>(
    `${BASE_USER_URL}/${payload.id}/events`
  );
};

export const apiCreateEvent = async (
  payload: CreateEventPayload | FormData,
  token?: string
) =>
  HTTPS_BASE.post<CreateEventResponse>(CREATE_EVENT_URL, payload, {
    headers: {
      'Content-Type': 'multipart/form-data;',
      Authorization: `Bearer ${token}`,
    },
  });

export const apiSearchEvents = async (queryString: string) => {
  return HTTPS_BASE.get<AxiosResponse<EventsSearchResultResponse>>(
    SEARCH_EVENTS_URL(queryString)
  );
};

export const apiGetFeaturedEvents = async (queryString: string) => {
  return HTTPS_BASE.get<AxiosResponse, any>(FEATURED_EVENTS_URL(queryString));
};

export const apiTrendingEvents = async (config?: TrendingEventsQueryParams) => {
  if (!config) {
    return HTTPS_BASE.get<GetTrendingEventsResponse>(
      `${CREATE_EVENT_URL}/trending`
    );
  }

  let url = `${CREATE_EVENT_URL}/trending${isAConfigOptionSet(config) && '?'}`;

  if (config.skip !== undefined && config.skip > 0) {
    if (url.endsWith('?')) {
      url += `skip=${config.skip}`;
    } else {
      url += `&skip=${config.skip}`;
    }
  }

  if (config.take !== undefined && config.take > 0) {
    if (url.endsWith('?')) {
      url += `take=${config.take}`;
    } else {
      url += `&take=${config.take}`;
    }
  }

  return HTTPS_BASE.get<GetTrendingEventsResponse>(url);
};

export const apiPurchaseTicketEvent = async (payload: {
  id: string;
  category: any[];
  token?: string;
}) =>
  HTTPS_BASE.post<PurchaseEventTicketResponse>(
    `${BASE_EVENTS_URL}/${payload.id}/tickets/purchase`,
    {
      category: payload.category,
      headers: {
        Authorization: `Bearer ${payload?.token}`,
      },
    }
  );

export const apiSetFavorites = async (payload: {
  type: string;
  eventId: string;
  token?: string;
}) =>
  HTTPS_BASE.post(`${USER_UPDATE_FAVORITES_EVENTS_URL}`, payload, {
    headers: {
      Authorization: `Bearer ${payload?.token}`,
    },
  });

export const apiRemoveFavorites = async (payload: {
  type: string;
  eventId: string;
  token?: string;
}) =>
  HTTPS_BASE.delete(`${USER_UPDATE_FAVORITES_EVENTS_URL}`, {
    headers: {
      Authorization: `Bearer ${payload?.token}`,
    },
    data: payload,
  });

export const apiDeleteMyEvent = async (payload: { id: string }) =>
  HTTPS_BASE.delete<any>(`${CREATE_EVENT_URL}/${payload.id}`);

export const apiEditEvent = async (payload: {
  formData: FormData;
  id: string;
  token?: string;
}) =>
  HTTPS_BASE.patch<EditEventResponse>(
    `${CREATE_EVENT_URL}/${payload.id}`,
    payload.formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data;',
        Authorization: `Bearer ${payload.token}`,
      },
    }
  );

export const apiEventRegistration = async (payload: {
  eventId: string;
  fullName: string;
  email: string;
}) =>
  HTTPS_BASE.post(`${EVENT_REGISTRATION_URL(payload.eventId)}`, {
    fullName: payload.fullName,
    email: payload.email,
  });
