import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { EVENT_ACCESS_CODES } from './constants';
import type { CreateEventAccessCodePayload, IEventAccessCode } from './types';

export const useCreateEventAccessCodes = createMutation<
  IEventAccessCode[],
  CreateEventAccessCodePayload,
  Error
>({
  mutationFn: async ({ quantity, expiresAt, eventId }) =>
    HTTPS_BASE({
      url: EVENT_ACCESS_CODES(eventId),
      method: 'POST',
      data: { quantity, expiresAt },
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
