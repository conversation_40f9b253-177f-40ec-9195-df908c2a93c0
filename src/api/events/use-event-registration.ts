import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { EVENT_REGISTRATION_URL } from './constants';
import type {
  EventRegistrationPayload,
  EventRegistrationResponse,
} from './types';

export const useEventRegistration = createMutation<
  EventRegistrationResponse,
  EventRegistrationPayload,
  Error
>({
  mutationFn: async ({ eventId, ...payload }) =>
    HTTPS_BASE({
      url: EVENT_REGISTRATION_URL(eventId),
      method: 'POST',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
