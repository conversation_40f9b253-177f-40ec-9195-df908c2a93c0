import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';
import { getQueryClient } from '../../api/common/api-provider';

import { constructQueryStrings, HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { SINGLE_EVENT_URL, SINGLE_EVENT_WITH_SLUG_URL } from './constants';
import { type ISingleEvent } from './types';

export const useGetEvent = createQuery<
  ISingleEvent,
  {
    id: string;
    targetCurrency?: string;
  },
  Error
>({
  queryKey: ['getEvent'],
  fetcher: async ({ id, targetCurrency }) => {
    const queryParams = constructQueryStrings<{ targetCurrency?: string }>({
      targetCurrency,
    });
    return HTTPS_BASE({
      url: SINGLE_EVENT_URL(id, queryParams),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});

export const useGetEventWithSlug = createQuery<
  ISingleEvent,
  {
    slug: string;
    userId?: string;
    deviceId?: string;
    targetCurrency?: string;
  },
  AxiosError
>({
  queryKey: ['getEventWithSlug'],
  fetcher: async ({ slug, targetCurrency, deviceId, userId }) => {
    const queryClient = getQueryClient();
    const token = queryClient.getQueryData<string>([
      'event-access-token',
      slug,
    ]);

    const queryParams = constructQueryStrings<{
      userId?: string;
      deviceId?: string;
      targetCurrency?: string;
    }>({
      targetCurrency,
      deviceId,
      userId,
    });

    try {
      const response = await HTTPS_BASE({
        url: SINGLE_EVENT_WITH_SLUG_URL(slug, queryParams),
        method: 'GET',
        headers: token ? { 'x-event-access-token': token } : {},
      });

      return response.data.data;
    } catch (error) {
      // if (axios.isAxiosError<ErrorResponse>(error)) {
      //   message = error.response?.data?.message ?? message;
      // } else if (error instanceof Error) {
      //   message = error.message;
      // }

      // throw new Error(error);

      throw error;
    }
  },
});
