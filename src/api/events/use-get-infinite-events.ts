import type { AxiosError } from 'axios';
import axios from 'axios';
import { createInfiniteQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import {
  constructQueryStrings,
  extractEventImageUrls,
  HTTPS_BASE,
} from '../common';
import type { ErrorResponse } from '../common/types';
import { ALL_EVENT_URL } from './constants';
import {
  type AllEventsQueryParams,
  type EventsSearchResultResponse,
} from './types';

const PAGE_SIZE = 10;

export const useGetInfiniteEvents = createInfiniteQuery<
  EventsSearchResultResponse,
  Omit<AllEventsQueryParams, 'skip'>,
  Error
>({
  queryKey: ['getEvents'],
  fetcher: async (queryObj, { pageParam = 0 }) => {
    const queryWithPagination = {
      ...queryObj,
      skip: pageParam,
      take: queryObj.take || PAGE_SIZE,
    };
    const queryParams =
      constructQueryStrings<AllEventsQueryParams>(queryWithPagination);
    return HTTPS_BASE({
      url: ALL_EVENT_URL(queryParams),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as EventsSearchResultResponse;

        const imageUrls = extractEventImageUrls(data.events);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls);
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
  initialPageParam: 0,
  getNextPageParam: (
    lastPage: EventsSearchResultResponse,
    allPages: EventsSearchResultResponse[]
  ) => {
    const loadedCount = allPages.flatMap((page) => page.events).length;

    // If the last page has fewer transactions than requested, we've reached the end
    if (loadedCount >= lastPage.total) {
      return undefined;
    }

    return loadedCount;
  },
});
