import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import {
  constructQueryStrings,
  extractEventImageUrls,
  HTTPS_BASE,
} from '../common';
import type { ErrorResponse } from '../common/types';
import { UPCOMING_EVENT_URL } from './constants';
import {
  type EventsSearchResultResponse,
  type UpcomingEventsQueryParams,
} from './types';

export const useGetUpcomingEvents = createQuery<
  EventsSearchResultResponse,
  UpcomingEventsQueryParams,
  Error
>({
  queryKey: ['getUpcomingEvents'],
  fetcher: async (queryObj) => {
    const queryParams =
      constructQueryStrings<UpcomingEventsQueryParams>(queryObj);
    return HTTPS_BASE({
      url: UPCOMING_EVENT_URL(queryParams),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as EventsSearchResultResponse;

        const imageUrls = extractEventImageUrls(data.events);

        if (imageUrls.length > 0) {
          preloadImages(imageUrls);
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
