import { type LocationType } from '@/types';

import { type LocationObject } from '../auth';

// Session status type
export type LiveSessionStatus = 'LIVE' | 'ENDED';

// Request status type
export type RequestStatus =
  | 'ACCEPTED'
  | 'REJECTED'
  | 'PENDING'
  | 'CANCELLED'
  | 'PLAYED';

// Base song interface
export interface Song {
  id: string;
  title: string;
  artist: string;
  coverUrl: string;
}

// User who requested a song
export interface RequestedBy {
  id: string;
  username: string;
  profileImageUrl?: string | null;
}

// DJ profile information
export interface DJProfile {
  id?: string;
  fullName: string;
  username: string;
  profileImageUrl: string;
}

// Queue item (song request)
export interface Queue {
  // Core properties
  id?: string;
  djSessionId?: string;
  userId?: string;
  songId: string;
  status: RequestStatus;
  isBoosted: boolean;
  song: Song;

  transactionId?: string;
  transactionIds?: string[];
  shoutOutTo?: string | null;
  requestedBy?: RequestedBy[];
  shoutOuts?: string[];
  meta?: any;
  songRequestIds?: string[];

  // Timestamps
  respondedAt?: any;
  playedAt?: any;
  createdAt: string;
  timestamp?: string;
}

// DJ live session interface
export interface DJLiveSession {
  playQueue: Queue[];
  playedSongs?: Queue[];
  requests: Queue[];
  creatorId: string;
}

// User live session interface
export interface UserLiveSession {
  queue: Queue[];
}

// Response from creating a live session
export interface CreateLiveSessionResponse extends DJLiveSession {
  id: string;
  djId: string;
  title: string;
  type?: string;
  locationType?: LocationType;

  // Session details
  bannerUrl: string;
  cost: number;
  shoutoutCost?: number;
  status: LiveSessionStatus;
  location: LocationObject;
  songRequestEnabled: boolean;
  shoutoutEnabled: boolean;
  endTime: null | string;

  dj: DJProfile;

  // Queue data (initialized as empty)
  playQueue: [];
  playedSongs: [];
  requests: [];

  // Timestamps
  createdAt: any;
  updatedAt: any;

  // Pricing and features
  currency?: string;
  isFree?: boolean;
  enableVideo?: boolean;
  cameraHidden?: boolean;
  conversionRate?: number;
  originalCost?: number;
  convertedCost?: number;
}

// Session information with questers data
export interface SessionInfo extends CreateLiveSessionResponse {
  dj: DJProfile;
  questersOnSession: number;
  profileImageUrls?: string[];
}

// Spotify authentication data
export interface SpotifyTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
}

export interface SpotifyAccessData extends SpotifyTokenResponse {
  expirationDate?: any;
}

export interface TrackItems {
  id: string;
  name: string;
  artists: { name: string; [key: string]: any }[];
  album: {
    images: { url: string; height: number; width: number }[];
    [key: string]: any;
  };
  duration_ms: number;
  [key: string]: any;
}

// Request history item
export interface IRequestHistoryItem {
  id: string;
  djSessionId: string;
  userId: string;

  status: RequestStatus;
  transactionId: string;
  songId: string;
  shoutOutTo: string | null;
  isBoosted: boolean;
  song: Song;

  meta: object | null;

  // Timestamps
  respondedAt: string | null;
  playedAt: string | null;
  createdAt: string;
  updatedAt: string;

  // Session info
  djSession: {
    title: string;
    dj: DJProfile;
  };
}

export interface LiveSessionResponse {
  id: string;
  cost: number;
  title: string;
  status: LiveSessionStatus;
  dj: DJProfile;
  bannerUrl: string;
  createdAt: string;
  distance: any;
  location: LocationObject;
  isfavourite?: boolean;
  sessionId?: string;
}

export interface TrendingDjResponse {
  id: string;
  profileImageUrl: string;
  fullName: string;
  username: string;
  questerCount: number;
}

// Trending DJ item type
export interface TrendingDJItem {
  id: string;
  name: string;
  avatar: string;
}

// Combined type for session and trending list
export type SessionOrTrendingItem = LiveSessionResponse | TrendingDJItem;

export type DJSessionState = {
  sessionInfo: Omit<SessionInfo, 'requests' | 'playQueue' | 'playedSongs'>;
  dj: DJLiveSession;
  user: UserLiveSession;
  topLiveSessions: LiveSessionResponse[];
  combinedSessionAndTrendingList: SessionOrTrendingItem[];
  recentLiveSessions: LiveSessionResponse[];
  spotifyData: SpotifyAccessData | null;
  trackResults: TrackItems[];
  requestHistory: IRequestHistoryItem[];
  loading: boolean;
  songRequestHistoryOptions: { label: string; value: string }[];
};

export type SessionType = 'REQUEST' | 'SHOUTOUT';
export interface DJSessionInterface {
  id: string;
  bannerUrl: string;
  cost: number;
  shoutoutCost: number;
  createdAt: any;
  title: string;
  type: SessionType;
  isFree: boolean;
  locationType: LocationType;
  enableVideo: boolean;
  cameraHidden?: boolean;
  songRequestEnabled: boolean;
  shoutoutEnabled: boolean;
  status: LiveSessionStatus;
  djId: string;
  isFavourite: boolean;
  dj: DJProfile;
  currency: string;
  conversionRate: number;
  originalCost: number;
  convertedCost: number;
}

export interface LiveSessionInterface {
  djSession: CreateLiveSessionResponse;
  questersOnSession: number;
}

export interface LiveSessionInfoResponse {
  djSession: DJSessionInterface;
  questersOnSession: number;
  profileImageUrls?: string[];
  users?: {
    id: string;
    username: string;
    fullName: string;
    profileImageUrl: string | null;
  }[];
}

export type SongUpdatePayload = {
  songId: string;
  sessionId: string;
  status: RequestStatus;
};

export type SongRecordingQuery = {
  sessionId: string;
  songId: string;
};

export interface SongRequestPayload {
  songId?: string;
  coverUrl?: string;
  title?: string;
  artist?: string;
  isBoosted?: boolean;
  userCurrency?: string;
  shoutOutTo?: string;
}

export type SongDeletePayload = {
  sessionId: string;
  songId: string;
};

export interface SessionQuery {
  skip: string;
  take: string;
  status: LiveSessionStatus;
  title: string;
  maxDistance: string;
  location: string;
  coordinates: string;
}

export interface SearchLiveSessionResponse {
  djSessions: LiveSessionResponse[];
  total: number;
}

export interface SpotifyTokenPayload {
  clientId: string;
  clientSecret: string;
}

export interface SpotifyTracksResponse {
  tracks: {
    href: string;
    items: TrackItems[];
    limit: 20;
    next: string;
    offset: 0;
    previous: null;
    total: number;
  };
}

export interface RequestHistoryPayload {
  take?: number;
  skip?: number;
}

export interface GetRequestHistoryResponse {
  message: string;
  statusCode: number;
  data: IRequestHistoryItem[];
}

export interface QuesterSessionInfo {
  questers: {
    count: number;
    profileImageUrls: string[];
    users: {
      id: string;
      username: string;
      fullName: string;
      profileImageUrl: string | null;
    }[];
  };
}

export interface GetSessionViewersResponse {
  id: string;
  questerId: string;
  djSessionId: string;
  createdAt: string;
  updatedAt: string;
  quester: {
    id: string;
    username: string;
    profileImageUrl: string;
    fullName: string;
  };
}

export enum SessionEvents {
  JOIN_SESSION = 'JOIN_SESSION',
  LEAVE_SESSION = 'LEAVE_SESSION',
  MY_QUEUE = 'MY_QUEUE',
  DJ_QUEUES = 'DJ_QUEUES',
  SESSION_ENDED = 'SESSION_ENDED',
  QUESTERS = 'QUESTERS',
  SESSION_STARTED = 'SESSION_STARTED',
}
