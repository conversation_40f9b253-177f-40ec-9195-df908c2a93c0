import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { AGORA_RTM_TOKEN_URL } from './constants';

export const useGetAgoraRtmToken = createQuery<
  undefined,
  { uid: string },
  Error
>({
  queryKey: ['getAgoraRtmToken'],
  fetcher: async ({ uid }) =>
    HTTPS_BASE({
      url: AGORA_RTM_TOKEN_URL(uid),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
