import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { AGORA_TOKEN_URL } from './constants';

export const useGetAgoraToken = createQuery<
  { token: string },
  { channelName: string; uid: number; role: number },
  Error
>({
  queryKey: ['getAgoraToken'],
  fetcher: async ({ channelName, uid, role }) =>
    HTTPS_BASE({
      url: AGORA_TOKEN_URL(channelName, uid, role),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
