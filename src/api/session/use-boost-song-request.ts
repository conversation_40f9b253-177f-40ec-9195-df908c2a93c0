import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE, toQueryString } from '../common';
import type { ErrorResponse } from '../common/types';
import { BOOST_REQUEST_URL } from './constants';

export const useBoostSongRequest = createMutation<
  undefined,
  { sessionId: string; payload: { songId: string; userCurrency?: string } },
  Error
>({
  mutationFn: async ({ sessionId, payload }) =>
    HTTPS_BASE({
      url: BOOST_REQUEST_URL(
        sessionId,
        payload.songId,
        toQueryString({ userCurrency: payload.userCurrency })
      ),
      method: 'PUT',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
