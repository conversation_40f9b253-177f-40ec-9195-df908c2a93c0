import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_FILE } from '../common';
import type { ErrorResponse, FormData } from '../common/types';
import { BASE_DJ_SESSION_URL } from './constants';
import type { CreateLiveSessionResponse } from './types';

export const useCreateLiveSession = createMutation<
  CreateLiveSessionResponse,
  FormData,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_FILE({
      url: BASE_DJ_SESSION_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
