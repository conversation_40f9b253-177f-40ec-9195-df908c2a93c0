import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { DELETE_SONG_REQUEST_URL } from './constants';
import { type SongDeletePayload } from './types';

export const useDeleteSongRequest = createMutation<
  undefined,
  SongDeletePayload,
  Error
>({
  mutationFn: async (payload) =>
    HTTPS_BASE({
      url: DELETE_SONG_REQUEST_URL(payload.sessionId, payload.songId),
      method: 'DELETE',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
