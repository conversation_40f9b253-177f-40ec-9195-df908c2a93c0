import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { END_SESSION_URL } from './constants';

export const useEndLiveSession = createMutation<
  undefined,
  { sessionId: string },
  Error
>({
  mutationFn: async ({ sessionId }) =>
    HTTPS_BASE({
      url: END_SESSION_URL(sessionId),
      method: 'PATCH',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
