import type { AxiosError } from 'axios';
import axios from 'axios';
import { createInfiniteQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { GET_REQUEST_HISTORY_URL } from './constants';
import {
  type GetRequestHistoryResponse,
  type RequestHistoryPayload,
} from './types';

const PAGE_SIZE = 10;

export interface RequestHistoryPayloadInfiniteFilterConfig
  extends Omit<RequestHistoryPayload, 'skip'> {
  take?: number;
}

export const useGetRequestHistory = createInfiniteQuery<
  GetRequestHistoryResponse['data'],
  RequestHistoryPayloadInfiniteFilterConfig,
  Error
>({
  queryKey: ['getRequestHistory'],
  fetcher: async (queryObj, { pageParam = 0 }) => {
    const queryWithPagination = {
      ...queryObj,
      skip: pageParam as number,
      take: queryObj.take || PAGE_SIZE,
    };
    const queryParams =
      constructQueryStrings<RequestHistoryPayloadInfiniteFilterConfig>(
        queryWithPagination
      );
    return HTTPS_BASE({
      url: GET_REQUEST_HISTORY_URL(queryParams),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
  initialPageParam: 0,
  getNextPageParam: (
    lastPage: GetRequestHistoryResponse['data'],
    allPages: GetRequestHistoryResponse['data'][]
  ) => {
    const loadedCount = allPages.flatMap(
      (sessionRequestHistory) => sessionRequestHistory
    ).length;

    if (lastPage.length < PAGE_SIZE) {
      return undefined;
    }

    return loadedCount;
  },
});
