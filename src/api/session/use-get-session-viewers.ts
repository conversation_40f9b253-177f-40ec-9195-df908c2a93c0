import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { preloadImages } from '@/components/ui';

import { extractSessionViewerImageUrls, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { SESSION_VIEWERS_URL } from './constants';
import { type GetSessionViewersResponse } from './types';

export const useGetLiveSessionViewers = createQuery<
  GetSessionViewersResponse[],
  {
    sessionId: string;
  },
  Error
>({
  queryKey: ['getSessionViewers'],
  fetcher: async ({ sessionId }) =>
    HTTPS_BASE({
      url: SESSION_VIEWERS_URL(sessionId),
      method: 'GET',
    })
      .then((response) => {
        const data = response.data.data as GetSessionViewersResponse[];

        const imageUrls = extractSessionViewerImageUrls(data);
        if (imageUrls.length > 0) {
          try {
            preloadImages(imageUrls);
          } catch (error) {
            console.warn('Failed to preload session viewer images:', error);
          }
        }

        return data;
      })
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
