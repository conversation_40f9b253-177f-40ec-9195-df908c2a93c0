import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { JOIN_SESSION_URL } from './constants';

export const useJoinLiveSession = createMutation<
  undefined,
  { sessionId: string },
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: JOIN_SESSION_URL(data.sessionId),
      method: 'PUT',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
