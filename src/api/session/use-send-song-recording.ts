import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_FILE } from '../common';
import type { ErrorResponse } from '../common/types';
import { SEND_SONG_RECORDING_URL } from './constants';
import { type SongRecordingQuery } from './types';

export const useSendSongRecording = createMutation<
  undefined,
  {
    query: SongRecordingQuery;
    body: FormData;
  },
  Error
>({
  mutationFn: async ({ query, body }) =>
    HTTPS_FILE({
      url: SEND_SONG_RECORDING_URL(query.sessionId, query.songId),
      method: 'POST',
      data: body,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
