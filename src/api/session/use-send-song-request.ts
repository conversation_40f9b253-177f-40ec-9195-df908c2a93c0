import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { REQUEST_SONG_URL } from './constants';
import { type SongRequestPayload } from './types';

export const useSendSongRequest = createMutation<
  undefined,
  { sessionId: string; payload: SongRequestPayload },
  Error
>({
  mutationFn: async ({ sessionId, payload }) =>
    HTTPS_BASE({
      url: REQUEST_SONG_URL(sessionId),
      method: 'POST',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
