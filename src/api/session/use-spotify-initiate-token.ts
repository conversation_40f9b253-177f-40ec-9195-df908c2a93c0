import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import type { ErrorResponse } from '../common/types';
import { SPOTIFY_TOKEN_URL } from './constants';
import { type SpotifyTokenPayload, type SpotifyTokenResponse } from './types';

export const useSpotifyInitiateToken = createMutation<
  SpotifyTokenResponse,
  SpotifyTokenPayload,
  Error
>({
  mutationFn: async ({ clientId, clientSecret }) =>
    axios
      .post<SpotifyTokenResponse>(
        SPOTIFY_TOKEN_URL,
        `grant_type=client_credentials&client_id=${clientId}&client_secret=${clientSecret}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      )
      .then((response) => response.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          console.log(
            '🚀 ~ error.response:',
            JSON.stringify(error.request),
            error.response,
            error.response?.data
          );
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
