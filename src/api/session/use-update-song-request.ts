import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { UPDATE_SONG_REQUEST_URL } from './constants';
import { type SongUpdatePayload } from './types';

export const useUpdateSongRequest = createMutation<
  undefined,
  SongUpdatePayload,
  Error
>({
  mutationFn: async ({ sessionId, songId, status }) =>
    HTTPS_BASE({
      url: UPDATE_SONG_REQUEST_URL(sessionId),
      method: 'PATCH',
      data: {
        songId,
        status,
      },
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
