export const BASE_TRANSACTION_URL = '/transactions';

export const GET_TRANSACTION_URL = (id: string) =>
  `${BASE_TRANSACTION_URL}/${id}`;

export const INITIALIZE_TRANSACTION_URL = `${BASE_TRANSACTION_URL}/initialize`;

export const INITIALIZE_STRIPE_TRANSACTION_URL = `${BASE_TRANSACTION_URL}/stripe/initialize`;

export const VERIFY_PAYSTACK_TRANSACTION_URL = (transactionRef: string) =>
  `${BASE_TRANSACTION_URL}/paystack/verify/${transactionRef}`;

export const VERIFY_NUBAN_URL = (bankCode: string, accountNumber: string) =>
  `https://app.nuban.com.ng/api/NUBAN-RNACLLZC1770?bank_code=${bankCode}&acc_no=${accountNumber}`;

export const GUESS_NUBAN_URL = (accountNumber: string) =>
  `https://app.nuban.com.ng/possible-banks/NUBAN-RNACLLZC1770?acc_no=${accountNumber}`;

// PAYOUTS
export const BASE_PAYOUT_URL = '/payouts';
export const PAYOUT_VERIFICATION_URL = `${BASE_PAYOUT_URL}/verification/bank`;
export const SAVE_PAYOUT_INFO_URL = `${BASE_PAYOUT_URL}/verification/add-bank`;
export const GET_PAYOUT_INFO_URL = `${BASE_PAYOUT_URL}/recipients`;
export const REQUEST_PAYOUT_URL = `${BASE_PAYOUT_URL}/request`;
export const LINK_BVN_CAC_URL = `${BASE_PAYOUT_URL}/account-verification/link-bvn-cac`;

import { BASE_USER_URL } from '@/api/users';

export const TRANSACTION_GRAPH_URL = `${BASE_USER_URL}/transactions/graph`;
export const STRIP_PAYMENT_INIT_URL = `${BASE_TRANSACTION_URL}/stripe/initialize`;

export const VERIFY_BANK = `${BASE_PAYOUT_URL}/verification/verify-bank`;
export const GET_BANKS = `${BASE_PAYOUT_URL}/get-banks`;
export const LINK_BVN_URL = `${BASE_PAYOUT_URL}/verification/link-bvn`;
