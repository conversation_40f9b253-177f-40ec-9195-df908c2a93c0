import { type SelectedTicket } from '@/lib';

export interface IBank {
  fullName: string;
  lastName: string;
  firstName: string;
  middleName: string;
  accountNumber: string;
  bankName: string;
}

export interface InitializeTransactionPayload {
  amount: number;
  country: string;
}
export interface Song {
  id: string;
  title: string;
  artist: string;
  coverUrl: string;
}
export interface InitializeTicketTransactionPayload {
  eventId: number;
  tickets: Omit<SelectedTicket, 'cost'>[];
  guestEmail?: string;
  guestName?: string;
}

export interface InitializeTransactionResponse {
  id: string;
  amount: number;
  balance: number;
  type: TransactionType;
  category: TransactionCategory;
  status: TransactionStatus;
  meta: Record<string, any>;
  walletId: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface TransactionResponse extends ITransaction {
  djSession?: {
    title: string;
    cost: number;
    shoutoutCost: number | null;
    playedSongRequestsWithShoutout: number;
    playedSongRequestsWithoutShoutout: number;
    boostedSongRequests: number;
    shoutOutRequests: number;
  };

  songRequest?: {
    status: SongRequestStatus;
    shoutOutTo: string | null;
    isBoosted: boolean;
    song: Song;
    djSession: {
      bannerUrl: string;
      title: string;
      cost: number;
      shoutoutCost: number | null;
      dj: {
        id: string;
        username: string;
        profileImageUrl: string | null;
      };
    };
  };

  ticket?: {
    event: {
      bannerUrl: string;
      title: string;
      startTime: Date;
      organizer: {
        id: string;
        username: string;
        profileImageUrl: string | null;
      };
    };
    meta: {
      user?: {
        email: string;
        fullName: string;
      };
      currency?: string;
      breakdown?: {
        cost: number;
        type: string;
        quantity: number;
      }[];
    };
    serviceFee: number;
    discount?: {
      id: string;
      code: string;
      amount: number;
    };
  };
}

export interface SongRequestStatus {
  PLAYED: 'PLAYED';
  PENDING: 'PENDING';
  ACCEPTED: 'ACCEPTED';
  REJECTED: 'REJECTED';
  CANCELLED: 'CANCELLED';
}

export interface VerifyPaystackTransactionPayload {
  transactionRef: string;
}

export interface VerifyPaystackTransactionResponse {
  amount: number;
  status: 'success' | 'failed';
}

export interface PayoutInformationPayload {
  accountName: string;
  accountNumber: string;
  bankCode: string;
  bankName: string;
}

export interface SaveAccountDetailsResponse {
  active: boolean;
  createdAt: Date;
  currency: string;
  description: any;
  details: {
    account_name: string;
    account_number: string;
    authorization_code: any;
    bank_code: string;
    bank_name: string;
  };
  domain: string;
  email: any;
  id: number;
  integration: number;
  isDeleted: boolean;
  is_deleted: boolean;
  metadata: any;
  name: string;
  recipient_code: string;
  type: string;
  updatedAt: Date;
}

export interface MakeWithdrawalRequestResponse {
  amount: number;
  createdAt: Date;
  id: string;
  meta: any;
  payoutDate: Date;
  status: string;
  transactionId: string;
  updatedAt: Date;
  userId: string;
}

export interface ITransaction {
  id: string;
  amount: number;
  balance: number;
  type: TransactionType;
  category: TransactionCategory;
  status: TransactionStatus;
  meta: Record<string, any>;
  walletId: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  currency?: string;
}

export interface PayoutVerificationResponse {
  bvn: {
    lastName: string;
    firstName: string;
    middleName: string;
    dateOfBirth: string;
    phoneNumber: string;
  };
  bank: {
    accountNumber: string;
    fullName: string;
    lastName: string;
    firstName: string;
    middleName: string;
  };
  isVerified: boolean;
}
export interface SaveAccountDetailsResponse {
  active: boolean;
  createdAt: Date;
  currency: string;
  description: any;
  details: {
    account_name: string;
    account_number: string;
    authorization_code: any;
    bank_code: string;
    bank_name: string;
  };
  domain: string;
  email: any;
  id: number;
  integration: number;
  isDeleted: boolean;
  is_deleted: boolean;
  metadata: any;
  name: string;
  recipient_code: string;
  type: string;
  updatedAt: Date;
}

export interface PayoutVerificationPayload {
  bankCode: string;
  accountNumber: string;
  bankName?: string;
  accountName?: string;
  bvn: string;
}
export interface PayoutInformationPayload {
  accountName: string;
  accountNumber: string;
  bankCode: string;
}

export interface IVerifyBank {
  accountNumber: string;
  bankCode: string;
}

// src/interfaces/bank.ts

export interface Bank {
  bankName: string;
  bankCode: string;
}

export interface MakeWithdrawalRequestResponse {
  amount: number;
  createdAt: Date;
  id: string;
  meta: any;
  payoutDate: Date;
  status: string;
  transactionId: string;
  updatedAt: Date;
  userId: string;
}

export interface IMakeWithdrawalRequestResponse {
  message: string;
  success: boolean;
  data?: any;
}

export interface ITransactionFilterConfig {
  id: string;
  skip?: number;
  take?: number;
  status?: string;
  category?: string;
  type?: string;
  currency?: string;
  startDate?: number;
  endDate?: number;
}

export interface GetAccountDetailsResponse {
  recipients: AccountInformation[];
}

export interface GetTransactionHistoryResponse {
  transactions: ITransaction[];
  total: number;
}

export interface TransactionState {
  amount: number;
  transactionRef: string;
  currentTransactionStatus: string;
  transactionHistory: ITransaction[];
  groupedTransactionHistory: Record<string, TransactionItemProps[]> | null;
  accountInformation: AccountInformation;
  loading: boolean;
  canLoadMore: boolean;
}

export interface ILinkBVN {
  bvn: string;
}

export interface AccountInformation {
  accountName: string;
  accountNumber: string;
  bankName?: string;
  bankCode: string;
  isBvnVerified?: boolean;
}

export interface TransactionItemProps {
  type: 'CREDIT' | 'DEBIT';
  category:
    | 'FUND_WALLET'
    | 'WITHDRAW_WALLET'
    | 'SONG_REQUEST'
    | 'DJ_SESSION_EARNINGS';
  amount: number;
  createdAt: string;
  status: 'PENDING' | 'SUCCESS' | 'FAILED';
  currency?: string;
}

export interface TransactionItemProps {
  type: 'CREDIT' | 'DEBIT';
  category:
    | 'FUND_WALLET'
    | 'WITHDRAW_WALLET'
    | 'SONG_REQUEST'
    | 'DJ_SESSION_EARNINGS';
  amount: number;
  createdAt: string;
  status: 'PENDING' | 'SUCCESS' | 'FAILED';
  currency?: string;
}

export interface IWalletTransactionPin {
  previousPin: string;
  pin: string;
}

export interface ICreateWalletTransactionPin {
  pin: string;
}

export interface IVerifyWalletTransactionPin {
  pin: string;
}

export type TransactionType = 'CREDIT' | 'DEBIT';
export type TransactionStatus = 'PENDING' | 'FAILED' | 'SUCCESS';
export type TransactionCategory =
  | 'FUND_WALLET'
  | 'WITHDRAW_WALLET'
  | 'SONG_REQUEST'
  | 'DJ_SESSION_EARNINGS'
  | 'EVENT_TICKET_PURCHASE'
  | 'EVENT_TICKET_EARNINGS';

export interface Transaction {
  id: string;
  amount: number;
  balance: number;
  type: TransactionType;
  category: TransactionCategory;
  status: TransactionStatus;
  meta: Record<string, any>;
  walletId: string;
  userId: string;
  createdAt?: string;
  updatedAt?: string;
  currency?: string;
}
