import type { AxiosError } from 'axios';
import axios from 'axios';
import { createInfiniteQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { GET_USER_TRANSACTION_HISTORY_URL } from './constants';
import { type GetTransactionHistoryResponse } from '../transactions/types';
import { type ITransactionFilterConfig } from '@/api/transactions';

const PAGE_SIZE = 10;

export interface ITransactionInfiniteFilterConfig
  extends Omit<ITransactionFilterConfig, 'skip'> {
  take?: number;
}

export const useGetTransactionHistory = createInfiniteQuery<
  GetTransactionHistoryResponse,
  ITransactionInfiniteFilterConfig,
  Error
>({
  queryKey: ['getTransactionHistory'],
  fetcher: async (queryObj, { pageParam = 0 }) => {
    const queryWithPagination = {
      ...queryObj,
      skip: pageParam as number,
      take: queryObj.take || PAGE_SIZE,
    };
    const queryParams =
      constructQueryStrings<ITransactionFilterConfig>(queryWithPagination);
    return HTTPS_BASE({
      url: GET_USER_TRANSACTION_HISTORY_URL(queryObj.id, queryParams),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
  initialPageParam: 0,
  getNextPageParam: (
    lastPage: GetTransactionHistoryResponse,
    allPages: GetTransactionHistoryResponse[]
  ) => {
    const loadedCount = allPages.flatMap((page) => page.transactions).length;

    // If the last page has fewer transactions than requested, we've reached the end
    if (
      lastPage.transactions.length < PAGE_SIZE ||
      loadedCount >= lastPage.total
    ) {
      return undefined;
    }

    return loadedCount;
  },
});
