'use client';
import { NextImage } from '@/components/ui/NextImage';
import { H2 } from '@/components/ui/typography';
import Link from 'next/link';
import { Image } from '@/components/ui';

export default function AuthLayout({
  children,
}: {
  readonly children: React.ReactNode;
}) {
  return (
    <main className="h-screen flex flex-row">
      <div className="relative hidden bg-muted lg:flex max-w-[533px] flex-1">
        <NextImage
          src={
            process.env.NEXT_PUBLIC_URL
              ? `${process.env.NEXT_PUBLIC_URL}/images/onboarding-image.png`
              : '/images/onboarding-image.png'
          }
          alt="Onboarding Image"
          fill
          className="absolute inset-0 object-contain dark:brightness-[0.2] dark:grayscale"
        />
        <Link
          href="/"
          className="absolute h-full top-8 left-8 max-h-[86px] w-full max-w-[205px] text-white"
        >
          <Image
            src={'/svgs/logo.svg'}
            alt="Popla Logo"
            width={114}
            height={48}
            className="dark:invert"
          />
        </Link>
        <H2 className="absolute bottom-8 left-8" weight="bold">
          Find Your Vibe, Anywhere, Anytime
        </H2>
      </div>
      {children}
    </main>
  );
}
