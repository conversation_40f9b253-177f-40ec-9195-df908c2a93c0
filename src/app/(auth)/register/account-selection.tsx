import { useFormContext } from 'react-hook-form';

import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { type FormType as AccountSetupFormType, USER_ROLE_CARDS } from '@/lib';

export default function AccountSelection() {
  const { watch, setValue } = useFormContext<AccountSetupFormType>();

  return (
    <div className="mt-4 gap-4 flex flex-col">
      {USER_ROLE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={watch('role') === card.id}
          onClick={() => setValue('role', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}
    </div>
  );
}
