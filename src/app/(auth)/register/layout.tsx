'use client';
import { FormProvider } from 'react-hook-form';

import { AccountSetupProvider, useAccountSetupForm } from '@/lib';

export default function AccountCreationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const {
    formMethods,
    usernameRefinement,
    isValidatingUsername,
    setIsValidatingUsername,
    usernameAvailable,
  } = useAccountSetupForm();

  return (
    <AccountSetupProvider
      value={{
        usernameRefinement,
        isValidatingUsername,
        setIsValidatingUsername,
        usernameAvailable,
      }}
    >
      <FormProvider {...formMethods}>{children}</FormProvider>
    </AccountSetupProvider>
  );
}
