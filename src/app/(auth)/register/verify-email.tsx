import React from 'react';
import { type Control, useFormContext } from 'react-hook-form';
import { ActivityIndicator } from '@/components/loaders';
import { toast } from 'react-hot-toast';
import * as z from 'zod';

import { useRequestOtp } from '@/api/auth';
import { colors, ControlledInput, H3, P } from '@/components/ui';
import { type FormType as AccountSetupFormType } from '@/lib';

interface VerifyEmailProps {
  control: Control<FormType>;
}

export const OtpSchema = z.object({
  code: z
    .string({
      error: 'Verification code is required',
    })
    .length(6, 'Verification code must be 6 digits')
    .regex(/^\d{6}$/, { message: 'Code must contain only numbers' }),
});

export type FormType = z.infer<typeof OtpSchema>;

export default function VerifyEmail({ control }: VerifyEmailProps) {
  const { getValues } = useFormContext<AccountSetupFormType>();
  const email = getValues('email');
  const { mutate: requestOtp, isPending: isRequesting } = useRequestOtp();

  const onResendOtp = () =>
    requestOtp(
      { email },
      {
        onSuccess: () => {
          toast.success('OTP resent successfully', {
            position: 'top-center',
          });
        },
        onError: (error) => toast.error(error.message),
      }
    );

  return (
    <div className="flex-1 justify-start">
      <div className="flex flex-col gap-[17px]">
        <H3>Ok, check your inbox!{'\n'}we sent you a verification code.</H3>
        <ControlledInput
          control={control}
          name="code"
          label="Verification code"
        />
        <P className="text-grey-50 dark:text-grey-60">
          Didn’t receive it?{' '}
          <button onClick={onResendOtp}>
            {isRequesting ? (
              <ActivityIndicator className="mt-5" color={colors.brand['60']} />
            ) : (
              <P className="text-brand-60 dark:text-brand-60">Tap to resend</P>
            )}
          </button>
        </P>
      </div>
    </div>
  );
}
