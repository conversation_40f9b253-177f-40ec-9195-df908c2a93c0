'use client';
import { useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

import {
  useCreateEventAccessCodes,
  useGetEventAccessCodes,
} from '@/api/events';
import { AccessCodeListItem } from '@/components/events/access/list-item';
import { BareLayout } from '@/components/layouts/bare-layout';
import { LoadingScreen } from '@/components/loaders';
import { Counter } from '@/components/ui';
import { EmptyState } from '@/components/ui';
import {
  Button,
  ChevronLeft,
  H5,
  LgBoldLabel,
  Modal,
  Small,
  XsBoldLabel,
} from '@/components/ui';
import { cn } from '@/lib';
import { usePurchaseEventTicket } from '@/lib';

export default function CreateAccessCode() {
  const { eventId } = usePurchaseEventTicket();

  const queryClient = useQueryClient();
  const [modal, setModal] = useState(false);

  const { isPending: accessCodesPending, data: accessCodes } =
    useGetEventAccessCodes({
      variables: { eventId },
    });

  const { isPending: isGeneratingCodes, mutate: generateAccessCodes } =
    useCreateEventAccessCodes();

  const [selectedTab, setSelectedTab] = React.useState<'CODES' | 'USED'>(
    'CODES'
  );
  const [accessCodeNumber, setAccessCodeNumber] = React.useState(20);

  const { unusedCodes, usedCodes } = React.useMemo(() => {
    const unused = accessCodes?.filter((code) => !code.isUsed) ?? [];
    const used = accessCodes?.filter((code) => code.isUsed) ?? [];
    return { unusedCodes: unused, usedCodes: used };
  }, [accessCodes]);

  const dataToRender = selectedTab === 'CODES' ? unusedCodes : usedCodes;

  const handleGenerate = () => {
    generateAccessCodes(
      { eventId, quantity: accessCodeNumber },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ['getEventAccessCodes', { eventId }],
          });
          setModal(false);
          toast.success('Access codes generated Successfully');
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  if (accessCodesPending) return <LoadingScreen />;

  return (
    <BareLayout
      title="Access code"
      subTitle="Manage access to your event"
      contentClassName="py-0"
      footer={<Button label="Generate code" onPress={() => setModal(true)} />}
    >
      {/* Tab */}
      <div className="h-12 w-full flex flex-row items-center rounded-full bg-grey-10 p-0.5 dark:bg-grey-90">
        <button
          className={cn(
            'flex text-grey-50 dark:text-grey-60 flex-1 items-center bg-transparent h-full justify-center',
            selectedTab === 'CODES' && 'bg-white dark:bg-grey-80 rounded-full'
          )}
          onClick={() => setSelectedTab('CODES')}
        >
          <XsBoldLabel
            className={cn(
              'text-grey-50 dark:text-grey-60',
              selectedTab === 'CODES' && 'text-brand-60 dark:text-brand-50'
            )}
          >
            Codes
          </XsBoldLabel>
        </button>
        <button
          className={cn(
            'flex text-fg-subtle-light dark:text-fg-subtle-dark flex-1 items-center bg-transparent h-full justify-center',
            selectedTab === 'USED' && 'bg-white dark:bg-grey-80 rounded-full'
          )}
          onClick={() => setSelectedTab('USED')}
        >
          <XsBoldLabel
            className={cn(
              'text-grey-50 dark:text-grey-60',
              selectedTab === 'USED' && 'text-brand-60 dark:text-brand-50'
            )}
          >
            Used codes
          </XsBoldLabel>
        </button>
      </div>

      {/* Tab View */}
      <div className="flex-1">
        {dataToRender && dataToRender.length > 0 ? (
          dataToRender.map((item, index) => (
            <React.Fragment key={item?.id?.toString() ?? index}>
              <AccessCodeListItem {...item} />
              {index < dataToRender.length - 1 && <div className="size-3" />}
            </React.Fragment>
          ))
        ) : (
          <EmptyState />
        )}
      </div>

      {/* Generate access code modal */}
      <Modal isOpen={modal} onClose={() => setModal(false)}>
        <div className="min-h-60">
          <div className="h-16 flex flex-row items-center gap-2 px-2">
            <ChevronLeft onClick={() => setModal(false)} />
            <LgBoldLabel>Generate code</LgBoldLabel>
          </div>

          <div className="gap-6 p-4">
            <div className="flex flex-row items-center justify-between gap-2">
              <div className="gap-1">
                <H5>Enter number of code</H5>
                <Small className="text-fg-muted-light dark:text-fg-muted-dark">
                  Create up to 20 codes each time
                </Small>
              </div>

              <Counter
                initialValue={20}
                minimum={1}
                maximum={20}
                onValueChange={(quantity) => setAccessCodeNumber(quantity)}
                className="border border-border-subtle-light dark:border-border-subtle-dark"
              />
            </div>

            <Button
              label="Create"
              onPress={handleGenerate}
              loading={isGeneratingCodes}
              disabled={isGeneratingCodes}
            />
          </div>
        </div>
      </Modal>
    </BareLayout>
  );
}
