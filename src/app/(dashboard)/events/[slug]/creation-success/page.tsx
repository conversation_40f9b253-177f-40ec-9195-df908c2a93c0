'use client';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

import { ShareModal } from '@/components/modals';
import { Button, H2, H4, Image } from '@/components/ui';
import { APP_URL } from '@/lib/constants';

export default function EventCreatedSuccess() {
  const router = useRouter();
  const params = useParams();
  const slug = params.slug;
  const [shareVisible, setShareModalVisible] = useState(false);
  if (!slug) {
    return null;
  }
  return (
    <div className="flex-1 bg-bg-canvas-light dark:bg-bg-canvas-dark">
      <div className="flex-1 flex justify-center">
        <div className="flex flex-col gap-[54px] p-4">
          <div className=" flex items-center justify-center flex-col gap-6 pt-10">
            <Image
              src={'/images/success.png'}
              className="size-[120px] self-center"
              alt=""
              width={120}
              height={120}
            />
            <div className="flex flex-col gap-2">
              <H2 className="text-center">Event created</H2>
              <H4 className="text-center">
                You will be notified when it goes live.
              </H4>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-auto gap-2 px-4 flex flex-col justify-center items-center">
        <Button
          label="My Events"
          variant="secondary"
          className="max-w-md"
          onPress={() => router.replace('/my-events')}
        />
      </div>
      <ShareModal
        isOpen={shareVisible}
        onDismiss={() => setShareModalVisible(false)}
        content={`${APP_URL}/events/${slug}`}
      />
    </div>
  );
}
