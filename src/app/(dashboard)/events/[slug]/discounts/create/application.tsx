'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';

import { BareLayout } from '@/components/layouts/bare-layout';
import { Button, P, RadioGroup } from '@/components/ui';
import { Checkbox } from '@/components/ui/checkbox';
import {
  cn,
  type CreateDiscountsFormType,
  usePurchaseTicketContext,
} from '@/lib';
import { type StepProps } from './page';
import { RadioGroupOption } from '@/components/ui/radio-group-option';

export default function CreateDiscountsApplication({ setStep }: StepProps) {
  const { watch, setValue } = useFormContext<CreateDiscountsFormType>();

  const { event } = usePurchaseTicketContext();

  const ticketCategories = event?.ticketCategories || {};

  const eventTickets = Object.entries(ticketCategories).map(
    ([category, { id }]) => ({
      id,
      category,
    })
  );

  const selectedTickets = watch('tickets');

  const isForAll = watch('isForAll');

  const handleApplicationTypeChange = (value: string) => {
    if (value === 'all') {
      setValue('isForAll', true);
    } else {
      setValue('isForAll', false);
    }
  };

  return (
    <BareLayout
      title="Discount application"
      subTitle="You can apply the discount to all tickets or specific tickets."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            onPress={() => setStep(0)}
          />
          <Button
            label="Next"
            size="sm"
            disabled={isForAll ? false : !(watch('tickets')?.length > 0)}
            onPress={() => setStep(2)}
          />
        </div>
      }
    >
      <RadioGroup
        value={isForAll ? 'all' : 'selected'}
        onChange={handleApplicationTypeChange}
        className="flex flex-col gap-4 py-4"
      >
        <div className="flex flex-col gap-4">
          <RadioGroupOption
            value="all"
            currentValue={isForAll ? 'all' : 'selected'}
            title="Apply to all tickets"
            onPress={() => {
              handleApplicationTypeChange('all');
            }}
            labelClassName={cn(
              !isForAll && 'text-fg-muted-light dark:text-fg-muted-dark'
            )}
          />
          <div className="rounded-md bg-bg-subtle-light dark:bg-bg-subtle-dark">
            <RadioGroupOption
              value="selected"
              onPress={() => {
                handleApplicationTypeChange('selected');
              }}
              currentValue={isForAll ? 'all' : 'selected'}
              title="Apply to specific ticket(s)"
              labelClassName={cn(
                isForAll && 'text-fg-muted-light dark:text-fg-muted-dark'
              )}
            />
            {!isForAll && (
              <>
                {!isForAll && (
                  <div className="flex flex-col gap-2 rounded-md p-4">
                    {eventTickets.map((ticket) => {
                      const isChecked = selectedTickets?.includes(
                        ticket.category
                      );

                      return (
                        <div
                          key={ticket.id}
                          className="flex flex-row items-center justify-between rounded-md border border-border-subtle-light p-4 dark:border-border-subtle-dark"
                        >
                          <P>{ticket.category}</P>
                          <Checkbox
                            checked={isChecked}
                            onChange={() => {
                              let updatedTickets = [...(selectedTickets || [])];

                              if (isChecked) {
                                updatedTickets = updatedTickets.filter(
                                  (category) => category !== ticket.category
                                );
                              } else {
                                updatedTickets.push(ticket.category);
                              }

                              setValue('tickets', updatedTickets);
                            }}
                            accessibilityLabel="Select ticket"
                          />
                        </div>
                      );
                    })}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </RadioGroup>
    </BareLayout>
  );
}
