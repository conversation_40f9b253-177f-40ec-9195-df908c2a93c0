'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';

import { BareLayout } from '@/components/layouts';
import { Button, ControlledInput, ControlledSelect } from '@/components/ui';
import { type CreateDiscountsFormType, useFieldBlurAndFilled } from '@/lib';
import { type StepProps } from './page';

export default function CreateDiscountsCodes({ setStep }: StepProps) {
  const { watch, control, setValue } =
    useFormContext<CreateDiscountsFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateDiscountsFormType>([
    'code',
    'value',
  ]);

  // const [_codeEnabled, _setCodeEnabled] = useState(false);

  // const handleCodeStateToggle = (checked: boolean) => {
  //   if (checked) {
  //     setCodeEnabled(true);
  //     setValue(`isEnabled`, true);
  //   } else {
  //     setCodeEnabled(false);
  //     setValue(`isEnabled`, false);
  //   }
  // };

  return (
    <BareLayout
      title="Discount code"
      subTitle="You can create single or multiple discount codes."
      footer={
        <Button
          label="Next"
          size="sm"
          disabled={
            !fieldStates.code.isValid ||
            !fieldStates.value.isValid ||
            !watch('type') ||
            (watch('type') === 'percentage' && watch('value') > 100)
          }
          onPress={() => setStep(1)}
        />
      }
    >
      <div className="flex flex-col gap-4">
        {/* <FeatureToggle
          title="Enable discount code"
          checked={codeEnabled}
          onChange={handleCodeStateToggle}
          accessibilityLabel="Offer ticket presale"
        /> */}
        <ControlledInput
          name="code"
          label="Enter code e.g., EARLYBIRD, VIP10"
          control={control}
        />
        <ControlledSelect
          name="type"
          placeholder="Select discount type"
          control={control}
          options={[
            { label: 'Percentage', value: 'percentage' },
            { label: 'Fixed amount', value: 'fixed' },
          ]}
        />
        <ControlledInput
          name="value"
          label={
            watch('type') === 'fixed'
              ? 'Discount amount e.g. 2000'
              : 'Discount Percentage (0-100)'
          }
          control={control}
          type="number"
          onChange={(e) => {
            let val = Number(e.target.value);
            if (watch('type') === 'percentage') {
              val = Math.min(100, val);
            }
            setValue('value', val);
          }}
        />
      </div>
    </BareLayout>
  );
}
