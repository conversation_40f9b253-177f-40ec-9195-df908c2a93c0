'use client';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { toast } from 'react-hot-toast';

import { DiscountType, useCreateEventDiscount } from '@/api/events';
import { BareLayout } from '@/components/layouts/bare-layout';
import { Button, ControlledInput, DateTimePicker } from '@/components/ui';
import { type CreateDiscountsFormType, usePurchaseTicketContext } from '@/lib';
import { type StepProps } from './page';
import dayjs from 'dayjs';

export default function CreateDiscountsLimits({ setStep }: StepProps) {
  const queryClient = useQueryClient();
  const { watch, control, setValue } =
    useFormContext<CreateDiscountsFormType>();
  const router = useRouter();

  const { event, eventId } = usePurchaseTicketContext();

  // const [_codeEnabled, _setCodeEnabled] = useState(false);

  // const handleCodeStateToggle = (checked: boolean) => {
  //   if (checked) {
  //     setCodeEnabled(true);
  //     setValue(`isEnabled`, true);
  //   } else {
  //     setCodeEnabled(false);
  //     setValue(`isEnabled`, false);
  //   }
  // };

  const startDatetime = watch(`startDatetime`);

  const startDatetimeLabel = startDatetime
    ? startDatetime.toLocaleString()
    : 'Start date and time (optional)';

  const endDatetime = watch(`endDatetime`);

  const endDatetimeLabel = endDatetime
    ? endDatetime.toLocaleString()
    : 'End date and time (optional)';

  const { isPending, mutate: createEventDiscount } = useCreateEventDiscount();

  const onSubmit = () => {
    const form = watch();
    const { endDatetime, startDatetime, limit, isForAll } = form;
    const payload = {
      discountType:
        form.type === 'fixed' ? DiscountType.AMOUNT : DiscountType.PERCENTAGE,
      eventId: event?.id || eventId,
      discountValue: Number(form.value),
      applicableTicketCategories: isForAll ? undefined : form.tickets,
      code: form.code,
      name: form.code,
      ...(startDatetime && {
        startDateTime: dayjs(startDatetime).format('YYYY-MM-DD'),
      }),
      ...(endDatetime && {
        endDateTime: dayjs(endDatetime).format('YYYY-MM-DD'),
      }),
      ...(limit && { maxUsage: Number(limit) }),
    };
    createEventDiscount(payload, {
      onSuccess: () => {
        toast.success('Discount created successfully');
        queryClient.invalidateQueries({ queryKey: ['getEventDiscounts'] });
        router.replace(`/events/${event?.slug}/discounts`);
      },
      onError: (error) => toast.error(error.message),
    });
  };

  return (
    <BareLayout
      title="Discount limits"
      subTitle="You can create single or multiple discount codes."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            onPress={() => setStep(1)}
          />
          <Button
            label="Create"
            size="sm"
            loading={isPending}
            disabled={isPending}
            onPress={onSubmit}
          />
        </div>
      }
    >
      <div className="flex flex-col gap-4">
        <ControlledInput
          name="limit"
          label="Code usage limit (optional)"
          control={control}
          onChange={(limit) => setValue('limit', Number(limit))}
        />
        <DateTimePicker
          selected={startDatetime}
          onChange={(date) => {
            setValue(`startDatetime`, date);
          }}
          minDate={new Date()}
          label={startDatetimeLabel}
        />

        <DateTimePicker
          selected={endDatetime}
          onChange={(date) => {
            setValue(`endDatetime`, date);
          }}
          minDate={startDatetime}
          label={endDatetimeLabel}
        />
      </div>
    </BareLayout>
  );
}
