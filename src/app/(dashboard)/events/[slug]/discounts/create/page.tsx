'use client';
import { useState } from 'react';
import Application from './application';
import Limits from './limits';
import Codes from './codes';

const steps = [Codes, Application, Limits];

export type StepProps = {
  setStep: (step: number) => void;
  step?: number;
};

export default function CreateDiscountsPage() {
  const [step, setStep] = useState(0);

  const goToStep = (step: number) => {
    setStep(step);
  };
  const StepComponent = steps[step];
  return <StepComponent setStep={goToStep} step={step} />;
}
