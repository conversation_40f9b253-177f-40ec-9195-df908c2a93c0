'use client';
import { useQueryClient } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

import {
  type EventDiscount,
  useDeleteEventDiscount,
  useGetEventDiscounts,
} from '@/api/events';
import { ConfirmationDialog } from '@/components/dialogs';
import { EventDiscountMoreDropdown } from '@/components/events/discount/more-dropdown';
import { BareLayout } from '@/components/layouts';
import { LoadingScreen } from '@/components/loaders';
import { Button, Image } from '@/components/ui';

export default function CreateDiscountsIndex() {
  const router = useRouter();
  const params = useParams<{ slug: string }>();
  const slug = params.slug;
  const queryClient = useQueryClient();

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [selectedDiscount, setSelectedDiscount] =
    useState<EventDiscount | null>(null);

  const { mutate: deleteDiscount } = useDeleteEventDiscount({
    onSuccess: (_, variables) => {
      queryClient.setQueryData<EventDiscount[]>(
        useGetEventDiscounts.getKey({ eventId: slug }),
        (oldData) => oldData?.filter((d) => d.id !== variables.discountId) ?? []
      );
      toast.success('Discount deleted successfully');
    },
    onError: (error) => toast.error(error.message),
    onSettled: () => {
      setConfirmVisible(false);
      setSelectedDiscount(null);
    },
  });

  const { isPending, data: discounts } = useGetEventDiscounts({
    variables: { eventId: slug },
  });

  if (isPending) return <LoadingScreen />;

  return (
    <BareLayout
      title="Discount code"
      subTitle="You can create single or multiple discount codes."
      footer={
        <Button
          label="Go back to event page"
          onPress={() => router.push(`/events/${slug}`)}
        />
      }
    >
      <div className="gap-4">
        {/* <FeatureToggle
          title="Enable discount code"
          checked={codeEnabled}
          onChange={handleCodeStateToggle}
          accessibilityLabel="Offer ticket presale"
        /> */}

        <div className="flex flex-col gap-4">
          {discounts?.map((discount) => (
            <div
              key={discount.id}
              className="relative flex flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark"
            >
              <div className="flex-1 flex flex-row items-center gap-2">
                <Image
                  src="/icons/events/discount.png"
                  className="size-10"
                  width={40}
                  height={40}
                  alt="Ticket"
                />
                <div className="flex flex-col gap-2">
                  <p className="font-bold text-fg-base-light dark:text-fg-base-dark">
                    {discount.name || discount.code}
                  </p>
                </div>
              </div>
              <EventDiscountMoreDropdown
                onValueChange={(value) => {
                  if (value === 'delete') {
                    setSelectedDiscount(discount);
                    setConfirmVisible(true);
                  }
                }}
                triggerClassName="size-6 bg-transparent dark:bg-transparent border-transparent"
                itemClassName="flex px-4 py-2.5 justify-between h-11"
              />
            </div>
          ))}

          <Button
            label="Add discount code +"
            variant="secondary"
            className="w-[180px] py-2"
            size="sm"
            onPress={() => router.push(`/events/${slug}/discounts/create`)}
          />
        </div>
      </div>

      <ConfirmationDialog
        visible={confirmVisible}
        message={`Are you sure you want to delete ${
          selectedDiscount?.name || 'this discount'
        }?`}
        onCancel={() => {
          setConfirmVisible(false);
          setSelectedDiscount(null);
        }}
        onConfirm={() => {
          if (selectedDiscount) {
            deleteDiscount({
              eventId: slug,
              discountId: selectedDiscount.id,
            });
          }
        }}
        cancelLabel="Dismiss"
      />
    </BareLayout>
  );
}
