'use client';
import { FormProvider } from 'react-hook-form';
import { PurchaseTicketProvider, usePurchaseEventTicket } from '@/lib';

export default function EventLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const {
    event,
    categories,
    isEventLoading,
    selectedTickets,
    handleTicketQuantityChange,
    getSelectedTicketsArray,
    hasSelectedTickets,
    formMethods,
    resetForm,
    calculateTotalCost,
    getTotalQuantity,
    eventId,
    editType,
    setEditType,
    activeDiscount,
    setActiveDiscount,
    accessCodeVerified,
    isValidatingAccessCode,
    validateAccessCode,
  } = usePurchaseEventTicket();

  return (
    <PurchaseTicketProvider
      value={{
        event,
        categories,
        isEventLoading,
        selectedTickets,
        handleTicketQuantityChange,
        getSelectedTicketsArray,
        hasSelectedTickets,
        resetForm,
        calculateTotalCost,
        getTotalQuantity,
        eventId,
        setEditType,
        editType,
        activeDiscount,
        setActiveDiscount,
        accessCodeVerified,
        isValidatingAccessCode,
        validateAccessCode,
      }}
    >
      <FormProvider {...formMethods}>{children}</FormProvider>
    </PurchaseTicketProvider>
  );
}
