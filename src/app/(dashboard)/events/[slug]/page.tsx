import { EventDetailsPage } from '@/components/events';
import type { Metadata } from 'next';
import { apiGetEventDetails } from '@/api';

type Props = {
  params: { slug: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  try {
    const { data } = await apiGetEventDetails({ slug });
    const event = data.data;

    if (!event) {
      return {};
    }

    return {
      title: `${event.title} - Popla`,
      description: event.description,
      openGraph: {
        title: event.title,
        description: event.description,
        url: `${process.env.NEXT_PUBLIC_APP_BASE_URL}/events/${slug}`,
        images: [
          {
            url: event.bannerUrl,
            width: 800,
            height: 600,
            alt: `${event.title} Image`,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: event.title,
        description: event.description,
        images: [event.bannerUrl],
      },
    };
  } catch (error) {
    console.log(error);
    return {};
  }
}

export default async function EventSlugPage({
  params: { slug },
}: {
  params: { slug: string };
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
}) {
  try {
    const { data } = await apiGetEventDetails({
      slug,
    });
    const event = data.data;
    if (!event) {
      return (
        <div className="flex flex-1 justify-center items-center">
          Event not found
        </div>
      );
    }
    return <EventDetailsPage slug={slug} />;
  } catch (error) {
    console.log(error);
    return (
      <div className="flex flex-1 justify-center items-center">
        Error loading data
      </div>
    );
  }
}
