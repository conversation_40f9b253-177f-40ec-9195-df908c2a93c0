'use client';

import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTheme } from 'next-themes';
import { toast } from 'react-hot-toast';

import {
  type TicketVerificationError,
  useGetEventTicket,
  useVerifyTicket,
} from '@/api/events';
import { BareLayout } from '@/components/layouts';
import { LoadingScreen } from '@/components/loaders';
import {
  Button,
  colors,
  H2,
  Image,
  MdBoldLabel,
  Modal,
  P,
} from '@/components/ui';

import { IoCameraOutline, IoLockClosed } from 'react-icons/io5';

export default function ScanTicket() {
  const { resolvedTheme } = useTheme();
  const params = useSearchParams();

  const eventId = params.get('id') || '';
  const isDark = resolvedTheme === 'dark';

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [stream, setStream] = useState<MediaStream | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [permission, setPermission] = useState<'granted' | 'denied' | 'prompt'>(
    'prompt'
  );

  useEffect(() => {
    async function requestCamera() {
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: 'environment' },
        });
        setStream(mediaStream);
        setPermission('granted');
        if (videoRef.current) videoRef.current.srcObject = mediaStream;
      } catch {
        setPermission('denied');
      }
    }

    requestCamera();
  }, []);
  const handleBarcodeScanned = ({ data }: { data: string }) => {
    if (scanned) return;

    setScanned(true);
    setScannedTicketId(data);
    setModalVisible(true);
  };
  useEffect(() => {
    if (!videoRef.current || permission !== 'granted') return;

    if ('BarcodeDetector' in window) {
      const detector = new (window as any).BarcodeDetector({
        formats: ['qr_code', 'ean_13', 'code_128'],
      });

      const scan = async () => {
        if (!videoRef.current) return;
        const barcodes = await detector.detect(videoRef.current);
        if (barcodes.length > 0) {
          handleBarcodeScanned({ data: barcodes[0].rawValue });
        }
        requestAnimationFrame(scan);
      };
      scan();
    } else {
      console.warn('BarcodeDetector API not supported in this browser.');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [permission]);

  const [scanned, setScanned] = useState(false);
  const [scannedTicketId, setScannedTicketId] = useState<string>('');

  const {
    data: ticket,
    error,
    isError,
    isSuccess,
    isLoading: isVerifyingTicket,
  } = useVerifyTicket({
    variables: {
      eventId,
      ticketId: scannedTicketId,
    },
    enabled: !!scannedTicketId,
    retry: false,
  });

  const { data: ticketDetails } = useGetEventTicket({
    variables: {
      eventId,
      ticketId: scannedTicketId,
    },
    enabled: !!scannedTicketId && isSuccess,
  });

  const isFree = ticketDetails?.isFree;
  const fullName = ticket?.user?.fullName || 'N/A';
  const userFriendlyTicketId = ticketDetails?.userFriendlyTicketId || 'N/A';
  const grade = isFree
    ? 'Free'
    : Object.keys(ticket?.breakdown || {})[0] || 'N/A';
  const quantity = isFree ? 1 : ticket?.breakdown?.[grade] || 'N/A';
  let ticketStatus: TicketVerificationError['status'] | 'VALID' = 'INVALID';

  if (isSuccess) {
    const { isUsed } = ticket;
    ticketStatus = isUsed ? 'USED' : 'VALID';
  }
  if (isError) {
    const { status } = error;
    ticketStatus = status;
  }

  useEffect(() => {
    if (!permission) {
      setPermission('prompt');
    }
  }, [permission]);

  const [modalVisible, setModalVisible] = useState(false);

  const resetScanner = () => {
    setScanned(false);
    setScannedTicketId('');
    setModalVisible(false);
  };

  const getStatusTitle = () => {
    switch (ticketStatus) {
      case 'VALID':
        return 'Valid';
      case 'USED':
        return 'Used';
      case 'INVALID':
        return 'Invalid';
      case 'UNKNOWN':
        return 'EXPIRED';
      default:
        return 'Invalid';
    }
  };

  const getStatusDescription = () => {
    switch (ticketStatus) {
      case 'VALID':
        return 'This ticket is valid for your event';
      case 'USED':
        return 'This ticket has already been used';
      case 'INVALID':
        return 'This ticket is not valid';
      case 'UNKNOWN':
        return 'This ticket was not found';
      default:
        return 'This ticket is invalid';
    }
  };

  if (!permission || permission === 'prompt') {
    return (
      <BareLayout
        title="Scan ticket"
        subTitle="Verify ticket and attendee information."
      >
        <div className="flex-1 flex items-center justify-center">
          <P className="text-center">Requesting camera permission...</P>
        </div>
      </BareLayout>
    );
  }

  if (permission === 'denied') {
    return (
      <BareLayout
        title="Scan ticket"
        subTitle="Verify ticket and attendee information."
      >
        <div className="flex-1 flex flex-col items-center justify-center gap-4 p-4">
          <IoCameraOutline
            size={64}
            color={isDark ? colors.white : colors.grey[100]}
          />
          <P className="text-center">
            Camera permission is required to scan tickets
          </P>
          <Button
            label="Grant Permission"
            onPress={() => setPermission('prompt')}
          />
          <>
            <P className="text-center text-fg-muted-light dark:text-fg-muted-dark">
              Camera permission was denied. Please enable it manually in your
              device settings.
            </P>
            <Button
              variant="secondary"
              label="Open Settings"
              onPress={() => {
                toast.error(
                  'Permission to access camera was denied. To use this feature, please enable camera access in your settings.'
                );
              }}
            />
          </>
        </div>
      </BareLayout>
    );
  }

  return (
    <BareLayout
      title="Scan ticket"
      subTitle="Verify ticket and attendee information."
    >
      {permission === 'granted' && (
        <video
          ref={videoRef}
          style={{ width: '100%', height: 488, objectFit: 'cover' }}
          autoPlay
          muted
        />
      )}

      <div className="flex flex-row items-start gap-1 px-4">
        <IoLockClosed
          size={20}
          color={isDark ? colors.white : colors.grey[100]}
          onClick={() => setModalVisible(true)}
        />
        <P className="flex-1 text-fg-muted-light dark:text-fg-muted-dark">
          Your privacy is important to us. This QR scan is 256-bit encrypted,
          ensuring the security of your personal data.
        </P>
      </div>

      <Modal isOpen={modalVisible} onClose={resetScanner}>
        <div className="min-h-[555px] flex-1 gap-6 p-4">
          <H2>Ticket information</H2>

          {isVerifyingTicket ? (
            <LoadingScreen />
          ) : (
            <>
              <div className="gap-6">
                <div className="items-center">
                  <Image
                    src={
                      ticketStatus === 'VALID'
                        ? '/images/success.png'
                        : '/images/invalid.png'
                    }
                    className="h-52 w-[211px]"
                    alt="Ticket status"
                  />
                  <div className="items-center gap-2">
                    <H2>{getStatusTitle()}</H2>
                    <P>{getStatusDescription()}</P>
                  </div>
                </div>
                <div className="gap-6">
                  <div className="flex-row justify-between">
                    <div className="flex-1 gap-1">
                      <MdBoldLabel>Full name</MdBoldLabel>
                      <P className="text-fg-muted-light dark:text-fg-muted-dark">
                        {fullName}
                      </P>
                    </div>
                    <div className="flex-1 gap-1">
                      <MdBoldLabel>Ticket ID</MdBoldLabel>
                      <P className="text-fg-muted-light dark:text-fg-muted-dark">
                        {userFriendlyTicketId}
                      </P>
                    </div>
                  </div>
                  <div className="flex-row justify-between">
                    <div className="flex-1 gap-1">
                      <MdBoldLabel>Ticket grade</MdBoldLabel>
                      <P className="text-fg-muted-light dark:text-fg-muted-dark">
                        {grade}
                      </P>
                    </div>
                    <div className="flex-1 gap-1">
                      <MdBoldLabel>Quantity</MdBoldLabel>
                      <P className="text-fg-muted-light dark:text-fg-muted-dark">
                        {quantity}
                      </P>
                    </div>
                  </div>
                </div>
              </div>

              <Button variant="secondary" label="Done" onPress={resetScanner} />
            </>
          )}
        </div>
      </Modal>
    </BareLayout>
  );
}
