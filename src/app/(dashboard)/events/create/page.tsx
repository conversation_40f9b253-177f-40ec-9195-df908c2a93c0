'use client';

import { useState } from 'react';
import AddType from './steps/add-type';
import AddDetails from './steps/add-details';
import AddBanner from './steps/add-banner';
import ChooseFormat from './steps/choose-format';
import AddAddress from './steps/add-address';
import AddUrl from './steps/add-url';
import Hybrid from './steps/hybrid';
import ChooseTicketType from './steps/choose-ticket-type';
import FreeEventDetails from './steps/free-event';
import FreeEventDetailsNext from './steps/free-event-next';
import AddTicket from './steps/add-ticket';
import Tickets from './steps/tickets';
import EditTicket from './steps/edit-ticket';
import Summary from './steps/summary';
import AddCollaborators from './steps/add-collaborators';

const steps = [
  AddType, //0
  AddDetails, //1
  AddBanner, //2
  ChooseFormat, //3
  AddAddress, //4
  AddUrl, //5
  Hybrid, //6
  ChooseTicketType, //7
  FreeEventDetails, //8
  FreeEventDetailsNext, //9
  AddTicket, //10
  Tickets, //11
  EditTicket, //12
  Summary, //13
  AddCollaborators, //14
];
export type StepProps = {
  setStep: (next: number, customFromStep?: number, ticketId?: string) => void;
  ticketId?: string;
  step?: number;
  fromStep?: number;
};

export default function CreateEventPage() {
  const [step, setStep] = useState(0);

  const [fromStep, setFromStep] = useState<number | undefined>();

  const [ticketId, setTicketId] = useState<string | undefined>(undefined);

  const goToStep = (
    next: number,
    customFromStep: number | undefined = undefined,
    ticketId?: string | undefined
  ) => {
    setStep(next);
    setFromStep(customFromStep);
    setTicketId(ticketId);
  };

  const StepComponent = steps[step];

  return (
    <StepComponent
      step={step}
      setStep={goToStep}
      ticketId={ticketId}
      fromStep={fromStep}
    />
  );
}
