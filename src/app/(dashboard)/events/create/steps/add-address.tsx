'use client';

import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';

import { LocationInputType, LocationInput } from '@/components/ui';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { type CreateEventFormType, useFieldBlurAndFilled } from '@/lib';

import { type StepProps } from '../page';
import { Button } from '@/components/ui';

export default function AddAddress({ setStep, fromStep }: StepProps) {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'location',
  ]);

  const onSelectLocation = useCallback(
    (
      location: LocationInputType | null,
      coordinates: { lat: number; lng: number } | null
    ) => {
      if (location && coordinates) {
        const locationObject = {
          ...location,
          coordinates: { lat: coordinates.lat, lng: coordinates.lng },
        };
        setValue('location', locationObject);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <CreateEventLayout
      title="Add event address"
      subTitle="Where will your event be taking place?"
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            disabled={fromStep === 13}
            onPress={() => setStep(fromStep || 3)}
          />
          <Button
            label="Continue"
            size="sm"
            disabled={!fieldStates.location.isValid}
            onPress={() => setStep(fromStep === 13 ? 13 : 7, 4)}
          />
        </div>
      }
    >
      <LocationInput
        onSelectLocation={onSelectLocation}
        defaultValue={watch('location')}
      />
    </CreateEventLayout>
  );
}
