'use client';
import { useState, useCallback, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import ReactCrop, { type Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { useDropzone } from 'react-dropzone';

import { Button, colors, Image, semanticColors } from '@/components/ui';
import type { CreateEventFormType } from '@/lib/hooks';
import { CreateEventLayout } from '@/components/layouts';
import { cn } from '@/lib';
import { type StepProps } from '../page';
import { useTheme } from 'next-themes';
import { IoCameraOutline, IoClose } from 'react-icons/io5';

type BannerData = {
  uri: string;
  file: File;
  type?: string;
  name?: string | null;
  size?: number;
};

async function getCroppedImg(
  image: HTMLImageElement,
  crop: Crop,
  mime: string = 'image/jpeg',
  quality: number = 1
): Promise<File | null> {
  if (!crop.width || !crop.height) return null;

  const canvas = document.createElement('canvas');
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  canvas.width = crop.width;
  canvas.height = crop.height;

  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  ctx.drawImage(
    image,
    crop.x! * scaleX,
    crop.y! * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width,
    crop.height
  );

  return new Promise((resolve) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) return resolve(null);
        const file = new File(
          [blob],
          `cropped-${Date.now()}.${mime.split('/')[1]}`,
          { type: mime }
        );
        resolve(file);
      },
      mime,
      quality
    );
  });
}

export default function AddBanner({ setStep, fromStep }: StepProps) {
  const { watch, setValue } = useFormContext<CreateEventFormType>();

  const bannerObj = watch('bannerObj');
  const currentMedia = watch('media') || [];

  const MAX_MEDIA_COUNT = 2;

  const [cropQueue, setCropQueue] = useState<File[]>([]);
  const [currentCropImage, setCurrentCropImage] = useState<File | null>(null);
  const [isCropping, setIsCropping] = useState(false);
  const [isBannerCrop, setIsBannerCrop] = useState(false);
  const [crop, setCrop] = useState<Crop>({
    unit: 'px',
    x: 0,
    y: 0,
    width: 480,
    height: 360,
  });

  const imageRef = useRef<HTMLImageElement | null>(null);
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const startCropping = useCallback((files: File[], banner: boolean) => {
    if (!files.length) return;
    setIsBannerCrop(banner);
    setCropQueue(files.length > 1 ? files.slice(1) : []);
    setCurrentCropImage(files[0]);
    setIsCropping(true);
  }, []);

  const onBannerDrop = useCallback(
    (acceptedFiles: File[]) => startCropping(acceptedFiles, true),
    [startCropping]
  );

  const onMediaDrop = useCallback(
    (acceptedFiles: File[]) => {
      const remainingSlots = MAX_MEDIA_COUNT - currentMedia.length;
      startCropping(acceptedFiles.slice(0, remainingSlots), false);
    },
    [startCropping, currentMedia.length]
  );

  const saveCroppedImage = useCallback(async () => {
    if (!currentCropImage || !imageRef.current) return;

    const croppedFile = await getCroppedImg(
      imageRef.current,
      crop,
      currentCropImage.type
    );
    if (!croppedFile) return;

    const imageData: BannerData = {
      uri: URL.createObjectURL(croppedFile),
      file: croppedFile,
      name: croppedFile.name,
      size: croppedFile.size,
      type: croppedFile.type,
    };

    if (isBannerCrop) {
      setValue('bannerObj', imageData);
    } else {
      setValue('media', [...currentMedia, imageData]);
    }

    if (currentCropImage) {
      URL.revokeObjectURL(currentCropImage as unknown as string);
    }

    if (cropQueue.length > 0) {
      setCurrentCropImage(cropQueue[0]);
      setCropQueue(cropQueue.slice(1));
    } else {
      setCurrentCropImage(null);
      setIsCropping(false);
    }
  }, [currentCropImage, crop, cropQueue, currentMedia, setValue, isBannerCrop]);

  const cancelCrop = useCallback(() => {
    setCurrentCropImage(null);
    setIsCropping(false);
    setCropQueue([]);
    setCrop({
      unit: 'px',
      x: 0,
      y: 0,
      width: 480,
      height: 360,
    });
  }, []);

  const removeMediaAt = useCallback(
    (index: number) => {
      const newMedia = [...currentMedia];
      newMedia.splice(index, 1);
      setValue('media', newMedia);
    },
    [currentMedia, setValue]
  );

  const continueDisabled = !bannerObj?.uri;

  const {
    getRootProps: getBannerRootProps,
    getInputProps: getBannerInputProps,
    isDragActive: isBannerDragActive,
  } = useDropzone({
    accept: { 'image/*': [] },
    onDrop: onBannerDrop,
    maxFiles: 1,
  });

  const {
    getRootProps: getMediaRootProps,
    getInputProps: getMediaInputProps,
    isDragActive: isMediaDragActive,
  } = useDropzone({
    accept: { 'image/*': [] },
    onDrop: onMediaDrop,
    maxFiles: MAX_MEDIA_COUNT - currentMedia.length,
  });

  return (
    <CreateEventLayout
      title="Add event image"
      subTitle="Add an event image"
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            disabled={fromStep === 13}
            onPress={() => setStep(fromStep || 1)}
          />
          <Button
            label="Continue"
            disabled={continueDisabled}
            size="sm"
            onPress={() => setStep(fromStep === 13 ? 13 : 3, 2)}
          />
        </div>
      }
    >
      {isCropping && currentCropImage ? (
        <div className="flex flex-col gap-4">
          <ReactCrop crop={crop} onChange={(c) => setCrop(c)}>
            <img
              src={URL.createObjectURL(currentCropImage)}
              ref={imageRef}
              alt="Image to crop"
              className="max-w-full h-auto rounded-lg"
            />
          </ReactCrop>
          <div className="flex gap-4">
            <Button label="Add" onClick={saveCroppedImage} />
            <Button label="Cancel" onClick={cancelCrop} />
          </div>
        </div>
      ) : (
        <div className="flex flex-col gap-4">
          {bannerObj?.uri ? (
            <div
              className="relative w-full overflow-hidden rounded-lg"
              {...getBannerRootProps()}
            >
              <input {...getBannerInputProps()} />
              <img
                src={bannerObj.uri}
                alt="Banner"
                className="rounded-lg w-full object-cover"
                style={{ aspectRatio: '4 / 3' }}
              />
              <Button
                className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 font-medium w-auto"
                variant="secondary"
                label="Change"
              />
            </div>
          ) : (
            <div
              {...getBannerRootProps()}
              className={cn(
                'flex flex-col p-6 cursor-pointer h-[360px] w-full items-center justify-center border border-dashed rounded-lg',
                isBannerDragActive &&
                  'border-primary bg-accent-moderate dark:bg-accent-moderate'
              )}
              style={{
                borderWidth: 2,
                borderColor: isDark
                  ? semanticColors.border.subtle.dark
                  : semanticColors.border.subtle.light,
                borderStyle: 'dashed',
                borderRadius: 12,
                backgroundColor: isDark
                  ? semanticColors.bg.subtle.dark
                  : semanticColors.bg.subtle.light,
              }}
            >
              <input {...getBannerInputProps()} />
              <div className="flex flex-col items-center justify-center gap-4">
                <Image
                  src={'/icons/upload.png'}
                  width={64}
                  height={64}
                  alt=""
                />
                <p className="text-center text-fg-base-light dark:text-fg-base-dark">
                  Upload photos of the event image
                </p>
                <div className="flex flex-wrap justify-center">
                  <p className="mx-1 text-xs text-fg-muted-light dark:text-fg-muted-dark">
                    {'Supported Format: JPG, PNG (Max 10mb)'}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-row flex-wrap gap-4 mt-2">
            {currentMedia.map((file, index) => (
              <div
                key={file.name || file.uri}
                className="relative size-20 rounded-md cursor-pointer"
              >
                <img
                  src={file.uri}
                  alt={`Media ${index + 1}`}
                  className="rounded-md size-full object-cover"
                />
                <button
                  type="button"
                  onClick={() => removeMediaAt(index)}
                  className="absolute -right-1 -top-1 z-10 cursor-pointer"
                >
                  <div className="size-6 flex items-center justify-center rounded-full bg-white dark:bg-white">
                    <IoClose size={16} color="#000" />
                  </div>
                </button>
              </div>
            ))}

            {currentMedia.length < MAX_MEDIA_COUNT && bannerObj?.uri && (
              <div
                {...getMediaRootProps()}
                className={cn(
                  'size-20 flex flex-col items-center justify-end gap-y-2 rounded-lg border border-dashed border-fg-subtle-light p-2 dark:border-fg-subtle-dark cursor-pointer',
                  isMediaDragActive &&
                    'border-primary bg-accent-moderate dark:bg-accent-moderate'
                )}
              >
                <input {...getMediaInputProps()} />
                <IoCameraOutline size={16} color={colors.grey[70]} />
                <span className="text-center text-[10px] leading-[120%] text-grey-70">
                  Add {MAX_MEDIA_COUNT - currentMedia.length} more photo
                  {MAX_MEDIA_COUNT - currentMedia.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </CreateEventLayout>
  );
}
