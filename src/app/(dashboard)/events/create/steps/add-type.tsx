'use client';
import { useFormContext } from 'react-hook-form';

import { GradientBorderCard } from '@/components/cards';
import { CreateEventLayout } from '@/components/layouts';
import { Button } from '@/components/ui';
import { type CreateEventFormType, EVENT_TYPE_CARDS } from '@/lib';
import { type StepProps } from '../page';

export default function ChooseEventVisibility({ setStep }: StepProps) {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const eventType = watch('eventType');

  return (
    <CreateEventLayout
      title="What type of event are you hosting?"
      subTitle="Choose whether your event is public or private."
      footer={
        <Button
          label="Continue"
          size="sm"
          disabled={!eventType}
          onPress={() => setStep(1)}
        />
      }
    >
      {EVENT_TYPE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={eventType === card.id}
          onClick={() => setValue('eventType', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}
    </CreateEventLayout>
  );
}
