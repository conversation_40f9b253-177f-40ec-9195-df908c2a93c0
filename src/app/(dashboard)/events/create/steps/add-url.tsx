'use client';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Button, ControlledInput } from '@/components/ui';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { type CreateEventFormType } from '@/lib';
import { type StepProps } from '../page';
import { Checkbox } from '@/components/ui/checkbox';
import { IoLink } from 'react-icons/io5';

export default function AddUrl({ setStep, fromStep }: StepProps) {
  const { control, watch } = useFormContext<CreateEventFormType>();
  const [isSendLater, setIsSendLater] = useState(false);
  const eventUrl = watch('onlineUrl');

  return (
    <CreateEventLayout
      title="Add streaming URL"
      subTitle="Add the streaming URL for your event."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            disabled={fromStep === 13}
            onPress={() => setStep(fromStep || 3)}
          />
          <Button
            label="Continue"
            size="sm"
            disabled={
              !isSendLater &&
              !(
                eventUrl &&
                (() => {
                  try {
                    new URL(eventUrl);
                    return true;
                  } catch {
                    return false;
                  }
                })()
              )
            }
            onPress={() => setStep(fromStep === 13 ? 13 : 7, 5)}
          />
        </div>
      }
    >
      <div className="flex flex-col gap-4">
        <ControlledInput
          name="onlineUrl"
          control={control}
          icon={
            <IoLink size={24} className="text-fg-muted dark:text-fg-muted" />
          }
          disabled={isSendLater}
        />
        <ControlledInput
          name="passcode"
          disabled={isSendLater}
          control={control}
          label="Link passcode (optional)"
        />
        <Checkbox
          label="Send link to attendees later"
          checked={isSendLater}
          className="text-fg-subtle-dark"
          onChange={() => setIsSendLater((prev) => !prev)}
          accessibilityLabel=""
        />
      </div>
    </CreateEventLayout>
  );
}
