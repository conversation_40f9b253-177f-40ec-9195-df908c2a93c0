'use client';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { GradientBorderCard } from '@/components/cards';
import { CreateEventLayout } from '@/components/layouts';
import { Button } from '@/components/ui';
import {
  type CreateEventFormType,
  EVENT_FORMAT_CARDS,
  useFieldBlurAndFilled,
} from '@/lib';

import { type StepProps } from '../page';

export default function ChooseEventFormat({ setStep, fromStep }: StepProps) {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'eventFormat',
  ]);

  const handleContinue = () => {
    const eventType = watch('eventFormat');
    if (eventType === 'ONLINE') {
      setStep(5, 3);
    } else if (eventType === 'HYBRID') {
      setStep(6, 3);
    } else {
      setStep(4, 3);
    }
  };

  return (
    <CreateEventLayout
      title="How do you want to host this event?"
      subTitle="Choose the format that suits your event."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            onPress={() => setStep(fromStep || 2)}
          />
          <Button
            label="Continue"
            size="sm"
            disabled={!fieldStates.eventFormat.isValid}
            onPress={handleContinue}
          />
        </div>
      }
    >
      {EVENT_FORMAT_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={watch('eventFormat') === card.id}
          onClick={() => setValue('eventFormat', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}
    </CreateEventLayout>
  );
}
