'use client';
import React from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { GradientBorderCard } from '@/components/cards';
import { CreateEventLayout } from '@/components/layouts';
import { Button } from '@/components/ui';
import { type CreateEventFormType, TICKET_TYPE_CARDS } from '@/lib';
import { type StepProps } from '../page';

export default function ChooseEventTicketType({
  setStep,
  fromStep,
}: StepProps) {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const { fields } = useFieldArray({
    control,
    name: 'tickets',
  });

  const ticketType = watch('ticketType');

  const handleContinue = () => {
    if (!ticketType) return;
    if (ticketType === 'FREE') {
      setStep(8);
    } else {
      if (fields.length < 2) {
        setStep(10);
      } else {
        setStep(11);
      }
    }
  };

  return (
    <CreateEventLayout
      title="Will your event be free or paid?"
      subTitle="Choose the ticket type that works best for your event."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            onPress={() => setStep(fromStep || 3)}
          />
          <Button
            label="Continue"
            size="sm"
            disabled={!ticketType}
            onPress={handleContinue}
          />
        </div>
      }
    >
      {TICKET_TYPE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={ticketType === card.id}
          onClick={() => setValue('ticketType', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}
    </CreateEventLayout>
  );
}
