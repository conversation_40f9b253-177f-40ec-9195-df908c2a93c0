'use client';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button, ControlledInput } from '@/components/ui';
import { type CreateEventFormType, useFieldBlurAndFilled } from '@/lib';

import { type StepProps } from '../page';

export default function FreeEventDetails({ setStep }: StepProps) {
  const { control, setValue } = useFormContext<CreateEventFormType>();

  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'maxAttendees',
  ]);

  return (
    <CreateEventLayout
      title="Set up free event"
      subTitle="Choose between requiring attendees to register for the event or just uploading your event."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            onPress={() => setStep(8)}
          />
          <Button
            label="Continue"
            size="sm"
            disabled={!fieldStates.maxAttendees.isValidOptional}
            onClick={() => setStep(13)}
          />
        </div>
      }
    >
      <div className="gap-4">
        <ControlledInput
          name="maxAttendees"
          label="Max. number of attendees"
          control={control}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setValue('maxAttendees', Number(e.target.value))
          }
          type="number"
          inputMode="numeric"
        />
      </div>
    </CreateEventLayout>
  );
}
