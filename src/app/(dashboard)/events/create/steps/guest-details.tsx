'use client';

import React, { useState } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';

import { CreateEventLayout } from '@/components/layouts';
import { Input } from '@/components/ui';
import {
  Button,
  colors,
  Modal,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  FeatureToggle,
} from '@/components/ui';
import { type CreateEventFormType } from '@/lib';
import { ChevronDown } from 'lucide-react';
import { type StepProps } from '../page';
import { IoCloseCircle } from 'react-icons/io5';

export const FIELD_TYPES = [
  { label: 'Alpha-numeric', value: 'alpha-numeric' },
  { label: 'Numeric', value: 'numeric' },
  { label: 'Age range selector', value: 'age-range-selector' },
  { label: 'Country selector', value: 'country-selector' },
  { label: 'Sex selector', value: 'sex-selector' },
];

export default function GuestDetails({ setStep }: StepProps) {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'registrationFields',
  });

  const handleToggle = (checked: boolean) => {
    if (checked) {
      setValue('isRegistrationRequired', true);
    } else {
      setValue('isRegistrationRequired', false);
    }
  };

  const [fieldName, setFieldName] = useState('');
  const [fieldType, setFieldType] =
    useState<(typeof FIELD_TYPES)[number]['value']>('alpha-numeric');

  const [openFieldsModal, setOpenFieldsModal] = useState(false);

  const alert = (message: string) => window.alert(message);

  const handleAddField = () => {
    if (!fieldName || !fieldType) return;
    const isDuplicate = fields.some(
      (field) =>
        field.name.trim().toLowerCase() === fieldName.trim().toLowerCase()
    );
    if (isDuplicate) {
      alert('Field already exists. Please use a unique field name.');
      return;
    }
    append({
      name: fieldName.trim(),
      type: fieldType as
        | 'alpha-numeric'
        | 'numeric'
        | 'age-range-selector'
        | 'country-selector'
        | 'sex-selector',
    });
    setFieldName('');
    setFieldType('alpha-numeric');
    setOpenFieldsModal(false);
  };

  const StaticField = ({ label }: { label: string }) => (
    <div className="min-h-12 flex flex-row items-start justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark">
      <p className="text-neutral-400 dark:text-neutral-500">{label}</p>
      <p className="text-neutral-400 dark:text-neutral-500">(Required)</p>
    </div>
  );

  return (
    <CreateEventLayout
      title="Guest Details"
      subTitle="Collect additional details from your guests during checkout."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            onPress={() => setStep(7)}
          />
          <Button size="sm" label="Continue" onPress={() => setStep(9)} />
        </div>
      }
    >
      <div className="flex flex-col gap-4">
        <FeatureToggle
          onChange={handleToggle}
          accessibilityLabel="Enable registration"
          checked={watch('isRegistrationRequired') ? true : false}
          title="Enable registration"
        />
        {watch('isRegistrationRequired') && (
          <>
            <StaticField label="Full name" />
            <StaticField label="Email" />

            {fields.map((field, index) => (
              <div
                key={field.id}
                className="relative min-h-12 flex flex-row items-start justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              >
                <p className="text-neutral-400 dark:text-neutral-500">
                  {field.name}
                </p>
                {(field.type === 'age-range-selector' ||
                  field.type === 'country-selector' ||
                  field.type === 'sex-selector') && (
                  <div>
                    <ChevronDown size={20} color={colors.grey[70]} />
                  </div>
                )}

                <button
                  onClick={() => remove(index)}
                  className="absolute -right-1 -top-1 z-10 flex items-center justify-center rounded-full bg-bg-danger-primary dark:bg-bg-danger-primary size-6"
                >
                  <div className="flex items-center justify-center rounded-full border-2 border-accent-on-accent dark:border-accent-on-accent size-4">
                    <IoCloseCircle size={12} color="#fff" />
                  </div>
                </button>
              </div>
            ))}

            <Button
              label="Add field +"
              variant="secondary"
              className="w-[154px] py-2"
              size="sm"
              onClick={() => setOpenFieldsModal(true)}
            />
          </>
        )}
      </div>

      <Modal
        isOpen={openFieldsModal}
        onClose={() => setOpenFieldsModal(false)}
        title="Add field"
      >
        <div className="flex-1 space-y-4">
          <Input
            label="Field name"
            value={fieldName}
            onChange={(e) => setFieldName(e.target.value)}
          />
          <Select
            value={fieldType}
            onValueChange={(val) => setFieldType(val as typeof fieldType)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose field type" />
            </SelectTrigger>

            <SelectContent>
              {FIELD_TYPES.map((opt) => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="mt-auto">
            <Button
              label="Save"
              onClick={handleAddField}
              disabled={!fieldName || !fieldType}
            />
          </div>
        </div>
      </Modal>
    </CreateEventLayout>
  );
}
