'use client';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { LocationInput } from '@/components/ui';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button, ControlledInput } from '@/components/ui';
import { type CreateEventFormType } from '@/lib';
import { type LocationType } from '@/types';
import { type StepProps } from '../page';
import { IoLink } from 'react-icons/io5';
import { Checkbox } from '@/components/ui/checkbox';

export default function Hybrid({ setStep, fromStep }: StepProps) {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const [isSendLater, setIsSendLater] = useState(false);

  const eventUrl = watch('onlineUrl');
  const location = watch('location');

  const onSelectLocation = useCallback(
    (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: { lat: number; lng: number }
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setValue('location', locationObject);
    },
    [setValue]
  );

  return (
    <CreateEventLayout
      title="Add event address and streaming URL"
      subTitle="Add the address and streaming URL for your event."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            disabled={fromStep === 13}
            onPress={() => setStep(fromStep || 3)}
          />
          <Button
            label="Continue"
            size="sm"
            disabled={
              !location?.address ||
              (!isSendLater &&
                !(
                  eventUrl &&
                  (() => {
                    try {
                      new URL(eventUrl);
                      return true;
                    } catch {
                      return false;
                    }
                  })()
                ))
            }
            onPress={() => setStep(fromStep === 13 ? 13 : 7, 6)}
          />
        </div>
      }
    >
      <div className="flex flex-col gap-4">
        <LocationInput
          onSelectLocation={onSelectLocation}
          defaultValue={watch('location')}
        />
        <ControlledInput
          name="onlineUrl"
          control={control}
          icon={
            <IoLink size={24} className="text-fg-muted dark:text-fg-muted" />
          }
          disabled={isSendLater}
        />
        <ControlledInput
          name="passcode"
          disabled={isSendLater}
          control={control}
          label="Link passcode (optional)"
        />
        <Checkbox
          label="Send link to attendees later"
          checked={isSendLater}
          className="text-fg-subtle-dark"
          onChange={() => setIsSendLater((prev) => !prev)}
          accessibilityLabel=""
        />
      </div>
    </CreateEventLayout>
  );
}
