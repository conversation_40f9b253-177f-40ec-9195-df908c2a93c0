'use client';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import { useState } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { getQueryClient } from '@/api';
import {
  type PresaleConfig,
  type TicketCategoriesType,
  useCreateEvent,
} from '@/api/events';
import { TicketCard } from '@/components/cards/ticket-card';
import { ConfirmationDialog } from '@/components/dialogs';
import { CollaboratorCard } from '@/components/events';
import { CreateEventLayout } from '@/components/layouts';
import {
  Button,
  colors,
  H2,
  H5,
  Image,
  Pencil,
  semanticColors,
  Small,
  Tiny,
  XsBoldLabel,
} from '@/components/ui';
import {
  type CreateEventFormType,
  toAmountInMinor,
  useFieldBlurAndFilled,
  formatDate,
  formatDateTime,
  formatTime,
} from '@/lib';
import { IoEye, IoEyeOff } from 'react-icons/io5';
import { Calendar, MapPin } from 'lucide-react';
import { type StepProps } from '../page';

export default function EventDetails({ setStep, fromStep }: StepProps) {
  const { watch, control, getValues, setValue } =
    useFormContext<CreateEventFormType>();

  const { fields, remove } = useFieldArray({
    control,
    name: 'tickets',
  });

  const ticketValues = watch('tickets');
  const location = watch('location');
  const onlineEventUrl = watch('onlineUrl');

  const artists = getValues('collaborators') || [];
  useFieldBlurAndFilled<CreateEventFormType>([
    'title',
    'description',
    'timezone',
    'category',
    'startDatetime',
    'endDatetime',
    'collaborators',
    'tickets',
  ]);

  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const queryClient = getQueryClient();
  const router = useRouter();

  const [menuVisible, setMenuVisible] = useState<string | number | null>(null);

  const [confirmRAVisible, setConfirmRAVisible] = useState(false);

  const [artistToDelete, setArtistToDelete] = useState<string | null>(null);

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState<number | null>(null);

  const { mutate, isPending } = useCreateEvent();
  return (
    <CreateEventLayout
      title="Review your event"
      subTitle="Make sure everything looks good before publishing."
      fullWidth={true}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
        <div className="relative w-full aspect-square">
          <Image
            src={getValues('bannerObj.uri') || '/landscape-event-bg.png'}
            className="object-cover rounded-md"
            alt=""
            fill
          />
        </div>

        <div className="flex-1 flex flex-col gap-4 py-6">
          <div className="flex flex-row items-center justify-between">
            <H2>{getValues('title')}</H2>
            {getValues('eventType') === 'PRIVATE' ? (
              <div className="flex flex-row items-center justify-center rounded-full bg-fg-danger-light dark:bg-fg-danger-dark p-2">
                <IoEyeOff size={12} color="#EA5455" className="mr-1" />
                <XsBoldLabel className="text-danger-60 dark:text-danger-40">
                  Private
                </XsBoldLabel>
              </div>
            ) : (
              <div className="flex flex-row items-center justify-center rounded-full bg-green-10 dark:bg-green-80 p-2">
                <IoEye size={12} color="#28C76F" className="mr-1" />
                <XsBoldLabel className="text-green-60 dark:text-green-40">
                  Public
                </XsBoldLabel>
              </div>
            )}
          </div>
          <div className="flex flex-row justify-between gap-3">
            <div className="flex flex-row gap-3">
              <Calendar
                size={24}
                color={isDark ? colors.white : colors.grey[100]}
              />
              <H5>
                {formatDate(getValues('startDatetime')) ===
                formatDate(getValues('endDatetime')) ? (
                  <>
                    {formatDate(getValues('startDatetime'))}
                    {'\n'}
                    {formatTime(getValues('startDatetime'))} -{' '}
                    {formatTime(getValues('endDatetime'))}
                  </>
                ) : (
                  <>
                    {formatDateTime(getValues('startDatetime'))} -{'\n'}
                    {formatDateTime(getValues('endDatetime'))}
                  </>
                )}
              </H5>
            </div>
            <Button
              label="Edit"
              className="w-[62px]"
              size="xs"
              iconPosition="right"
              onClick={() => setStep(1, 13)}
              variant="outline"
              iconClassName="right-2"
              icon={
                <Pencil
                  color={
                    isDark
                      ? semanticColors.accent.bold.dark
                      : semanticColors.accent.bold.light
                  }
                />
              }
            />
          </div>
          {(getValues('onlineUrl') || getValues('location')) && (
            <div className="flex flex-row justify-between gap-3">
              <div className="flex flex-row gap-3">
                <MapPin
                  size={24}
                  color={isDark ? colors.white : colors.grey[100]}
                />

                <div className="flex flex-col gap-2">
                  <div className="flex flex-col gap-2">
                    {getValues('eventFormat') === 'IN_PERSON' && (
                      <div className="flex flex-col gap-1">
                        <H5>{getValues('location').landmark}</H5>
                        <Tiny className="text-grey-60 dark:text-grey-50">
                          {getValues('location').state +
                            ', ' +
                            getValues('location').country}
                        </Tiny>
                      </div>
                    )}

                    {getValues('eventFormat') === 'ONLINE' && (
                      <div className="flex flex-col gap-1">
                        <H5>Event URL</H5>
                        <Tiny className="text-grey-60 dark:text-grey-50">
                          {getValues('onlineUrl')}
                        </Tiny>
                      </div>
                    )}

                    {getValues('eventFormat') === 'HYBRID' && (
                      <>
                        <div className="flex flex-col gap-1">
                          <H5>{getValues('location').address}</H5>
                          <Tiny className="text-grey-60 dark:text-grey-50">
                            {getValues('location').address}
                          </Tiny>
                        </div>
                        <div className="flex flex-col gap-1">
                          <H5>Event URL</H5>
                          <Tiny className="text-grey-60 dark:text-grey-50">
                            {getValues('onlineUrl')}
                          </Tiny>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <Button
                label="Edit"
                className="w-[62px]"
                onClick={() => {
                  if (getValues('eventFormat') === 'ONLINE') {
                    setStep(3, 13);
                  } else if (getValues('eventFormat') === 'IN_PERSON') {
                    setStep(4, 13);
                  } else {
                    setStep(6, 13);
                  }
                }}
                size="xs"
                iconPosition="right"
                variant="outline"
                iconClassName="right-2"
                icon={
                  <Pencil
                    color={
                      isDark
                        ? semanticColors.accent.bold.dark
                        : semanticColors.accent.bold.light
                    }
                  />
                }
              />
            </div>
          )}

          {artists.length > 0 && (
            <>
              <H5 className="text-grey-60 dark:text-grey-50">Lineup</H5>
              <div className="overflow-visible space-y-4 flex">
                <div className="flex flex-row flex-wrap gap-2">
                  {artists.map((artist) => (
                    <CollaboratorCard
                      key={artist.id}
                      artist={artist}
                      onRemove={() => {
                        setArtistToDelete(artist.id);
                        setConfirmVisible(true);
                      }}
                    />
                  ))}
                </div>
              </div>
            </>
          )}
          {getValues('description') && (
            <div className="flex flex-col gap-2">
              <H5 className="text-grey-60 dark:text-grey-50">
                About this event
              </H5>
              <Small>{getValues('description')}</Small>
              <Button
                label="Edit"
                className="w-[62px]"
                size="xs"
                onClick={() => setStep(1, 13)}
                iconPosition="right"
                variant="outline"
                iconClassName="right-2"
                icon={
                  <Pencil
                    color={
                      isDark
                        ? semanticColors.accent.bold.dark
                        : semanticColors.accent.bold.light
                    }
                  />
                }
              />
            </div>
          )}

          {getValues('tickets')?.length > 1 && (
            <div className="flex flex-col gap-2">
              <H5 className="text-grey-60 dark:text-grey-50">Tickets</H5>
              <a onClick={() => setMenuVisible(null)} className="flex-1">
                <div className="flex flex-col gap-4">
                  {fields.map((field, index) => {
                    if (index === 0) return null;
                    const ticket = ticketValues?.[index];

                    return (
                      <div key={field.id}>
                        <TicketCard
                          ticket={ticket}
                          fieldId={field.id}
                          isMenuOpen={menuVisible === field.id}
                          setMenuVisible={setMenuVisible}
                          onEdit={() => {
                            const id = ticket?.id;
                            setStep(12, 13, id);
                            setMenuVisible(null);
                          }}
                          onDelete={() => {
                            setTicketToDelete(index);
                            setConfirmVisible(true);
                            setMenuVisible(null);
                          }}
                        />
                      </div>
                    );
                  })}
                  <Button
                    label="Add new ticket +"
                    variant="secondary"
                    className="w-[154px] py-2"
                    size="sm"
                    onPress={() => setStep(10, 13)}
                  />
                </div>
              </a>
            </div>
          )}
          <div className="flex flex-row gap-4">
            <Button
              variant="secondary"
              label="Previous"
              size="sm"
              onPress={() =>
                setStep(fromStep || watch('ticketType') === 'FREE' ? 8 : 11)
              }
            />
            <Button
              label="Create"
              loading={isPending}
              size="sm"
              disabled={isPending}
              onPress={() => {
                const {
                  category,
                  description,
                  endDatetime,
                  eventFormat,
                  eventType,
                  startDatetime,
                  ticketType,
                  tickets,
                  timezone,
                  title,
                  bannerObj,
                  isRegistrationRequired,
                  maxAttendees,
                  passcode,
                  media,
                  registrationFields,
                } = watch();

                const formDataPayload = new FormData();
                formDataPayload.append('title', title);
                formDataPayload.append('description', description);
                // @ts-expect-error -abc
                formDataPayload.append('banner', bannerObj.file);
                media?.forEach((file) => {
                  formDataPayload.append('media', file.file as any);
                });

                artists
                  .map(({ id, avatar, name }) => ({
                    spotifyArtistId: id,
                    avatar,
                    artistName: name,
                  }))
                  .forEach((artist, index) => {
                    Object.entries(artist).forEach(([key, value]) => {
                      formDataPayload.append(
                        `artists[${index}][${key}]`,
                        value
                      );
                    });
                  });
                if (location && location.address !== '') {
                  formDataPayload.append(
                    'location',
                    JSON.stringify({
                      city: location.city,
                      state: location.state,
                      street: String(location.street),
                      country: location.country,
                      address: location.address,
                      coordinates: {
                        lat: location.coordinates.lat,
                        lng: location.coordinates.lng,
                      },
                      ...(location.landmark && {
                        landmark: location.landmark,
                      }),
                    })
                  );
                }
                formDataPayload.append(
                  'startTime',
                  startDatetime.toISOString()
                );
                formDataPayload.append('endTime', endDatetime.toISOString());
                formDataPayload.append('categoryId', category);
                formDataPayload.append('timezone', timezone);
                formDataPayload.append('eventFormat', eventFormat);
                formDataPayload.append('eventType', eventType);
                formDataPayload.append('ticketType', ticketType);
                formDataPayload.append(
                  'registrationRequired',
                  // @ts-expect-error -abc
                  isRegistrationRequired || false
                );

                formDataPayload.append(
                  'isTicketed',
                  // @ts-expect-error -abc
                  true
                );
                if (maxAttendees) {
                  formDataPayload.append(
                    'maxAttendees',
                    // @ts-expect-error -abc
                    maxAttendees
                  );
                }
                if (onlineEventUrl && onlineEventUrl !== 'https://') {
                  formDataPayload.append('onlineEventUrl', onlineEventUrl);
                }
                if (passcode) {
                  formDataPayload.append('passcode', passcode);
                }

                const initial: TicketCategoriesType = {};
                const presaleTickets: Omit<PresaleConfig, 'eventId'>[] = [];

                const ticketCategories = tickets
                  .slice(1)
                  .reduce((prev, item) => {
                    const {
                      name,
                      price,
                      quantity,
                      id,
                      hasPresale,
                      hasPurchaseLimit,
                      hasTimeline,
                      description,
                      endDatetime,
                      purchaseLimit,
                      startDatetime,
                    } = item;

                    const costInMinor = toAmountInMinor(price);

                    prev[name] = {
                      cost: costInMinor,
                      quantity: Number(quantity),
                      id,
                      hasPresale,
                      hasPurchaseLimit,
                      hasTimeline,
                      description,
                      endDateTime: endDatetime,
                      purchaseLimit,
                      startDateTime: startDatetime,
                    };

                    if (hasPresale) {
                      const {
                        presalePrice,
                        presaleQuantity,
                        presalePurchaseLimit,
                        presaleDescription,
                        presaleStartDatetime,
                        presaleEndDatetime,
                      } = item;

                      const presaleConfig: Omit<PresaleConfig, 'eventId'> = {
                        name: name + ' - Presale',
                        price: toAmountInMinor(presalePrice),
                        quantity: Number(presaleQuantity),
                        ticketCategoryId: id,
                        totalTickets: Number(presaleQuantity),
                        description: presaleDescription || '',
                        purchaseLimit:
                          presalePurchaseLimit || Number(presaleQuantity),
                        currency: 'NGN', // You may want to make this configurable
                        startDateTime: presaleStartDatetime.toISOString(),
                        // dayjs(presaleEndDatetime).format('YYYY-MM-DD')
                        endDateTime: presaleEndDatetime.toISOString(),
                      };

                      presaleTickets.push(presaleConfig);
                    }

                    return prev;
                  }, initial);

                formDataPayload.append(
                  'ticketCategories',
                  JSON.stringify(ticketCategories)
                );

                presaleTickets.forEach((p, index) => {
                  Object.entries(p).forEach(([key, value]) => {
                    formDataPayload.append(
                      `presaleConfig[${index}][${key}]`,
                      // @ts-expect-error -abc
                      value
                    );
                  });
                });

                if (registrationFields && registrationFields.length > 0) {
                  registrationFields.forEach((field, index) => {
                    Object.entries(field).forEach(([key, value]) => {
                      formDataPayload.append(
                        `registrationFields[${index}][${key}]`,
                        value
                      );
                    });
                  });
                }

                mutate(formDataPayload, {
                  onSuccess: (response) => {
                    toast.success('Event created successfully');
                    router.replace(`/events/${response.slug}/creation-success`);
                    queryClient.invalidateQueries({ queryKey: ['getEvents'] });
                  },
                  onError: (error) => toast.error(error.message),
                });
              }}
            />
          </div>
        </div>
      </div>
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to delete this ticket?"
        onCancel={() => {
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
        onConfirm={() => {
          if (ticketToDelete !== null) {
            remove(ticketToDelete);
          }
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
      />
      <ConfirmationDialog
        visible={confirmRAVisible}
        message="Are you sure you want to delete this artist?"
        onCancel={() => {
          setConfirmRAVisible(false);
          setArtistToDelete(null);
        }}
        onConfirm={() => {
          if (artistToDelete !== null) {
            setTimeout(() => {
              setValue(
                'collaborators',
                artists.filter((a) => a.id !== artistToDelete)
              );
            }, 0);
          }
          setConfirmRAVisible(false);
          setArtistToDelete(null);
        }}
      />
    </CreateEventLayout>
  );
}
