'use client';

import { useFormContext, useFieldArray } from 'react-hook-form';
import { useState } from 'react';
import { TicketCard } from '@/components/cards';
import { ConfirmationDialog } from '@/components/dialogs';
import { CreateEventLayout } from '@/components/layouts';
import { Button } from '@/components/ui';
import { type CreateEventFormType } from '@/lib';
import { type StepProps } from '../page';

export default function Tickets({ setStep, fromStep }: StepProps) {
  const { watch, control } = useFormContext<CreateEventFormType>();
  const { fields, remove } = useFieldArray({
    control,
    name: 'tickets',
  });

  const [menuVisible, setMenuVisible] = useState<string | null>(null);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState<number | null>(null);

  return (
    <CreateEventLayout
      title="Set up tickets"
      subTitle="You can create multiple ticket types with varying prices."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            disabled={fromStep === 13}
            onPress={() => setStep(fromStep || 7)}
          />
          <Button label="Continue" size="sm" onPress={() => setStep(13)} />
        </div>
      }
    >
      <div onClick={() => setMenuVisible(null)} className="flex-1">
        <div className="flex flex-col gap-4">
          {fields.map((field, index) => {
            if (index === 0) return null;
            const ticket = watch(`tickets.${index}`);
            if (!ticket) return null;
            return (
              <div key={field.id} className="relative gap-4">
                <TicketCard
                  ticket={ticket}
                  fieldId={field.id}
                  isMenuOpen={menuVisible === field.id}
                  setMenuVisible={setMenuVisible}
                  onEdit={() => {
                    const id = ticket?.id;
                    setStep(12, 11, id);
                    setMenuVisible(null);
                  }}
                  onDelete={() => {
                    setTicketToDelete(index);
                    setConfirmVisible(true);
                    setMenuVisible(null);
                  }}
                />
              </div>
            );
          })}
          <Button
            label="Add new ticket +"
            variant="secondary"
            className="w-[154px] py-2"
            size="sm"
            onClick={() => setStep(10, 11)}
          />
        </div>
      </div>
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to delete this ticket?"
        onCancel={() => {
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
        onConfirm={() => {
          if (ticketToDelete !== null) {
            remove(ticketToDelete);
          }
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
      />
    </CreateEventLayout>
  );
}
