'use client';
// import { useQueryClient } from '@tanstack/react-query';
// import { useRouter } from 'next/navigation';
// import LottieView from 'lottie-react';
// import { useCallback, useMemo, useState } from 'react';
// import { toast } from 'react-hot-toast';
// import {
//   AdvancedMarker,
//   APIProvider,
//   Map,
//   Marker,
// } from '@vis.gl/react-google-maps';

// import type { Point } from '@/api/auth';
// import { useSearchEvents } from '@/api/events';
// import { useJoinLiveSession } from '@/api/session';
// import { useGetHomeData, useGetUserFavourites } from '@/api';
// import { LiveUserAvatar } from '@/components/avatars';
// import { HomeCompletionCard, LiveUserCard } from '@/components/cards';
// import { EventCarousel } from '@/components/carousels';
// import { FloatingActionPopover } from '@/components/ui';
// import { LoadingScreen } from '@/components/loaders';
// import { HomeLoading } from '@/components/loaders';
// import { EmptyState } from '@/components/ui';
// import { H5, Image, Skeleton } from '@/components/ui';
// import {
//   cn,
//   CREATOR_COMPLETION_CARDS,
//   QUESTER_COMPLETION_CARDS,
//   useAppSession,
//   useAuth,
// } from '@/lib';
// import { useLocation } from '@/lib';
// import { getCombinedSessionAndDjSessionsList } from '@/lib/home/<USER>';
// import { useSession } from '@/lib/session';
// import { getLiveSession } from '@/lib/session/utils';

// import { ErrorScreen } from '@/components/loaders';
// import circleAnimationData from '~/animations/circle.json';

// export default function Home() {
//   const router = useRouter();

//   const queryClient = useQueryClient();
//   const [refreshing, setRefreshing] = useState(false);

//   const onRefresh = useCallback(() => {
//     setRefreshing(true);

//     Promise.all([
//       queryClient.invalidateQueries({
//         queryKey: ['getUserFavourites'],
//         exact: true,
//       }),
//       queryClient.invalidateQueries({
//         queryKey: ['getHomeData'],
//         exact: true,
//       }),
//       queryClient.invalidateQueries({
//         queryKey: ['searchEvents'],
//         exact: false,
//       }),
//       queryClient.invalidateQueries({
//         queryKey: ['getTrendingCreators'],
//         exact: true,
//       }),
//       queryClient.invalidateQueries({
//         queryKey: ['getUser'],
//         exact: false,
//       }),
//     ])
//       .then(() => new Promise((resolve) => setTimeout(resolve, 1000)))
//       .finally(() => {
//         setRefreshing(false);
//       });
//   }, [queryClient]);

//   const activeSession = getLiveSession();

//   const { user: currentUser } = useAuth();

//   const setJoinedSessionId = useSession.use.setJoinedSessionId();
//   const { mutate: joinLiveSession, isPending: isJoiningSession } =
//     useJoinLiveSession();
//   const { location, locationAccessible } = useLocation();

//   const currentCoordinate: Pick<Point, 'lat' | 'lng'> = {
//     lat: location?.coordinates.lat || 0,
//     lng: location?.coordinates.lng || 0,
//   };

//   const { data, isLoading, isLoadingError, isError } = useGetHomeData({
//     variables: {
//       ...currentCoordinate,
//       userCurrency: currentUser?.wallet?.currency || 'NGN',
//     },
//     enabled: currentCoordinate.lat !== 0 && currentCoordinate.lng !== 0,
//     refetchInterval: 60 * 1000,
//   });

//   const { data: eventData } = useSearchEvents({
//     variables: { skip: 0, take: 8, isFeatured: true },
//   });

//   const { data: favourites = [] } = useGetUserFavourites({
//     variables: {
//       type: 'ACCOUNT',
//     },
//   });

//   const isCreator = currentUser?.role !== 'QUESTER';

//   const creatorCompletedSteps = useMemo(
//     () =>
//       Number(Boolean(currentUser?.bio)) +
//       Number(Boolean(currentUser?.profileImageUrl)) +
//       Number(Boolean(currentUser?.category)) +
//       // Number(Boolean(currentUser?.genres?.length)) +
//       Number(Boolean(favourites?.length)),
//     [
//       currentUser?.bio,
//       currentUser?.profileImageUrl,
//       currentUser?.category,
//       // currentUser?.genres?.length,
//       favourites?.length,
//     ]
//   );

//   const questerCompletedSteps = useMemo(
//     () =>
//       Number(Boolean(currentUser?.bio)) +
//       Number(Boolean(currentUser?.profileImageUrl)) +
//       // Number(Boolean(currentUser?.genres?.length)) +
//       Number(Boolean(favourites?.length)),
//     [
//       currentUser?.bio,
//       currentUser?.profileImageUrl,
//       // currentUser?.genres?.length,
//       favourites?.length,
//     ]
//   );

//   const cards = isCreator
//     ? CREATOR_COMPLETION_CARDS.filter((card) => {
//         if (card.id === 'bio') return !currentUser?.bio;
//         if (card.id === 'avatar') return !currentUser?.profileImageUrl;
//         if (card.id === 'category') return !currentUser?.category;
//         // if (card.id === 'preferences') return !currentUser?.genres.length;
//         if (card.id === 'inspirations') return !favourites?.length;
//         return true;
//       })
//     : QUESTER_COMPLETION_CARDS.filter((card) => {
//         if (card.id === 'bio') return !currentUser?.bio;
//         if (card.id === 'avatar') return !currentUser?.profileImageUrl;
//         // if (card.id === 'preferences') return !currentUser?.genres.length;
//         if (card.id === 'inspirations') return !favourites?.length;
//         return true;
//       });

//   const { hideTodo, setHideTodo } = useAppSession();

//   const hasTodo = useMemo(() => {
//     if (!currentUser) return false;

//     if (isCreator) {
//       return creatorCompletedSteps < CREATOR_COMPLETION_CARDS.length;
//     } else {
//       return questerCompletedSteps < QUESTER_COMPLETION_CARDS.length;
//     }
//   }, [creatorCompletedSteps, isCreator, questerCompletedSteps, currentUser]);

//   const handleJoinLiveSession = useCallback(
//     (sessionId: string) => {
//       const alreadyInSession = activeSession?.id === sessionId;
//       const inAnotherSession = !!activeSession?.id && !alreadyInSession;

//       if (inAnotherSession) {
//         toast.error('Already in a live session');
//         return;
//       }

//       joinLiveSession(
//         { sessionId },
//         {
//           onSuccess: () => {
//             setJoinedSessionId(sessionId);
//             toast.success('Successfully joined live session');
//             router.push(`/session/${sessionId}`);
//           },
//           onError: (error) => toast.error(error.message),
//         }
//       );
//     },
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//     [activeSession?.id, joinLiveSession, setJoinedSessionId]
//   );

//   if (isJoiningSession) return <LoadingScreen />;

//   if (isLoading || !data) {
//     return <HomeLoading />;
//   }

//   const { djSessions, trendingDJs, user } = data;

//   const combinedSessionAndTrendingList = getCombinedSessionAndDjSessionsList({
//     activeSession: activeSession?.id || '',
//     activeSessionCreator: activeSession?.dj?.username || '',
//     djSessions: [...djSessions].reverse(),
//     trendingDJs,
//     user,
//   });
//   if (isLoadingError || isError) {
//     return <ErrorScreen title="Error" message="Something went wrong" />;
//   }

//   return (
//     <div className="relative flex-1 bg-bg-canvas-light dark:bg-bg-canvas-dark">
//       <div className="gap-4 rounded-b-xl bg-brand-20 pb-6 dark:bg-brand-90">
//         <div className="flex flex-row overflow-x-auto gap-4">
//           {combinedSessionAndTrendingList.map((item, index) =>
//             'dj' in item ? (
//               <LiveUserAvatar
//                 key={item.id}
//                 username={item.dj.username}
//                 avatar={item.dj.profileImageUrl}
//                 className={cn(index === 0 && 'ml-4')}
//                 onPress={() => handleJoinLiveSession(item.id)}
//                 hideStatusPill={item.status !== 'LIVE'}
//               />
//             ) : (
//               <LiveUserAvatar
//                 key={item.id}
//                 username={item.name}
//                 avatar={item.avatar}
//                 className={cn(index === 0 && 'ml-4')}
//                 onPress={() => router.push(`/users/${item.id}`)}
//                 hideStatusPill
//               />
//             )
//           )}
//         </div>
//       </div>
//       <div className="flex-1">
//         <div className="flex-1">
//           <div className="gap-4 py-4">
//             <div className="gap-4 px-4">
//               <H5>Happening nearby</H5>
//               <div className="relative h-[203px] overflow-hidden rounded-lg">
//                 {currentCoordinate.lat && currentCoordinate.lng ? (
//                   <APIProvider
//                     apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY || ''}
//                   >
//                     <Map
//                       defaultCenter={{
//                         lat: currentCoordinate.lat,
//                         lng: currentCoordinate.lng,
//                       }}
//                       defaultZoom={locationAccessible ? 15 : 6}
//                       style={{ width: '100%', height: '100%' }}
//                       disableDefaultUI={true}
//                       mapTypeId={'roadmap'}
//                     >
//                       <AdvancedMarker position={currentCoordinate} />
//                     </Map>
//                   </APIProvider>
//                 ) : (
//                   <Skeleton className="h-56 w-full rounded-lg" />
//                 )}

//                 <button
//                   className="absolute inset-0 z-10 items-center justify-center"
//                   onClick={() => {
//                     router.push('/explore/map-view');
//                   }}
//                 >
//                   <div className="relative items-center justify-center">
//                     <LottieView
//                       autoPlay
//                       style={{ width: 150, height: 150 }}
//                       animationData={circleAnimationData}
//                     />
//                     <Image
//                       className="absolute size-20"
//                       src={'/assets/images/popla.png'}
//                       content="cover"
//                       alt=""
//                     />
//                   </div>
//                 </button>
//               </div>
//             </div>
//             {!hideTodo && hasTodo && (
//               <div className="gap-y-4 py-2 pl-4">
//                 <div className="flex-row items-center justify-between pr-4">
//                   <H5 className="">To Do</H5>

//                   <button onClick={() => setHideTodo(true)}>
//                     <H5 className="text-fg-link dark:text-fg-link">Hide</H5>
//                   </button>
//                 </div>

//                 <div className="flex flex-row gap-x-4">
//                   {cards.map((card, index) => (
//                     <HomeCompletionCard
//                       key={index}
//                       onPress={() =>
//                         router.push(`/profile/completion/${card.id}`)
//                       }
//                       title={card.title}
//                       imageSource={card.imageSource}
//                       className={index === cards.length - 1 ? 'pr-4' : ''}
//                     />
//                   ))}
//                 </div>
//               </div>
//             )}

//             {djSessions.length > 0 && (
//               <div className="gap-4">
//                 <H5 className="px-4">Join now</H5>
//                 <div className="flex flex-row overflow-x-auto no-scrollbar">
//                   {djSessions.length === 0 ? (
//                     <EmptyState />
//                   ) : (
//                     djSessions.map((item, index) => (
//                       <div key={item.id} className="flex flex-row items-center">
//                         <LiveUserCard
//                           status="LIVE"
//                           username={item.dj.username}
//                           avatar={item.dj.profileImageUrl}
//                           bgImage={
//                             index % 2 === 0
//                               ? '/assets/gradient-bg/gradient-card-bg.png'
//                               : '/assets/gradient-bg/gradient-card-bg-2.png'
//                           }
//                           className={cn('ml-0', index === 0 && 'ml-4')}
//                           onPress={() => handleJoinLiveSession(item.id)} // use onClick on web
//                         />
//                         {/* Separator */}
//                         {index !== djSessions.length - 1 && (
//                           <div className="size-4" />
//                         )}
//                       </div>
//                     ))
//                   )}
//                 </div>
//               </div>
//             )}

//             <div className="gap-4">
//               <H5 className="px-4">Featured events</H5>
//               {eventData && eventData.events.length > 0 ? (
//                 <EventCarousel events={eventData?.events} />
//               ) : (
//                 <EmptyState />
//               )}
//             </div>
//           </div>
//         </div>
//       </div>
//       {user.role === 'CREATOR' && <FloatingActionPopover />}
//     </div>
//   );
// }
