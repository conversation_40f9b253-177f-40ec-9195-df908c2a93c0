'use client';

import { AppHeader, AppHeaderMobile, AppSidebar } from '@/components/layouts';
import { SidebarInset, SidebarProvider } from '@/components/ui';

import { usePathname } from 'next/navigation';
import { APIProvider } from '@/api';
import { cn, SearchEventsProvider } from '@/lib';
import { useAuth } from '@/lib';
import { LoadingScreen } from '@/components/loaders';

export default function AppDashboardLayout({
  children,
}: {
  readonly children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isProfilePage = pathname.startsWith('/profile');
  const { isLoggingOut } = useAuth();

  if (isLoggingOut) return <LoadingScreen />;

  return (
    <SidebarProvider>
      <div className="hidden lg:block">
        <AppSidebar />
      </div>
      <SidebarInset className="flex flex-col h-full overflow-y-auto">
        <APIProvider>
          <SearchEventsProvider>
            {!isProfilePage && (
              <>
                <div className="block lg:hidden">
                  <AppHeaderMobile />
                </div>
                <div className="hidden lg:block">
                  <AppHeader />
                </div>
              </>
            )}

            <main
              className={cn(
                'flex-1 overflow-y-auto h-full',
                isProfilePage ? '' : 'px-4'
              )}
            >
              {children}
            </main>
          </SearchEventsProvider>
        </APIProvider>
      </SidebarInset>
    </SidebarProvider>
  );
}
