'use client';
import dayjs from 'dayjs';
import { useCallback, useMemo, useState } from 'react';

import { type IEvent, useGetInfiniteEvents } from '@/api/events';
import { CreatorEventsTab } from '@/components/tab-views/my-events-tab';
import { AppTab } from '@/components/ui';
import { useAuth } from '@/lib';
import { type TabScreenItem } from '@/types';

export default function MyEvents() {
  const { user: currentUser, isLoading: userLoading } = useAuth();
  const [index, setIndex] = useState(0);

  const {
    data: eventData,
    isPending,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useGetInfiniteEvents({
    variables: {
      userId: currentUser?.id,
      organizerId: currentUser?.id,
      take: 9,
    },
  });

  const allEvents = useMemo(() => {
    return eventData?.pages?.flatMap((eventsData) => eventsData.events) || [];
  }, [eventData?.pages]);

  const { upcomingEvents, pastEvents } = useMemo(() => {
    const now = dayjs();
    return allEvents.reduce(
      (acc, event) => {
        if (dayjs(event.endTime).isAfter(now)) {
          acc.upcomingEvents.push(event);
        } else {
          acc.pastEvents.push(event);
        }
        return acc;
      },
      { upcomingEvents: [] as IEvent[], pastEvents: [] as IEvent[] }
    );
  }, [allEvents]);

  const handleLoadMore = useCallback(() => {
    if (index === 0 && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
    if (index === 1 && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [index, hasNextPage, isFetchingNextPage, fetchNextPage]);

  const tabItems: TabScreenItem[] = useMemo(() => {
    return [
      {
        key: 'Upcoming',
        title: 'Upcoming',
        component: () => (
          <CreatorEventsTab
            events={upcomingEvents}
            index={0}
            isPending={isPending || userLoading}
            handleLoadMore={index === 1 ? handleLoadMore : undefined}
            isFetchingNextPage={isFetchingNextPage}
          />
        ),
      },
      {
        key: 'Past',
        title: 'Past',
        component: () => (
          <CreatorEventsTab
            events={pastEvents}
            index={1}
            isPending={isPending || userLoading}
            handleLoadMore={index === 1 ? handleLoadMore : undefined}
            isFetchingNextPage={isFetchingNextPage}
          />
        ),
      },
    ];
  }, [upcomingEvents, pastEvents, isPending, userLoading]);

  return (
    <div className="flex-1 ">
      <AppTab
        items={tabItems}
        tabIndex={index}
        tabSetIndex={(i) => setIndex(i)}
      />
    </div>
  );
}
