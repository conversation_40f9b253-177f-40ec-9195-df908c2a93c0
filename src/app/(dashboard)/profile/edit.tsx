'use client';

import { FormProvider } from 'react-hook-form';
import { EditUserDetailsForm } from '@/components/forms/edit-user-details';
import { SocialMediaForm } from '@/components/forms/social-media-form';
import { But<PERSON>, H3, SmRegularLabel } from '@/components/ui';
import { AccountSetupProvider, useEditAccountForm } from '@/lib';

export default function EditProfile() {
  const {
    formMethods,
    usernameRefinement,
    isValidatingUsername,
    setIsValidatingUsername,
    onSubmit,
    accountUpdating,
    usernameAvailable,
  } = useEditAccountForm();

  return (
    <FormProvider {...formMethods}>
      <div className="flex flex-col bg-bg-canvas dark:bg-grey-100">
        <AccountSetupProvider
          value={{
            usernameRefinement,
            isValidatingUsername,
            setIsValidatingUsername,
            usernameAvailable,
          }}
        >
          <div className="flex-1 overflow-y-auto px-4 py-6 space-y-8">
            <div className="space-y-1">
              <H3 className="text-fg-base-light dark:text-fg-base-dark">
                Account settings
              </H3>
              <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                Please update your profile settings here
              </SmRegularLabel>
            </div>
            <EditUserDetailsForm />

            <hr className="text-fg-muted-light dark:text-fg-muted-dark" />
            <SocialMediaForm />
            <hr className="text-fg-muted-light dark:text-fg-muted-dark" />
            <div className="flex justify-end">
              <Button
                label="Update"
                disabled={isValidatingUsername || accountUpdating}
                loading={accountUpdating}
                className="w-[103px]"
                onClick={formMethods.handleSubmit(onSubmit)}
              />
            </div>
            <hr className="text-fg-muted-light dark:text-fg-muted-dark" />
          </div>
        </AccountSetupProvider>
      </div>
    </FormProvider>
  );
}
