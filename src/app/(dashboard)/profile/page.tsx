'use client';

import { useRef, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useLoggedInUser } from '@/lib';
import { H2, Image, MdBoldLabel, SmRegularLabel } from '@/components/ui';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import EditProfile from './edit';
import { useUploadProfileImage } from '@/api';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { Security } from './security';
import { LoadingScreen } from '@/components/loaders';
import { AppHeader } from '@/components/layouts';

export default function ProfilePage() {
  const { data: user, isLoading } = useLoggedInUser();

  const queryClient = useQueryClient();

  const { mutate: updateAvatar, isPending } = useUploadProfileImage();

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const pickImage = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const formDataPayload = new FormData();
    formDataPayload.append('profileImage', file);

    updateAvatar(
      {
        userId: user?.id || '',
        payload: formDataPayload,
      },
      {
        onSuccess: () => {
          toast.success('Profile image updated successfully');
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
        },
        onError: (error: any) => toast.error(error.message),
      }
    );
  };
  const [tab, setTab] = useState('Account');

  const allTabs = ['Account', 'Security'];

  if (isLoading) return <LoadingScreen />;

  return (
    <div className="flex flex-col">
      <div className="relative h-96 w-full overflow-hidden px-8 pt-6 rounded-md">
        <AppHeader />
        <div className="absolute inset-0">
          <Image
            src={user?.profileImageUrl || '/images/avatar.png'}
            alt={user?.fullName || ''}
            fill
            className="w-full h-full object-cover blur-2xl scale-110"
          />
          <div className="absolute inset-0 bg-black/30" />
        </div>

        <div className="relative space-y-4 pt-10">
          <div className="relative w-fit">
            <Avatar className="size-32">
              <AvatarImage
                src={user?.profileImageUrl || '/images/avatar.png'}
                alt={user?.fullName}
              />
              <AvatarFallback>{user?.fullName?.[0] ?? 'U'}</AvatarFallback>
            </Avatar>

            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isPending}
              className="absolute -bottom-3 left-1/2 -translate-x-1/2 px-4 py-1 text-sm 
               bg-accent-subtle-light dark:bg-accent-subtle-dark 
               rounded-full text-accent-bold-light dark:text-accent-bold-dark 
               shadow-md"
            >
              Change
            </button>

            <input
              type="file"
              accept="image/*"
              className="hidden"
              ref={fileInputRef}
              onChange={pickImage}
            />
          </div>

          <div className="space-y-2">
            <H2 className="text-accent-on-accent dark:text-accent-on-accent">
              {user?.fullName}
            </H2>
            <SmRegularLabel className="text-accent-on-accent dark:text-accent-on-accent">
              @{user?.username}
            </SmRegularLabel>
          </div>
        </div>
      </div>

      <Tabs
        value={tab}
        className="w-full space-y-4"
        onValueChange={(tab) => {
          setTab(tab);
        }}
      >
        <TabsList className="h-12 flex flex-row justify-start w-full p-4 border-b border-border-subtle-light dark:border-border-subtle-dark bg-transparent">
          {allTabs.map((tab) => (
            <MdBoldLabel asChild key={tab}>
              <TabsTrigger
                value={tab}
                className="snap-start 
                   data-[state=active]:bg-transparent 
                   data-[state=active]:border-b 
                   data-[state=active]:border-brand-60 
                   rounded-none !py-4 px-4
                   text-fg-subtle-light dark:text-fg-subtle-dark 
                   data-[state=active]:text-brand-60 
                   hover:text-white transition-colors"
              >
                {tab}
              </TabsTrigger>
            </MdBoldLabel>
          ))}
        </TabsList>
        <TabsContent value={tab}>
          {tab === 'Account' ? <EditProfile /> : <Security />}
        </TabsContent>
      </Tabs>
    </div>
  );
}
