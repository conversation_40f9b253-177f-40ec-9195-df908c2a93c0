'use client';

import { FormProvider } from 'react-hook-form';
import { Button, H3, SmRegularLabel } from '@/components/ui';
import { useUpdatePasswordForm } from '@/lib';
import { UpdatePasswordForm } from '@/components/forms';
export const Security = () => {
  const { formMethods, onSubmit, passwordUpdating } = useUpdatePasswordForm();

  return (
    <div className="flex flex-col bg-bg-canvas dark:bg-grey-100">
      <FormProvider {...formMethods}>
        <div className="flex-1 overflow-y-auto px-4 py-6 space-y-8">
          <div className="space-y-1">
            <H3 className="text-fg-base-light dark:text-fg-base-dark">
              Security
            </H3>
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              Control your account security here
            </SmRegularLabel>
          </div>
          <UpdatePasswordForm />

          <div className="flex justify-end">
            <Button
              label="Update"
              disabled={passwordUpdating}
              loading={passwordUpdating}
              className="w-[103px]"
              onClick={formMethods.handleSubmit(onSubmit)}
            />
          </div>
          <hr className="text-fg-muted-light dark:text-fg-muted-dark" />
        </div>
      </FormProvider>
    </div>
  );
};
