'use client';
// import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

import { useGetEvents } from '@/api/events';
import { useGetUser } from '@/api';
import { RegularAvatar } from '@/components/avatars';
import { EventFavoriteCard } from '@/components/cards/event-favorite';
import { MyEventsLoading } from '@/components/loaders/my-events';
import { EmptyState } from '@/components/ui';
import { Button, H3, P } from '@/components/ui';
import { cn, useAccountFavourite, useAuth } from '@/lib';
import {
  IoEyeOutline,
  IoLogoInstagram,
  IoLogoSnapchat,
  IoLogoTiktok,
  IoLogoYoutube,
} from 'react-icons/io5';
import { FiX } from 'react-icons/fi';
import { FaSpotify } from 'react-icons/fa';
import { useParams } from 'next/navigation';
import { LoadingScreen } from '@/components/loaders';

export default function UserProfile() {
  const { username } = useParams<{ username: string }>();
  const { user: currentUser } = useAuth();
  const [selectedTab, setSelectedTab] = useState<'events' | 'posts'>('events');

  const { data: user, isLoading: isUserLoading } = useGetUser({
    variables: { id: username },
  });

  const { data: eventsData, isPending: areEventsPending } = useGetEvents({
    variables: {
      userId: currentUser?.id,
      organizerId: user?.id,
    },
    enabled: !!user?.id && selectedTab === 'events',
  });

  const { favourited, toggleFavourite } = useAccountFavourite(user?.id || '');

  if (isUserLoading) {
    return (
      <div className="flex flex-1 justify-center items-center">
        <LoadingScreen />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex flex-1 justify-center items-center">
        User not found
      </div>
    );
  }

  const isEventsTab = user.role !== 'QUESTER' && selectedTab === 'events';
  const isPostsTab = user.role === 'QUESTER' || selectedTab === 'posts';
  const events = eventsData?.events ?? [];

  return (
    <div className="flex-1">
      <div className="flex-1 flex-col gap-4 md:p-4 py-4">
        <div className="items-start flex flex-col gap-4">
          <RegularAvatar
            avatar={user?.profileImageUrl || '/images/avatar.png'}
            size={80}
          />
          <H3>{user.username}</H3>
          <div className="flex flex-row gap-2 pb-2">
            <Button
              label={favourited ? 'Favourited' : 'Favourite'}
              className="h-[30px] w-[74-px]"
              variant={favourited ? 'secondary' : 'default'}
              onClick={toggleFavourite}
            />
            {/* <Button
                variant="secondary"
                label="Chat"
                className="h-[30px] w-[74-px]"
                onClick={() => {
                  push(
                    `/chats/${chatRoomId}?username=${encodeURIComponent(user.username)}&userId=${encodeURIComponent(user.id)}&userAvatar=${encodeURIComponent(user.profileImageUrl || '')}`
                  );
                }}
              /> */}
          </div>
        </div>
        <div className="gap-[5px] flex flex-col pb-2">
          {user.role !== 'QUESTER' && (
            <p className="text-body-sm text-fg-muted-light dark:text-fg-muted-dark">
              {user.category}
            </p>
          )}
          {user.bio && <P className="pb-1 text-xs">{user.bio}</P>}
          {user.url && (
            <p className="text-xs font-bold text-fg-link">{user.url}</p>
          )}
        </div>
        {(user.socials?.spotify ||
          user.socials?.instagram ||
          user.socials?.snapchat ||
          user.socials?.youtube ||
          user.socials?.x ||
          user.socials?.tiktok) && (
          <p className="flex flex-row items-center gap-4 py-2">
            {user.socials?.spotify && (
              <a
                href={user.socials?.spotify || '#'}
                target="_blank"
                rel="noopener noreferrer"
              >
                <FaSpotify size={32} color="#2EBD59" />
              </a>
            )}
            {user.socials?.instagram && (
              <a
                href={user.socials?.instagram || '#'}
                target="_blank"
                rel="noopener noreferrer"
              >
                <IoLogoInstagram className="size-8" />
              </a>
            )}
            {user.socials?.snapchat && (
              <a
                href={user.socials?.snapchat || '#'}
                target="_blank"
                rel="noopener noreferrer"
              >
                <IoLogoSnapchat className="size-8" />
              </a>
            )}
            {user.socials?.tiktok && (
              <a
                href={user.socials?.tiktok || '#'}
                target="_blank"
                rel="noopener noreferrer"
              >
                <IoLogoTiktok size={32} color="#FF0000" className="size-8" />
              </a>
            )}
            {user.socials?.x && (
              <a
                href={user.socials?.x || '#'}
                target="_blank"
                rel="noopener noreferrer"
              >
                <FiX className="size-8" />
              </a>
            )}
            {user.socials?.youtube && (
              <a
                href={user.socials?.youtube || '#'}
                target="_blank"
                rel="noopener noreferrer"
              >
                <IoLogoYoutube size={32} color="#FF0000" />
              </a>
            )}
          </p>
        )}
        {user.role !== 'QUESTER' ? (
          <div className="flex flex-row rounded-full bg-bg-subtle-light p-0.5 dark:bg-bg-subtle-dark mb-4">
            <button
              onClick={() => setSelectedTab('events')}
              className={cn(
                'items-center flex-1 py-2 rounded-full bg-transparent',
                selectedTab === 'events' &&
                  'rounded-full bg-bg-surface-light dark:bg-bg-surface-dark'
              )}
            >
              <p
                className={cn(
                  'text-sm font-medium text-fg-muted-light dark:text-fg-muted-dark',
                  selectedTab === 'events' && 'text-fg-link dark:text-fg-link'
                )}
              >
                Events
              </p>
            </button>
            <button
              onClick={() => setSelectedTab('posts')}
              className={cn(
                'items-center flex-1 py-2 rounded-full bg-transparent',
                selectedTab === 'posts' &&
                  'rounded-full bg-bg-surface-light dark:bg-bg-surface-dark'
              )}
            >
              <p
                className={cn(
                  'text-sm font-medium text-fg-muted-light dark:text-fg-muted-dark',
                  selectedTab === 'posts' && 'text-fg-link dark:text-fg-link'
                )}
              >
                Posts
              </p>
            </button>
          </div>
        ) : (
          <div className="flex items-center justify-center rounded-full bg-bg-surface-light py-2 text-center dark:bg-bg-surface-dark mb-4">
            <p className="text-sm font-medium text-fg-link">Posts</p>
          </div>
        )}
        {isEventsTab && (
          <>
            {areEventsPending ? (
              <MyEventsLoading />
            ) : (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {events.length > 0 ? (
                  events.map((item) => (
                    <EventFavoriteCard
                      key={item.id}
                      {...item}
                      attendees={item.ticketsSold}
                    />
                  ))
                ) : (
                  <div className="col-span-full">
                    <EmptyState />
                  </div>
                )}
              </div>
            )}
          </>
        )}
        {isPostsTab && (
          <div className="flex flex-row flex-wrap justify-between gap-4">
            {true ? (
              <EmptyState />
            ) : (
              [].map((_, index) => (
                <button
                  key={index}
                  onClick={() => console.log(`Pressed image ${index}`)}
                >
                  <div className="mb-1 h-[231px] w-[117px]">
                    {/* <Image
                      src={'/images/feed_image.png'}
                      className="size-full rounded-sm"
                      alt="Feed image"
                    /> */}
                    <div className="absolute bottom-1.5 left-1.5 flex flex-row items-center">
                      <IoEyeOutline size={12} color="#fff" className="mr-1" />
                      <p className="text-xs font-bold text-white">12.4K</p>
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
}
