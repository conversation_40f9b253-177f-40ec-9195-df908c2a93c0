'use client';
import { ArrowUpRight } from 'lucide-react';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useRef } from 'react';
import html2canvas from 'html2canvas-pro';
import { useGetTransaction } from '@/api/transactions';
import { LoadingScreen } from '@/components/loaders';
import {
  Back,
  Button,
  CoinIcon,
  colors,
  DownloadIcon,
  FundIcon,
  H4,
  LiveIcon,
  MdBoldLabel,
  MdRegularLabel,
  semanticColors,
  SmRegularLabel,
  TicketIcon,
} from '@/components/ui';
import { StatusPill } from '@/components/ui/status-pill';
import { formatAmount, formatEnumLabel, toAmountInMajor } from '@/lib';
import { useTheme } from 'next-themes';
import { IoWalletOutline } from 'react-icons/io5';
import { FiArrowUpRight } from 'react-icons/fi';

export default function Transaction({ params }: { params: { id: string } }) {
  const ref = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const handleDownloadImage = async () => {
    if (!ref.current) return;
    const canvas = await html2canvas(ref.current);
    const link = document.createElement('a');
    link.download = 'transaction.png';
    link.href = canvas.toDataURL();
    link.click();
  };

  const txId = params.id;
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const { data: transaction, isLoading } = useGetTransaction({
    variables: { id: txId },
    enabled: !!txId,
  });

  if (isLoading) return <LoadingScreen />;
  if (!transaction) return null;

  const {
    songRequest,
    djSession,
    ticket,
    amount,
    currency,
    category,
    createdAt,
    type,
    status,
    id,
    meta,
  } = transaction;

  const isOnlineTransaction =
    category === 'FUND_WALLET' ||
    (category === 'EVENT_TICKET_PURCHASE' && meta?.paymentMethod === 'DIRECT');

  const IconComponent = () => {
    const iconColor = isDark ? colors.brand[40] : colors.brand[70];

    switch (category) {
      case 'FUND_WALLET':
        return <IoWalletOutline size={32} color={iconColor} />;
      case 'SONG_REQUEST':
        return <LiveIcon height={32} width={32} color={iconColor} />;
      case 'WITHDRAW_WALLET':
        return <FiArrowUpRight size={32} color={iconColor} />;
      case 'EVENT_TICKET_PURCHASE':
      case 'EVENT_TICKET_EARNINGS':
        return <TicketIcon height={32} width={32} color={iconColor} />;
      case 'DJ_SESSION_EARNINGS':
        return <LiveIcon height={32} width={32} color={iconColor} />;
      default:
        return <FundIcon height={32} width={32} color={iconColor} />;
    }
  };

  const items = {
    Type: formatEnumLabel(type || ''),
    Date: createdAt ? format(new Date(createdAt), 'd LLL, yyyy') : '',
    Time: createdAt ? format(new Date(createdAt), 'p') : '',
    Status: status,
  };

  const hasBreakdown = [
    'SONG_REQUEST',
    'DJ_SESSION_EARNINGS',
    'EVENT_TICKET_PURCHASE',
    'EVENT_TICKET_EARNINGS',
  ].includes(category);

  const majorAmount = toAmountInMajor(amount);
  const breakdownItems: { label: string; value?: string }[] = [];

  if (hasBreakdown) {
    switch (category) {
      case 'SONG_REQUEST': {
        const isBoosted = !!songRequest?.isBoosted;
        const hasShoutout = !!songRequest?.shoutOutTo?.trim();
        const hasSongRequest = !!songRequest?.song.title.trim();
        const songRequestFee = hasSongRequest
          ? toAmountInMajor(songRequest?.djSession?.cost ?? 0)
          : 0;
        const shoutOutFee = hasShoutout
          ? toAmountInMajor(songRequest?.djSession?.shoutoutCost ?? 0)
          : 0;

        const baseFee = songRequestFee + shoutOutFee;

        const boostFee = isBoosted ? baseFee : 0;

        breakdownItems.push(
          { label: 'Session', value: songRequest?.djSession?.title },
          {
            label: 'Creator',
            value: songRequest?.djSession?.dj.username,
          }
        );

        if (hasSongRequest) {
          breakdownItems.push({
            label: 'Song',
            value: songRequest?.song?.title,
          });
        }

        if (hasShoutout) {
          breakdownItems.push({
            label: 'Shoutout To',
            value: songRequest?.shoutOutTo || '',
          });
        }

        if (songRequestFee) {
          breakdownItems.push({
            label: 'Request fee',
            value: formatAmount(songRequestFee, undefined, currency),
          });
        }

        if (shoutOutFee) {
          breakdownItems.push({
            label: 'Shoutout fee',
            value: formatAmount(shoutOutFee, undefined, currency),
          });
        }

        if (isBoosted) {
          breakdownItems.push({
            label: 'Boost fee',
            value: formatAmount(boostFee, undefined, currency),
          });
        }

        break;
      }

      case 'DJ_SESSION_EARNINGS': {
        breakdownItems.push({
          label: 'Session',
          value: djSession?.title,
        });

        const {
          boostedSongRequests,
          shoutOutRequests,
          shoutoutCost,
          cost,
          playedSongRequestsWithShoutout,
        } = djSession || {};
        const sessionCostInMajor = toAmountInMajor(cost || 0);
        const shoutoutCostInMajor = toAmountInMajor(shoutoutCost || 0);
        if (playedSongRequestsWithShoutout) {
          breakdownItems.push({
            label: `Song Requests x${playedSongRequestsWithShoutout}`,
            value: formatAmount(
              sessionCostInMajor * playedSongRequestsWithShoutout,
              undefined,
              currency
            ),
          });
        }

        if (boostedSongRequests) {
          breakdownItems.push({
            label: `Boosted Requests x${boostedSongRequests}`,
            value: formatAmount(
              sessionCostInMajor * boostedSongRequests,
              undefined,
              currency
            ),
          });
        }

        if (shoutOutRequests) {
          breakdownItems.push({
            label: `Shoutouts x${shoutOutRequests}`,
            value: formatAmount(
              shoutoutCostInMajor * shoutOutRequests,
              undefined,
              currency
            ),
          });
        }

        break;
      }
      case 'EVENT_TICKET_PURCHASE': {
        breakdownItems.push({
          label: 'Event',
          value: ticket?.event?.title ?? 'N/A',
        });

        const breakdown = ticket?.meta?.breakdown || [];
        const aggregatedBreakdown = Array.from(
          breakdown.reduce((map, { type, quantity, cost }) => {
            if (!map.has(type)) {
              map.set(type, { type, quantity, cost });
            } else {
              const existing = map.get(type)!;
              existing.quantity += quantity;
              existing.cost += cost;
            }
            return map;
          }, new Map<string, { type: string; quantity: number; cost: number }>())
        ).map(([, value]) => value);

        aggregatedBreakdown?.forEach(({ type, quantity, cost }) => {
          const costInMajor = toAmountInMajor(cost);

          breakdownItems.push({
            label: `${type} × ${quantity}`,
            value: formatAmount(costInMajor * quantity, undefined, currency),
          });
        });

        breakdownItems.push({
          label: 'Service fee',
          value: formatAmount(
            toAmountInMajor(ticket?.serviceFee ?? 0),
            undefined,
            currency
          ),
        });

        if (ticket?.discount) {
          breakdownItems.push({
            label: 'Discount',
            value: `- ${formatAmount(
              toAmountInMajor(ticket?.discount.amount ?? 0),
              undefined,
              currency
            )}`,
          });
        }

        break;
      }

      case 'EVENT_TICKET_EARNINGS': {
        breakdownItems.push({
          label: 'Event',
          value: meta.eventTitle ?? 'N/A',
        });

        break;
      }
    }
  }

  return (
    <div className="flex-1">
      <div className="flex-1 flex flex-col gap-4 p-4">
        <div className="h-16 flex flex-row items-center justify-between px-2">
          <Back />
          <button
            className="size-8 flex items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark"
            onClick={handleDownloadImage}
          >
            <DownloadIcon
              color={
                isDark
                  ? semanticColors.accent.bold.dark
                  : semanticColors.accent.bold.light
              }
            />
          </button>
        </div>
        <div
          ref={ref}
          style={{
            backgroundColor: isDark
              ? colors.grey[100]
              : semanticColors.bg.canvas.light,
            rowGap: 16,
            flex: 1,
          }}
          className="max-w-md mx-auto"
        >
          <div className="h-20 flex flex-row items-center gap-4 px-2">
            <div className="size-16 flex items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark">
              <IconComponent />
            </div>

            <div className="flex-1 flex flex-row items-center justify-between">
              <div className="flex flex-col gap-2">
                <H4 className="text-fg-on-contrast-dark dark:text-fg-on-contrast-light">
                  {category === 'DJ_SESSION_EARNINGS'
                    ? 'Live Session Earnings'
                    : category === 'SONG_REQUEST'
                      ? 'Live Request'
                      : category === 'WITHDRAW_WALLET'
                        ? 'Wallet Withdrawal'
                        : category === 'FUND_WALLET'
                          ? 'Wallet Funding'
                          : formatEnumLabel(category) || ''}
                </H4>
                {amount !== undefined && (
                  <div className="flex flex-row items-center gap-0.5">
                    <CoinIcon color={isDark ? colors.white : colors.black} />
                    <MdRegularLabel className="text-fg-on-contrast-dark dark:text-fg-on-contrast-light">
                      {formatAmount(majorAmount, undefined, currency)}
                    </MdRegularLabel>
                  </div>
                )}
              </div>

              {category !== 'WITHDRAW_WALLET' && (
                <div className="h-8 flex flex-row items-center gap-2 rounded-full bg-accent-subtle-light pl-3 pr-4 dark:bg-accent-subtle-dark">
                  <ArrowUpRight />
                  <MdBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
                    {isOnlineTransaction ? 'Online' : 'Wallet'}
                  </MdBoldLabel>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-row gap-2.5 px-2">
            <MdRegularLabel className="text-fg-base-light dark:text-fg-base-dark">
              Transaction ID:
            </MdRegularLabel>
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark truncate">
              {id}
            </SmRegularLabel>
          </div>

          <div className="flex flex-col gap-4 p-4">
            {Object.entries(items).map(([key, value]) => (
              <div
                key={key}
                className="flex flex-row items-center justify-between gap-2"
              >
                <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                  {key}
                </SmRegularLabel>
                {key === 'Status' ? (
                  <StatusPill status={value as any} />
                ) : (
                  <MdRegularLabel className="self-end text-fg-base-light dark:text-fg-base-dark truncate">
                    {value}
                  </MdRegularLabel>
                )}
              </div>
            ))}
          </div>

          {hasBreakdown && (
            <div className="px-2">
              <MdRegularLabel>Breakdown</MdRegularLabel>
              <div className="flex flex-col gap-4 px-2 py-4">
                {breakdownItems.map(({ label, value }, index) => (
                  <div key={index} className="flex flex-row items-center gap-2">
                    <SmRegularLabel className="flex-1 text-left text-fg-muted-light dark:text-fg-muted-dark truncate">
                      {String(label)}
                    </SmRegularLabel>

                    <MdRegularLabel className="flex-1 text-right truncate">
                      {String(value ?? '')}
                    </MdRegularLabel>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="mt-auto flex items-center justify-center pt-4">
          <Button
            label="Contact support"
            variant="ghost"
            textClassName="!text-[#7257FF]"
            onPress={() => router.push(`/report-problem?trxId=${txId}`)}
          />
        </div>
      </div>
    </div>
  );
}
