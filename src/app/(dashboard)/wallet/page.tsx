'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Eye, EyeOff, Banknote, Plus, ArrowUpRight } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/dialogs';

import { useGetTransactionHistory } from '@/api';
import { TransactionHistoryItem } from '@/components/wallet';
import { EmptyState } from '@/components/ui';
import {
  Button,
  colors,
  H1,
  MdBoldLabel,
  MdRegularLabel,
  XsRegularLabel,
  PasswordIcon,
  semanticColors,
  Image,
} from '@/components/ui';
import {
  formatAmount,
  maskLast,
  toAmountInMajor,
  useLoggedInUser,
  useVisibility,
} from '@/lib';
import { LoadingScreen } from '@/components/loaders';
import { useTheme } from 'next-themes';

export default function Wallet() {
  const router = useRouter();
  const { toggleVisibility, isOpen } = useVisibility();

  const { isLoading: userLoading, data: currentUser } = useLoggedInUser();
  const isCreator = currentUser?.role !== 'QUESTER';
  const bankDetails = currentUser?.idVerificationData?.bank;

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetTransactionHistory({
      variables: {
        id: currentUser?.id || '',
        currency: 'NGN',
        take: 10,
      },
      enabled: !!currentUser?.id,
    });

  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const allTransactions = React.useMemo(() => {
    return data?.pages?.flatMap((page) => page.transactions) || [];
  }, [data?.pages]);

  const [bankModalOpen, setBankModalOpen] = React.useState(false);

  if (isLoading || userLoading) return <LoadingScreen />;

  return (
    <div className="flex flex-col gap-y-4">
      <div className="space-y-4">
        <div className="w-full lg:w-5/12">
          <div className="relative h-[208px] rounded-lg p-4">
            <Image
              className="size-full rounded-lg"
              fill
              src={
                isDark
                  ? '/images/gradient-bg/wallet-gradient.png'
                  : '/images/gradient-bg/wallet-gradient-light.png'
              }
              content="cover"
              alt=""
            />
            <div className="absolute inset-0 flex flex-col justify-between p-4">
              <div className="space-y-1">
                <XsRegularLabel className="text-white dark:text-white">
                  Balance
                </XsRegularLabel>
                <div className="flex flex-row items-center gap-4">
                  <H1 className="text-white dark:text-white">
                    {!isOpen
                      ? formatAmount(
                          toAmountInMajor(currentUser?.walletBalance || 0)
                        )
                      : '*****'}
                  </H1>
                  <button onClick={toggleVisibility}>
                    {isOpen ? (
                      <EyeOff className="size-6" />
                    ) : (
                      <Eye className="size-6" />
                    )}
                  </button>
                </div>
              </div>
              <div className="flex flex-row gap-x-3">
                <WalletButton type="topup" />
                {isCreator && <WalletButton type="withdraw" />}
              </div>
            </div>
          </div>
        </div>

        {isCreator && (
          <button
            className="h-[53px] flex flex-row items-center gap-4 rounded-md bg-bg-subtle-light px-4 dark:bg-bg-subtle-dark w-full lg:w-5/12 cursor-pointer"
            onClick={() => router.push('/wallet/pin')}
          >
            <PasswordIcon color={colors.brand[60]} />
            <MdRegularLabel className="flex-1">Transaction pin</MdRegularLabel>
          </button>
        )}

        <div className="flex flex-col flex-1 border-t gap-y-[1px]">
          {allTransactions.length === 0 && !isFetchingNextPage ? (
            <EmptyState />
          ) : (
            <ul className="divide-y divide-grey-20 dark:divide-grey-80 flex flex-col flex-1">
              {allTransactions.map((item) => (
                <li key={item.id}>
                  <TransactionHistoryItem item={item} />
                </li>
              ))}
            </ul>
          )}
          {hasNextPage && (
            <div className="flex justify-center py-4">
              <Button
                label={isFetchingNextPage ? 'Loading...' : 'Load more'}
                onClick={() => fetchNextPage()}
              />
            </div>
          )}
        </div>
      </div>

      <Dialog open={bankModalOpen} onOpenChange={setBankModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Account Details</DialogTitle>
          </DialogHeader>
          {bankDetails && (
            <div className="flex items-center gap-3 rounded-md border px-4 py-3">
              <div className="w-8 h-8 flex items-center justify-center bg-gray-200 dark:bg-gray-700">
                <Banknote className="w-5 h-5" />
              </div>
              <MdRegularLabel className="flex-1">
                {bankDetails.bankName}{' '}
                {maskLast(bankDetails.accountNumber, 4, '*')}
              </MdRegularLabel>
            </div>
          )}
          <Button
            label={bankDetails ? 'Update bank details' : 'Add new bank'}
            variant="secondary"
            onClick={() => {
              setBankModalOpen(false);
              router.push('/profile/withdraw/bank');
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

export const WalletButton: React.FC<{ type: 'topup' | 'withdraw' }> = ({
  type,
}) => {
  const router = useRouter();
  const isTopup = type === 'topup';
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  return (
    <button
      className="flex h-8 items-center justify-center gap-2 rounded-full bg-brand-20 px-4 pl-3 dark:bg-brand-90 cursor-pointer"
      onClick={() =>
        isTopup ? router.push('/wallet/topup') : router.push('/wallet/withdraw')
      }
    >
      {isTopup ? (
        <Plus
          className="size-4"
          color={isDark ? colors.brand[40] : colors.brand[70]}
        />
      ) : (
        <ArrowUpRight
          className="size-4"
          color={
            isDark
              ? semanticColors.accent.bold.dark
              : semanticColors.accent.bold.light
          }
        />
      )}
      <MdBoldLabel>{isTopup ? 'Top up' : 'Withdraw'}</MdBoldLabel>
    </button>
  );
};
