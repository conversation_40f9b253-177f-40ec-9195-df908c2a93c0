'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

import { useCreatePin, useValidatePin } from '@/api';
import { AuthLayout } from '@/components/layouts';
import { Button, ChevronLeft, colors, H3 } from '@/components/ui';
import { cn, useLoggedInUser } from '@/lib';

export default function Pin() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [pin, setPin] = useState('');
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [step, setStep] = useState(1);
  const [error, setError] = useState('');

  const { data: user } = useLoggedInUser();
  const userHasPin = !!user?.pin;

  const { mutate: createPin, isPending: creatingPin } = useCreatePin();
  const { mutate: validatePin, isPending: validatingPin } = useValidatePin();

  const onCreatePin = (finalPin: string) => {
    createPin(
      { pin: finalPin },
      {
        onSuccess: () => {
          toast.success('PIN saved successfully');
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          router.back();
        },
        onError: (error: any) => {
          setError(error.message);
          toast.error(error.message);
        },
      }
    );
  };

  const onValidateOldPin = () => {
    validatePin(
      { pin },
      {
        onSuccess: (isValid: boolean) => {
          if (isValid) {
            setStep(2);
            setError('');
            setPin('');
          } else {
            setError('Invalid PIN');
            setPin('');
            toast.error('Invalid PIN');
          }
        },
        onError: (error: any) => {
          setError(error.message);
          setPin('');
          toast.error(error.message);
        },
      }
    );
  };

  const handleKeyPress = (key: string) => {
    const getCurrentInput = () => {
      if (!userHasPin) return step === 1 ? pin : confirmPin;
      if (step === 1) return pin;
      if (step === 2) return newPin;
      return confirmPin;
    };

    const currentInput = getCurrentInput();

    if (key === 'delete') {
      if (!userHasPin) {
        if (step === 1) {
          setPin((prev) => prev.slice(0, -1));
        } else {
          setConfirmPin((prev) => prev.slice(0, -1));
        }
      } else {
        if (step === 1) setPin((prev) => prev.slice(0, -1));
        if (step === 2) setNewPin((prev) => prev.slice(0, -1));
        if (step === 3) setConfirmPin((prev) => prev.slice(0, -1));
      }
      setError('');
    } else if (currentInput.length < 4) {
      if (!userHasPin) {
        if (step === 1) {
          setPin((prev) => prev + key);
        } else {
          setConfirmPin((prev) => prev + key);
        }
      } else {
        if (step === 1) setPin((prev) => prev + key);
        if (step === 2) setNewPin((prev) => prev + key);
        if (step === 3) setConfirmPin((prev) => prev + key);
      }
      setError('');
    }
  };

  const handleContinue = () => {
    if (!userHasPin) {
      if (step === 1 && pin.length === 4) {
        setStep(2);
        setConfirmPin('');
      } else if (step === 2 && confirmPin.length === 4) {
        if (pin === confirmPin) {
          onCreatePin(confirmPin);
        } else {
          setError('PINs do not match. Please try again.');
          setConfirmPin('');
        }
      }
    } else {
      if (step === 1 && pin.length === 4) {
        onValidateOldPin();
      } else if (step === 2 && newPin.length === 4) {
        setStep(3);
        setConfirmPin('');
      } else if (step === 3 && confirmPin.length === 4) {
        if (newPin === confirmPin) {
          if (pin === confirmPin) {
            setError('New PIN cannot be the same as the old one.');
            return;
          }
          onCreatePin(confirmPin);
        } else {
          setError('PINs do not match. Please try again.');
          setConfirmPin('');
        }
      }
    }
  };

  const handleBackPress = () => {
    if (step === 1) router.back();
    else {
      setStep((prev) => prev - 1);
      setError('');
    }
  };

  const renderKey = (value: string) => {
    if (!value) return <div key="spacer" className="w-14 h-14" />;

    const isDelete = value === 'delete';
    const getCurrentInput = () => {
      if (!userHasPin) return step === 1 ? pin : confirmPin;
      if (step === 1) return pin;
      if (step === 2) return newPin;
      return confirmPin;
    };
    const currentInput = getCurrentInput();
    const isDisabled = isDelete && currentInput.length === 0;

    return (
      <button
        key={value}
        onClick={() => !isDisabled && handleKeyPress(value)}
        className={cn(
          'flex items-center justify-center w-14 h-14 m-1 rounded-full border-2 transition-colors duration-200 ease-in-out',
          isDisabled && 'opacity-50 cursor-not-allowed',
          !isDisabled &&
            'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800'
        )}
      >
        {isDelete ? (
          <ChevronLeft color={isDisabled ? colors.grey[70] : undefined} />
        ) : (
          <H3>{value}</H3>
        )}
      </button>
    );
  };

  const getTitles = () => {
    if (!userHasPin) {
      return step === 1
        ? {
            title: 'Create transaction PIN',
            sub: 'You will need this to authorize transactions',
          }
        : {
            title: 'Confirm transaction PIN',
            sub: 'Re-enter your PIN to confirm',
          };
    }
    if (step === 1)
      return { title: 'Update transaction PIN', sub: 'Enter your old PIN' };
    if (step === 2)
      return { title: 'New transaction PIN', sub: 'Enter your new PIN' };
    return {
      title: 'Confirm new PIN',
      sub: 'Re-enter your new PIN to confirm',
    };
  };

  const getCurrentInput = () => {
    if (!userHasPin) return step === 1 ? pin : confirmPin;
    if (step === 1) return pin;
    if (step === 2) return newPin;
    return confirmPin;
  };

  const currentInput = getCurrentInput();

  return (
    <AuthLayout
      showBackButton={step > 1}
      title={getTitles().title}
      subTitle={getTitles().sub}
      onBackPress={handleBackPress}
    >
      <div className="flex flex-col items-center gap-8 justify-between py-8 h-full">
        <div className="flex flex-col items-center">
          <div className="flex flex-row gap-4">
            {[0, 1, 2, 3].map((index) => {
              const isFilled = index < currentInput.length;
              return (
                <div
                  key={index}
                  className={`w-14 h-14 flex items-center justify-center rounded-full border-2 ${
                    isFilled
                      ? 'border-accent-moderate dark:border-accent-moderate'
                      : 'border-grey-30 dark:border-grey-80'
                  }`}
                >
                  {isFilled && (
                    <div className="size-2 rounded-full bg-black dark:bg-white" />
                  )}
                </div>
              );
            })}
          </div>
          {error && <p className="my-4 text-center text-red-500">{error}</p>}
        </div>

        <Button
          label="Continue"
          className="w-full max-w-[240px] mx-auto"
          disabled={currentInput.length < 4 || creatingPin || validatingPin}
          loading={creatingPin || validatingPin}
          onClick={handleContinue}
        />

        <div className="grid grid-cols-3 gap-6 place-items-center">
          {['1', '2', '3', '4', '5', '6', '7', '8', '9', '', '0', 'delete'].map(
            renderKey
          )}
        </div>
      </div>
    </AuthLayout>
  );
}
