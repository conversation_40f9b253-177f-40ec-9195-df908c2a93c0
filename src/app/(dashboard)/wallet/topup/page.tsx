'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';
import CurrencyInput from 'react-currency-input-field';

import { Button, H1, Tiny } from '@/components/ui';
import { useTopup, toAmountInMinor } from '@/lib';

export default function TopupWallet() {
  const [value, setValue] = useState<string>('');

  const MAX_AMOUNT = 10000000;
  const MIN_AMOUNT = 1000;

  const numericAmount = Number(value || 0);

  const { topup, initializeTransaction, initializing } = useTopup({
    amount: numericAmount,
  });

  const handleTopup = () => {
    if (!value || numericAmount < MIN_AMOUNT) {
      toast.error(`Minimum topup amount is ₦${MIN_AMOUNT}`);
      return;
    }
    if (numericAmount > MAX_AMOUNT) {
      toast.error(`Maximum top-up amount is ₦${MAX_AMOUNT}`);
      return;
    }

    initializeTransaction(
      {
        amount: toAmountInMinor(numericAmount),
        country: 'NIGERIA',
      },
      {
        onSuccess: (data) => {
          topup({ reference: data.id });
        },
        onError: (error: any) => toast.error(error.message),
      }
    );
  };

  return (
    <div className="flex min-h-screen flex-col bg-bg-canvas dark:bg-grey-100 pb-10">
      <H1 className="px-4 pb-4">Top-up wallet</H1>

      <div className="flex justify-center px-4">
        <div className="flex w-full max-w-md flex-col gap-14">
          <div className="flex flex-col h-[70px] items-center justify-between">
            <CurrencyInput
              value={value}
              decimalsLimit={2}
              decimalSeparator="."
              groupSeparator=","
              autoFocus
              max={MAX_AMOUNT}
              min={MIN_AMOUNT}
              prefix="₦"
              allowNegativeValue={false}
              inputMode="decimal"
              onValueChange={(val) => {
                if (!val) {
                  setValue('');
                  return;
                }

                const numericValue = Number(val);

                if (numericValue > MAX_AMOUNT) {
                  setValue(MAX_AMOUNT.toString());
                } else {
                  setValue(val);
                }
              }}
              onBlur={() => {
                const numericValue = Number(value || 0);

                if (numericValue < MIN_AMOUNT) {
                  setValue(MIN_AMOUNT.toString());
                }
              }}
              className="h-auto font-aeonik-black text-center text-2xl leading-[150%] font-bold text-grey-100 dark:text-white 
          placeholder:text-grey-50 dark:placeholder:text-grey-60 
          placeholder:text-2xl placeholder:leading-[150%] placeholder:font-aeonik-black placeholder:font-bold focus:border-0 focus:outline-none"
              placeholder="₦0"
            />
            <Tiny className="text-grey-60 dark:text-grey-50">
              Minimum: ₦{MIN_AMOUNT}
            </Tiny>
          </div>

          <Button
            label="Continue"
            disabled={
              !value ||
              numericAmount < MIN_AMOUNT ||
              numericAmount > MAX_AMOUNT ||
              initializing
            }
            loading={initializing}
            onClick={handleTopup}
          />
        </div>
      </div>
    </div>
  );
}
