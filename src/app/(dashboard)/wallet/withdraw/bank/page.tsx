'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { FormProvider } from 'react-hook-form';
import { toast } from 'react-hot-toast';

import { useValidatePin, useSavePayoutDetails, getQueryClient } from '@/api';
import { BankForm } from '@/components/forms';
import { AuthLayout } from '@/components/layouts';
import { Button, ChevronLeft, colors, H2, H3, Modal } from '@/components/ui';
import { cn, useBankSetupForm, useLoggedInUser } from '@/lib';

function PinInput({
  pin,
  onChange,
  onConfirm,
}: {
  pin: string;
  onChange: (key: string) => void;
  onConfirm: (pin: string) => void;
}) {
  const handleKeyPress = (key: string) => {
    if (key === 'delete') {
      onChange(pin.slice(0, -1));
    } else if (pin.length < 4) {
      const newPin = pin + key;
      onChange(newPin);
      if (newPin.length === 4) onConfirm(newPin);
    }
  };

  const renderKey = (value: string) => {
    const isDelete = value === 'delete';
    const isDisabled = isDelete && pin.length === 0;

    return (
      <button
        key={value}
        onClick={() => !isDisabled && handleKeyPress(value)}
        className={cn(
          'items-center justify-center opacity-70',
          isDisabled && 'opacity-50'
        )}
        disabled={isDisabled}
      >
        {isDelete ? (
          <ChevronLeft color={isDisabled ? colors.grey[70] : undefined} />
        ) : (
          <H3 className="text-accent-moderate dark:text-accent-moderate">
            {value}
          </H3>
        )}
      </button>
    );
  };

  return (
    <div className="min-h-[478px] flex flex-col items-center gap-6 px-4 pt-6">
      <H2>Enter your transaction PIN</H2>
      <div className="flex flex-row gap-2">
        {[0, 1, 2, 3].map((index) => (
          <div
            key={index}
            className={cn(
              'flex size-14 items-center justify-center rounded-full border-2',
              index < pin.length
                ? 'border-accent-moderate dark:border-accent-moderate'
                : 'border-grey-20 dark:border-grey-80'
            )}
          >
            {index < pin.length && (
              <div className="size-1 rounded-full bg-black dark:bg-white" />
            )}
          </div>
        ))}
      </div>

      <div className="w-full flex flex-col gap-16">
        {[['1', '2', '3'], ['4', '5', '6'], ['7', '8', '9'], ['0']].map(
          (row, idx) => (
            <div key={idx} className="flex flex-row justify-around">
              {idx === 3 && <div className="size-7" />}
              {row.map(renderKey)}
              {idx === 3 && renderKey('delete')}
            </div>
          )
        )}
      </div>
    </div>
  );
}

export default function Bank() {
  const router = useRouter();
  const queryClient = getQueryClient();
  const { data: user } = useLoggedInUser();
  const hasPinCreated = !!user?.pin;

  const [pin, setPin] = useState('');
  const [modalOpen, setModalOpen] = useState(false);

  const { mutate: validatePin } = useValidatePin();
  const { formMethods, hasExistingBankDetails } = useBankSetupForm();
  const { mutate: addBankDetails, isPending } = useSavePayoutDetails();

  const acctName = formMethods.watch('accountName');

  const handleConfirmPin = (pin: string) => {
    validatePin(
      { pin },
      {
        onSuccess: (isValid) => {
          if (isValid) handleAddBankDetails();
          else toast.error('Invalid Pin');
          setPin('');
        },
        onError: (err) => {
          toast.error(err.message);
          setPin('');
        },
      }
    );
  };

  const handleAddBankDetails = () => {
    const payload = formMethods.getValues();
    const isDirty = formMethods.getFieldState('accountNumber').isDirty;

    if (isDirty) {
      addBankDetails(payload, {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          toast.success('Bank details added successfully');
          router.back();
        },
        onError: (err) => toast.error(err.message),
      });
    } else router.back();
  };

  const handleContinue = () => {
    if (hasPinCreated) setModalOpen(true);
    else {
      toast.custom('You need to set up a PIN to continue.');
      router.push('/profile/pin');
    }
  };

  return (
    <AuthLayout
      title={`${hasExistingBankDetails ? 'Update' : 'Add'} Bank Details`}
      subTitle={`${hasExistingBankDetails ? 'Modify' : 'Enter'} your bank account details`}
      showBackButton={false}
      footer={
        <Button
          label="Continue"
          className="my-4"
          loading={isPending}
          disabled={!formMethods.formState.isValid || !acctName || isPending}
          onPress={handleContinue}
        />
      }
    >
      <FormProvider {...formMethods}>
        <BankForm />
      </FormProvider>

      <Modal isOpen={modalOpen} onClose={() => setModalOpen(false)}>
        <PinInput pin={pin} onChange={setPin} onConfirm={handleConfirmPin} />
      </Modal>
    </AuthLayout>
  );
}
