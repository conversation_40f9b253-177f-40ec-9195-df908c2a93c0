'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { BareLayout } from '@/components/layouts';
import {
  Button,
  Image,
  MdRegularLabel,
  Tiny,
  XsBoldLabel,
  XsRegularLabel,
} from '@/components/ui';
import {
  formatAmount,
  formatCurrency,
  formatNumber,
  maskLast,
  useLoggedInUser,
  useWithdrawalSummary,
} from '@/lib';
import CurrencyInput from 'react-currency-input-field';

export default function Withdraw() {
  const router = useRouter();
  const { data: user } = useLoggedInUser();
  const bankDetails = user?.idVerificationData?.bank;

  const MAX_AMOUNT = ********;
  const MIN_AMOUNT = 10000;

  const [value, setValue] = useState<string>('');

  const numericAmount = Number(value || 0);

  const { summaryItems, total } = useWithdrawalSummary({
    amount: numericAmount,
    feeRate: 0.01,
  });

  return (
    <BareLayout title="Withdraw">
      <div className="flex items-center justify-center">
        <div className="w-full max-w-md mx-auto px-4 flex flex-col">
          <div className="flex flex-row items-center gap-4 rounded-md bg-bg-subtle-light px-4 py-2 dark:bg-bg-subtle-dark">
            <div className="flex flex-row gap-1">
              <Image
                src={`https://flagcdn.com/128x96/${
                  user?.wallet?.countryCode ?? 'ng'
                }.png`}
                alt="Country flag"
                width={24}
                height={24}
                className="size-6 rounded-xl"
              />
              <MdRegularLabel>NGN</MdRegularLabel>
            </div>
            <div className="flex flex-row gap-1">
              <XsRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                Wallet balance
              </XsRegularLabel>
              <MdRegularLabel>
                {formatNumber(user?.walletBalance)}
              </MdRegularLabel>
            </div>
          </div>

          <div className="flex flex-col gap-6">
            <div className="mt-10 flex h-[70px] flex-col items-center justify-between">
              <CurrencyInput
                value={value}
                decimalsLimit={2}
                groupSeparator=","
                decimalSeparator="."
                prefix="₦"
                allowNegativeValue={false}
                inputMode="decimal"
                className="w-full text-center text-2xl font-aeonik-black font-bold text-grey-100 dark:text-white leading-[120%] placeholder:text-grey-50 dark:placeholder:text-grey-60 placeholder:leading-[120%] placeholder:font-aeonik-black placeholder:font-bold placeholder:text-xl focus:border-0 focus:outline-none"
                placeholder="₦0"
                onValueChange={(val) => {
                  if (!val) {
                    setValue('');
                    return;
                  }

                  const numericValue = Number(val);

                  if (numericValue > MAX_AMOUNT) {
                    setValue(MAX_AMOUNT.toString());
                  } else {
                    setValue(val);
                  }
                }}
                onBlur={() => {
                  const numericValue = Number(value || 0);

                  if (numericValue < MIN_AMOUNT) {
                    setValue(MIN_AMOUNT.toString());
                  }
                }}
              />
              <Tiny className="text-grey-60 dark:text-grey-50">
                Minimum withdrawal: ₦{formatCurrency(MIN_AMOUNT)}
              </Tiny>
            </div>

            <div className="flex flex-col gap-2">
              {summaryItems.map(({ label, value, key }) => (
                <div
                  className="flex flex-row items-center justify-between gap-2.5"
                  key={key}
                >
                  <XsRegularLabel className="flex-1 text-fg-muted-light dark:text-fg-muted-dark">
                    {label}
                  </XsRegularLabel>
                  <XsRegularLabel>{formatAmount(value)}</XsRegularLabel>
                </div>
              ))}
            </div>

            {bankDetails && (
              <div className="flex flex-row justify-between gap-4 rounded-md border border-border-muted-light px-4 py-2 dark:border-border-muted-dark">
                <div className="flex flex-row gap-1">
                  <XsRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                    Withdrawing to
                  </XsRegularLabel>
                  <MdRegularLabel>
                    {bankDetails.bankName}{' '}
                    {maskLast(bankDetails.accountNumber, 4, '*')}
                  </MdRegularLabel>
                </div>
                <button
                  className="flex justify-center px-4 py-2 cursor-pointer"
                  onClick={() => router.push('/wallet/withdraw/bank')}
                >
                  <XsBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
                    Change
                  </XsBoldLabel>
                </button>
              </div>
            )}
          </div>

          <Button
            className="mt-[18px] w-full"
            label="Withdraw"
            disabled={!value || total < MIN_AMOUNT || total > MAX_AMOUNT}
            onClick={() => {
              if (!bankDetails) {
                router.push('/wallet/withdraw/bank');
              } else {
                router.push(`/wallet/withdraw/review?amount=${value}`);
              }
            }}
          />
        </div>
      </div>
    </BareLayout>
  );
}
