'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useSearchParams, useRouter } from 'next/navigation';
import React from 'react';
import { toast } from 'react-hot-toast';

import { useWithdrawalRequest } from '@/api/transactions';
import { useValidatePin } from '@/api';
import { BareLayout } from '@/components/layouts';
import {
  Button,
  ChevronLeft,
  colors,
  H2,
  H3,
  Modal,
  semanticColors,
  SmRegularLabel,
  WarningTriangle,
} from '@/components/ui';
import {
  cn,
  formatAmount,
  toAmountInMinor,
  useLoggedInUser,
  useWithdrawalSummary,
} from '@/lib';
import { useTheme } from 'next-themes';

export default function ReviewWithdrawal() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const params = useSearchParams();
  const amount = params.get('amount') || '';
  const { data: user } = useLoggedInUser();
  const bankDetails = user?.idVerificationData?.bank;
  const hasPinCreated = !!user?.pin;
  const { resolvedTheme } = useTheme();

  const isDark = resolvedTheme === 'dark';

  const [modalOpen, setModalOpen] = React.useState(false);

  const { summaryItems, withdrawalAmount, total } = useWithdrawalSummary({
    amount: Number(amount || 0),
    feeRate: 0.01,
  });

  const [pin, setPin] = React.useState('');
  const { mutate: validatePin, isPending: validatingPin } = useValidatePin();

  const onConfirmPin = (pin: string) => {
    validatePin(
      { pin },
      {
        onSuccess: (isValid) => {
          if (isValid) {
            handleWithdraw();
          } else {
            toast.error('Invalid Pin');
          }
          setPin('');
        },
        onError: (error) => {
          toast.error(error.message);
          setPin('');
        },
      }
    );
  };

  const { mutate: submitWithdrawaRequest, isPending: isSubmittingRequest } =
    useWithdrawalRequest();

  const handleWithdraw = () => {
    if (Number(total) > 10000000) {
      toast.error('Maximum withdrawal amount is ₦10,000,000');
      return;
    }
    submitWithdrawaRequest(
      { amount: toAmountInMinor(total) },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          queryClient.invalidateQueries({
            queryKey: ['getTransactionHistory'],
          });
          toast.success('Transaction Successful');
          router.push('/wallet');
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  const handleKeyPress = (key: string) => {
    if (key === 'delete') {
      setPin((prev) => prev.slice(0, -1));
    } else if (pin.length < 4) {
      const newPin = pin + key;
      setPin(newPin);
      if (newPin.length === 4) {
        setModalOpen(false);
        onConfirmPin(newPin);
      }
    }
  };

  const renderKey = (value: string) => {
    const isDelete = value === 'delete';
    const isDisabled = isDelete && pin.length === 0;

    return (
      <button
        key={value}
        onClick={() => !isDisabled && handleKeyPress(value)}
        className={cn(
          'items-center justify-center',
          isDisabled && 'opacity-50'
        )}
        disabled={isDisabled}
      >
        {isDelete ? (
          <ChevronLeft color={isDisabled ? colors.grey[70] : undefined} />
        ) : (
          <H3 className="text-accent-moderate dark:text-accent-moderate">
            {value}
          </H3>
        )}
      </button>
    );
  };

  return (
    <BareLayout
      title="Review transfer details"
      footer={
        <Button
          label="Withdraw"
          loading={validatingPin || isSubmittingRequest}
          disabled={validatingPin || isSubmittingRequest}
          onPress={() => {
            if (hasPinCreated) {
              setModalOpen(true);
            } else {
              toast.custom('You need to set up a PIN to continue.');
              router.push('/profile/pin');
            }
          }}
        />
      }
    >
      <Card>
        <div className="size-10 flex items-center justify-center rounded-full bg-bg-warning-light dark:bg-bg-warning-dark">
          <WarningTriangle
            color={
              isDark
                ? semanticColors.fg.warning.dark
                : semanticColors.fg.warning.light
            }
          />
        </div>
        <SmRegularLabel>
          You are about to withdraw {formatAmount(withdrawalAmount)}
        </SmRegularLabel>
        <SmRegularLabel className="text-fg-warning-light dark:text-fg-warning-dark">
          Withdrawals may take up to 48hrs to reflect in your bank account.
        </SmRegularLabel>
      </Card>
      {bankDetails && (
        <Card>
          <div className="flex flex-row items-center justify-between">
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              Recipient
            </SmRegularLabel>
            <SmRegularLabel>{bankDetails.fullName}</SmRegularLabel>
          </div>
          <div className="flex flex-row items-center justify-between">
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              Account number
            </SmRegularLabel>
            <SmRegularLabel>{bankDetails.accountNumber}</SmRegularLabel>
          </div>
          <div className="flex flex-row items-center justify-between">
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              Bank
            </SmRegularLabel>
            <SmRegularLabel>{bankDetails.bankName}</SmRegularLabel>
          </div>
        </Card>
      )}
      <Card>
        {summaryItems.map(({ key, value }) => (
          <div key={key} className="flex flex-row items-center justify-between">
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
              {key === 'amount'
                ? 'You get'
                : key === 'fee'
                  ? 'Transaction fees'
                  : 'Total'}
            </SmRegularLabel>
            <SmRegularLabel>{formatAmount(value)}</SmRegularLabel>
          </div>
        ))}
      </Card>

      <Modal isOpen={modalOpen} onClose={() => setModalOpen(false)}>
        <div className="min-h-[478px] items-center flex flex-col gap-6 px-4 pt-6">
          <H2>Enter your transaction PIN</H2>

          <div className="flex flex-row gap-4">
            {[0, 1, 2, 3].map((index) => {
              const isFilled = index < pin.length;
              return (
                <div
                  key={index}
                  className={cn(
                    'flex size-14 items-center justify-center rounded-full border-2',
                    isFilled
                      ? 'border-accent-moderate dark:border-accent-moderate'
                      : 'border-grey-30 dark:border-grey-80'
                  )}
                >
                  {isFilled && (
                    <div className="size-2 rounded-full bg-black dark:bg-white" />
                  )}
                </div>
              );
            })}
          </div>
          <div className="grid grid-cols-3 gap-6 place-items-center">
            {[
              '1',
              '2',
              '3',
              '4',
              '5',
              '6',
              '7',
              '8',
              '9',
              '',
              '0',
              'delete',
            ].map(renderKey)}
          </div>
        </div>
      </Modal>
    </BareLayout>
  );
}

const Card: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => (
  <div
    className={cn(
      'rounded-lg bg-bg-subtle-light dark:bg-bg-subtle-dark p-4 flex flex-col gap-2',
      className
    )}
  >
    {children}
  </div>
);
