import ky from 'ky';
import { z } from 'zod';

import { env } from '@/env.mjs';

const PaystackInitializeTransactionSuccessResponse = z.object({
  status: z.literal(true),
  message: z.string(),
  data: z.object({
    authorization_url: z.string().url(),
    access_code: z.string(),
    reference: z.string(),
  }),
});

export async function POST(request: Request) {
  let response: {
    status: boolean;
    message: string;
    data: {
      paymentLink: string;
    } | null;
  } | null = null;

  try {
    const {
      slug,
      email,
      userId,
      amount,
      eventId,
      currency = 'NGN',
      fullName,
      breakdown,
    } = await request.json();

    const paystackResponse = await ky
      .post(`${env.PAYSTACK_BASE_URL}/transaction/initialize/`, {
        headers: {
          authorization: `Bearer ${env.PAYSTACK_SECRET_KEY}`,
        },
        credentials: 'include',
        json: {
          email,
          amount,
          currency,
          metadata: {
            purpose: 'EVENT_TICKET_PURCHASE',
            eventId,
            breakdown: breakdown,
            user: {
              fullName,
              email,
            },
            ...(userId && { userId }),
            custom_fields: [
              {
                display_name: 'Event ID',
                variable_name: 'eventId',
                value: eventId,
              },
            ],
            // Replace with URL to redirect on cancel
            // cancel_action: 'https://your-cancel-url.com',
          },
          // callback_url: `${env.NEXT_PUBLIC_APP_BASE_URL}/event/features`,
          callback_url: `${env.NEXT_PUBLIC_APP_BASE_URL}/event/${slug}`,
        },
      })
      .json();

    const parsedResponse =
      PaystackInitializeTransactionSuccessResponse.parse(paystackResponse);

    const {
      data: { authorization_url },
    } = parsedResponse;

    response = {
      status: true,
      message: 'Successfully initialized paystack transaction',
      data: {
        paymentLink: authorization_url,
      },
    };
  } catch (error: any) {
    const errorResponse = await error?.response?.json();

    const errorMessage = errorResponse?.message || error?.message;

    response = {
      status: false,
      message: errorMessage,
      data: null,
    };
  } finally {
    return Response.json(response);
  }
}
