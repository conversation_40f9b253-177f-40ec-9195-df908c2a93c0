import { NextResponse } from 'next/server';
import { env } from '@/env.mjs';

export async function POST(req: any) {
  const { token } = await req.json();

  if (!token) {
    return NextResponse.json({ message: 'Token is required' }, { status: 400 });
  }

  const response = NextResponse.json({ message: 'Login successful' });

  response.cookies.set('token', token, {
    httpOnly: true,
    path: '/',
    sameSite: 'none',
    secure: true,
  });

  try {
    // Fetch user info from external API
    const apiResponse = await fetch(
      `${env.NEXT_PUBLIC_API_BASE_URL}/users/me`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );

    if (!apiResponse.ok) {
      return NextResponse.json(
        { message: 'Failed to fetch user info' },
        { status: apiResponse.status }
      );
    }
    const data = await apiResponse.json();
    const user = data.data;

    // Set the user cookie
    response.cookies.set('user', JSON.stringify(user), {
      httpOnly: true,
      path: '/',
      sameSite: 'none',
      secure: true,
    });

    return response;
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
