import * as React from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import '@/styles/globals.css';
import { Providers, AuthProvider, ThemeProvider, SessionProvider } from '@/lib';
import { Toaster } from 'react-hot-toast';
import { env } from '@/env.mjs';
// import Script from 'next/script';
import { CookieConsent } from '@/components/cookies';
import { Metadata } from 'next';
import { cn } from '@/lib/utils';
import { aeonik, inter } from '@/lib';
import { getAuthSession } from '@/lib/utils/auth';

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: `Popla - Connect with people, Request for your choice songs, and Get tickets to Events`,
    description:
      'Popla is a unique service that helps you request for songs. Whether from Bands or DJs playing around you',
    openGraph: {
      title:
        'Popla - Connect with people, Request for your choice songs, and Get tickets to Events',
      description:
        'Popla is a unique service that helps you request for songs. Whether from Bands or DJs playing around you',
      url: `${env.NEXT_PUBLIC_APP_BASE_URL}`,
    },
  };
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getAuthSession();

  return (
    <html
      className={cn(
        '!font-default bg-background text-foreground antialiased',
        aeonik.variable,
        inter.variable
      )}
    >
      {/* <Script
        src={`https://maps.googleapis.com/maps/api/js?key=${env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY}&libraries=places`}
        strategy="lazyOnload"
      /> */}
      <body className="font-aeonik flex flex-col h-screen bg-bg-canvas-light dark:bg-bg-canvas-dark">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <SessionProvider session={session}>
            <AuthProvider>
              <Providers>
                <Toaster position="top-center" />
                {children}
                <CookieConsent />
              </Providers>
            </AuthProvider>
          </SessionProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
