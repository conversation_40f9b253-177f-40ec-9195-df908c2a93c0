import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/dialogs/dialog';
import { NextImage } from '@/components/ui/NextImage';

export const PasswordResetSuccessDialog = ({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        hideCloseIcon
        className="sm:max-w-[398px] p-6 rounded-2xl! bg-bg-canvas-light dark:bg-bg-canvas-dark border-0 gap-0"
      >
        <DialogHeader className="items-center">
          <NextImage
            src={
              process.env.NEXT_PUBLIC_URL
                ? `${process.env.NEXT_PUBLIC_URL}/images/success.png`
                : '/images/success.png'
            }
            alt="Success icon"
            width={120}
            height={120}
          />
          <DialogTitle className="mt-0!">
            Password changed succesfully
          </DialogTitle>
          <DialogDescription className="py-4 mt-0!">
            You can login now.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="pt-6">
          <DialogClose asChild>
            <Button className="flex-1">Okay</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
