import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/dialogs';
import { Button, MdBoldLabel, Small, Image } from '@/components/ui';
import { cn } from '@/lib/utils';
import { USER_ROLE } from '@/api';
import { USER_ROLE_CARDS } from '@/lib';

export const SelectAccountTypeDialog = ({
  open,
  setOpen,
  accountType,
  onSelectAccountType,
  onProceed,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  accountType: USER_ROLE;
  onSelectAccountType: (accountType: USER_ROLE) => void;
  onProceed: () => void;
}) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <form>
        <DialogContent
          hideCloseIcon
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
          className="sm:max-w-[618px] p-6 rounded-2xl! bg-bg-canvas-light dark:bg-bg-canvas-dark border-0 gap-0"
        >
          <DialogHeader>
            <DialogTitle>What brings you to Popla?</DialogTitle>
          </DialogHeader>

          <div className="flex gap-4 py-6">
            {USER_ROLE_CARDS.map(({ icon, id, title, description }) => (
              <div
                key={id}
                className={cn(
                  'flex flex-1 items-center gap-4 rounded-2xl border border-border-subtle-light dark:border-border-subtle-dark px-4 py-3',
                  accountType === id &&
                    'border-2 border-accent-moderate dark:border-accent-moderate'
                )}
                onClick={() => onSelectAccountType(id)}
              >
                <div className="size-10">
                  <Image src={icon} alt={title} width={40} height={40} />
                </div>
                <div className="flex flex-col gap-2">
                  <MdBoldLabel themed weight="bold">
                    {title}
                  </MdBoldLabel>
                  <Small className="text-fg-muted-light dark:text-fg-muted-dark">
                    {description}
                  </Small>
                </div>
              </div>
            ))}
          </div>

          <DialogFooter className="mt-6">
            <DialogClose asChild>
              <Button
                variant="default"
                disabled={!accountType}
                onClick={onProceed}
              >
                Continue
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
};
