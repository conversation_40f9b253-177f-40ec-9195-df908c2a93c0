'use client';
import { But<PERSON> } from '@/components/ui/button';
import { PasswordInput } from '@/components/ui/password-input';
import { H1, Small } from '@/components/ui/typography';
import { cn } from '@/lib/utils';
import React from 'react';

export const PasswordForm = ({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'form'>) => {
  const [password, setPassword] = React.useState('');
  const [passwordConfirmation, setPasswordConfirmation] = React.useState('');
  return (
    <form className={cn('flex flex-col gap-8', className)} {...props}>
      <H1 themed weight="bold">
        Let’s secure your account
      </H1>
      <div className="grid gap-4">
        <PasswordInput
          label="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          autoComplete="new-password"
          required
        />
        <ul className="list-disc pl-5">
          <Small
            as="li"
            className="text-fg-subtle-light dark:text-fg-subtle-dark"
            weight="regular"
          >
            Include at least one lowercase letter (a-z), a number (0-9), and a
            special character (!, @, #, $ etc.).
          </Small>
          <Small
            as="li"
            className="text-fg-subtle-light dark:text-fg-subtle-dark"
            weight="regular"
          >
            Use 7 characters or more.
          </Small>
        </ul>
        <PasswordInput
          label="Confirm Password"
          value={passwordConfirmation}
          onChange={(e) => setPasswordConfirmation(e.target.value)}
          autoComplete="new-password"
          required
        />
      </div>

      <Button disabled>Continue</Button>
    </form>
  );
};
