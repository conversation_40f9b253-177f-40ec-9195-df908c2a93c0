import * as React from 'react';

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SUPPORTED_COUNTRIES } from '@/lib/constants/generic';
import { cn } from '@/lib/utils';
import { Small } from '@/components/ui/typography';

interface SelectCountryProps {
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export const SelectCountry = ({
  value,
  onChange,
  error,
  placeholder = 'Select a location',
  disabled = false,
  className,
}: SelectCountryProps) => {
  return (
    <div className="space-y-2">
      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger
          className={cn(
            'w-full',
            error && 'border-red-500 focus:ring-red-500',
            className
          )}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {SUPPORTED_COUNTRIES.map((country) => (
              <SelectItem key={country.code} value={country.value}>
                {country.label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
      {error && <Small className="text-red-60 dark:text-red-80">{error}</Small>}
    </div>
  );
};
