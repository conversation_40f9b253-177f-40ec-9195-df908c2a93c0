'use client';
import { Button } from '@/components/ui/button';
import { PaginationDots } from '@/components/ui/pagination-dots';
import { H1, P } from '@/components/ui/typography';

interface Props {
  readonly children: React.ReactNode;
  handleContinue: () => void;
  title: string;
  subTitle?: string;
  showSkip?: boolean;
  handleSkip?: () => void;
  handleBack?: () => void;
  currentStep: number;
  totalSteps: number;
  isFirstStep?: boolean;
  canContinue?: boolean;
  goToStep?: (step: number) => void;
}
export const OnboardingLayout = ({
  children,
  title,
  subTitle,
  showSkip,
  handleContinue,
  handleSkip,
  currentStep,
  totalSteps,
  canContinue = true,
  goToStep,
}: Props) => {
  return (
    <section className="flex flex-col flex-1 p-6 md:p-8 max-w-3xl">
      <div className="flex flex-1 flex-col gap-10">
        <div className="flex flex-col gap-6">
          <PaginationDots
            total={totalSteps}
            activeIndex={currentStep}
            onDotClick={(i) => i < currentStep && goToStep?.(i)}
          />
          <div className="flex flex-col gap-2">
            <H1 themed weight="bold">
              {title}
            </H1>
            <P themed className="text-fg-muted-light dark:text-fg-muted-dark">
              {subTitle}
            </P>
          </div>
        </div>
        {children}
      </div>
      <div className="flex gap-4 justify-end">
        {showSkip && handleSkip && (
          <Button variant="ghost" onClick={handleSkip}>
            Skip
          </Button>
        )}
        <Button disabled={!canContinue} onClick={handleContinue}>
          {currentStep === totalSteps - 1 ? 'Finish' : 'Continue'}
        </Button>
      </div>
    </section>
  );
};
