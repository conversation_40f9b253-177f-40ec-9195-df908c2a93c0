'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { cn, useAuth } from '@/lib';
import { Image } from '@/components/ui';

interface AvatarWithLetterProps {
  onClick?: () => void;
  className?: string;
}

export const AvatarWithLetter: React.FC<AvatarWithLetterProps> = ({
  onClick,
  className,
}) => {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div
      className={cn(
        'w-8 h-8 flex items-center justify-center self-center rounded-full bg-brand-60 cursor-pointer',
        className
      )}
      onClick={() => (onClick ? onClick() : router.push('/profile'))}
    >
      {user?.bannerUrl ? (
        <Image
          key={'profile-avatar'}
          width={24}
          height={24}
          className="size-8 rounded-full object-cover"
          src={user.bannerUrl}
          alt="Profile"
        />
      ) : (
        <span className="text-brand-20 font-semibold">
          {user?.username?.charAt(0) || '@'}
        </span>
      )}
    </div>
  );
};
