'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { NextImage } from '@/components/ui/NextImage';

interface RegularAvatarProps {
  avatar: string;
  className?: string;
  size?: number;
  hasRing?: boolean;
  alt?: string;
}

export const RegularAvatar: React.FC<RegularAvatarProps> = ({
  avatar = '/images/users/dj-aisha.png',
  className,
  size = 64,
  hasRing = true,
  alt = 'User avatar',
}) => {
  return (
    <div
      className={cn('relative flex items-center justify-center', className)}
      style={{ width: size, height: size }}
    >
      {hasRing ? (
        <>
          {/* Gradient ring background */}
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: 'linear-gradient(135deg, #664FB0 0%, #A992F5 100%)',
            }}
          />

          {/* White/transparent spacer to create inner cutout */}
          <div
            className="absolute rounded-full bg-brand-10 dark:bg-brand-90"
            style={{
              width: size - 4,
              height: size - 4,
            }}
          />

          {/* Avatar image */}
          <div
            className="relative overflow-hidden rounded-full"
            style={{
              width: size - 8,
              height: size - 8,
            }}
          >
            <NextImage
              src={avatar}
              alt={alt}
              fill
              className="object-cover"
              sizes={`${size}px`}
            />
          </div>
        </>
      ) : (
        <div
          className="relative overflow-hidden rounded-full"
          style={{
            width: size,
            height: size,
          }}
        >
          <NextImage
            src={avatar}
            alt={alt}
            fill
            className="object-cover"
            sizes={`${size}px`}
          />
        </div>
      )}
    </div>
  );
};
