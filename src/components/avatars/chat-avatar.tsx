'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import React from 'react';

interface ChatAvatarProps {
  username: string;
  userId: string;
  avatar: string;
  online?: boolean;
}

export const ChatAvatar: React.FC<ChatAvatarProps> = ({
  username,
  userId,
  avatar,
  online,
}) => {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center gap-2">
      <button
        className="relative"
        onClick={() => router.push(`/users/${userId}`)}
      >
        <Image
          src={avatar}
          alt={username}
          width={64}
          height={64}
          className="rounded-full object-cover"
        />
        {online && (
          <div className="absolute bottom-0 right-0 flex size-6 items-center justify-center rounded-full bg-white dark:bg-gray-100">
            <span className="block size-3 rounded-full bg-green-500 border-2 border-white dark:border-gray-100" />
          </div>
        )}
      </button>
      <h4 className="text-base font-semibold">{username}</h4>
    </div>
  );
};
