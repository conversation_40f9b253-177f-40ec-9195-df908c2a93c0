'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { H5 } from '@/components/ui/typography';

interface AvatarInitialsProps {
  initials: string;
  className?: string;
  onProfileClick?: () => void;
}

export const AvatarInitials: React.FC<AvatarInitialsProps> = ({
  initials = 'AI',
  className,
  onProfileClick,
}) => {
  return (
    <div
      className={cn(
        'relative size-8 rounded-full flex items-center justify-center bg-accent-subtle-light dark:bg-accent-subtle-dark',
        onProfileClick && 'cursor-pointer',
        className
      )}
      onClick={onProfileClick}
    >
      <H5 themed className="text-accent-moderate" weight="bold">
        {initials}
      </H5>
    </div>
  );
};
