'use client';

import Image from 'next/image';
import React from 'react';
import { cn } from '@/lib';

interface LiveUserAvatarProps {
  username: string;
  avatar: string;
  className?: string;
  textClassName?: string;
  hideStatusPill?: boolean;
  onPress?: () => void;
  hasRing?: boolean;
}

export const LiveUserAvatar: React.FC<LiveUserAvatarProps> = ({
  username,
  avatar,
  className,
  textClassName,
  hideStatusPill,
  onPress,
  hasRing = true,
}) => {
  return (
    <div
      className={cn('flex flex-col items-center gap-1', className)}
      onClick={onPress}
    >
      <div className="relative w-16 h-16 flex items-center justify-center rounded-full">
        {hasRing ? (
          <>
            {/* Gradient ring */}
            <div
              className="absolute inset-0 rounded-full"
              style={{
                background: 'linear-gradient(45deg, #664FB0, #A992F5)',
              }}
            />
            {/* White spacer to create inner cutout */}
            <div className="absolute w-14 h-14 rounded-full bg-white dark:bg-gray-900" />
            <Image
              src={avatar}
              alt={username}
              width={56}
              height={56}
              className="rounded-full object-cover"
            />
          </>
        ) : (
          <Image
            src={avatar}
            alt={username}
            width={56}
            height={56}
            className="rounded-full object-cover"
          />
        )}

        {!hideStatusPill && (
          <div className="absolute -bottom-1.5 flex w-10 flex-row items-center justify-center gap-0.5 rounded-md bg-red-50 px-1 py-0.5">
            <div className="w-1 h-1 rounded-full bg-white" />
            <span className="text-[10px] font-bold text-white">LIVE</span>
          </div>
        )}
      </div>
      <span className={cn('text-sm', textClassName)}>{username}</span>
    </div>
  );
};
