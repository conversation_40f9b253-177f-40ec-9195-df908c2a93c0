'use client';

import Image from 'next/image';
import React from 'react';
import { cn, useAccountFavourite } from '@/lib';
import { Button } from '@/components/ui';

interface PopularAccountCardProps {
  id: string;
  username: string;
  avatar: string;
  className?: string;
  textClassName?: string;
  onClick?: () => void;
}

export const PopularAccountCard: React.FC<PopularAccountCardProps> = ({
  id,
  username,
  avatar,
  className,
  textClassName,
  onClick,
}) => {
  const { favourited, toggleFavourite } = useAccountFavourite(id);

  return (
    <div
      className={cn('w-[108px] rounded-md p-2 relative', className)}
      onClick={onClick}
    >
      {/* Gradient overlay */}
      <div
        className="absolute inset-0 rounded-md"
        style={{
          background: 'linear-gradient(to bottom, #FFFFFF, #FFFFFF)',
          opacity: 0.05,
        }}
      />

      <div className="flex flex-col items-center gap-2 relative">
        <Image
          src={avatar}
          alt={username}
          width={64} // approximate size-16 in pixels
          height={64}
          className="rounded-full object-cover"
        />
        <span
          className={cn('text-sm truncate', textClassName)}
          title={username}
        >
          {username}
        </span>
        <Button
          label={favourited ? 'Favourited' : 'Favourite'}
          className="h-8 px-2"
          textClassName="text-xs/150"
          onClick={(e) => {
            e.stopPropagation(); // prevent triggering parent click
            toggleFavourite();
          }}
        />
      </div>
    </div>
  );
};
