'use client';

import React from 'react';
import Image from 'next/image';
import clsx from 'clsx';

interface RegularAvatarProps {
  avatar: string;
  className?: string;
  size?: number;
  hasRing?: boolean;
}

export const RegularAvatarR: React.FC<RegularAvatarProps> = ({
  avatar,
  className,
  size = 64,
  hasRing = true,
}) => {
  const ringSize = size - 4;
  const imageSize = size - 8;

  return (
    <div
      className={clsx('relative flex items-center justify-center', className)}
      style={{ width: size, height: size }}
    >
      {hasRing ? (
        <>
          {/* Gradient ring */}
          <div className="absolute inset-0 rounded-full bg-linear-to-br from-[#664FB0] to-[#A992F5]" />

          {/* Inner cutout */}
          <div
            className="absolute rounded-full bg-brand-10 dark:bg-brand-90"
            style={{ width: ringSize, height: ringSize }}
          />

          {/* Avatar image */}
          <Image
            src={avatar}
            alt="User avatar"
            width={imageSize}
            height={imageSize}
            className="rounded-full object-cover z-10"
          />
        </>
      ) : (
        <Image
          src={avatar}
          alt="User avatar"
          width={size}
          height={size}
          className="rounded-full object-cover"
        />
      )}
    </div>
  );
};
