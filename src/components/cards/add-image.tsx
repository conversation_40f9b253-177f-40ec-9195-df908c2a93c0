'use client';

import React from 'react';
import type { Path, UseFormSetValue, FieldValues } from 'react-hook-form';
import { IoClose } from 'react-icons/io5';
import Image from 'next/image';

type ExtractArrayElement<T> = T extends (infer U)[]
  ? U & { uri: string }
  : never;

interface AddImageCardProps<T extends FieldValues, K extends Path<T>> {
  photo: ExtractArrayElement<NonNullable<T[K]>>;
  photos: NonNullable<T[K]>;
  setValue: UseFormSetValue<T>;
  name: K;
  onImageChange?: () => Promise<void>;
}

export const AddImageCard = <T extends FieldValues, K extends Path<T>>({
  photo,
  photos,
  setValue,
  name,
  onImageChange,
}: AddImageCardProps<T, K>) => {
  return (
    <div className="relative w-20 h-20 rounded-md">
      {/* Close button */}
      <button
        type="button"
        aria-label="Remove image"
        onClick={() => {
          const updated = photos.filter((p: T[K]) => p !== photo) as T[K];
          setValue(name, updated);
        }}
        className="absolute -right-1 -top-1 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-white dark:bg-white"
      >
        <IoClose size={16} color="#000" />
      </button>

      {/* Image clickable area */}
      <button
        type="button"
        onClick={onImageChange}
        className="w-full h-full rounded-md overflow-hidden"
      >
        <Image
          src={photo.uri}
          alt="User uploaded"
          width={80}
          height={80}
          className="object-cover"
        />
      </button>
    </div>
  );
};
