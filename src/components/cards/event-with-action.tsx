import { MapPin } from 'lucide-react';
import { format, isPast, parseISO } from 'date-fns';
import Link from 'next/link';
import React from 'react';

import { type IEvent } from '@/api/events';
import { But<PERSON>, H3, SmRegular<PERSON>abel, Tiny } from '@/components/ui';
import { cn } from '@/lib';

interface EventCardWithActionProps extends Partial<IEvent> {
  attendees: number;
  id: string;
}

export const EventCardWithAction: React.FC<EventCardWithActionProps> = ({
  bannerUrl,
  title,
  startTime,
  endTime,
  slug,
  location,
}) => {
  const date = parseISO(startTime || '');
  const month = format(date, 'MMM');
  const day = format(date, 'd');
  const isEventInThePast = endTime ? isPast(endTime) : true;

  return (
    <Link
      href={`/events/${slug}`}
      className="relative grid grid-cols-[130px_1fr_56px] items-center gap-2 pr-2 rounded-md bg-bg-disabled-light dark:bg-bg-disabled-dark"
    >
      {isEventInThePast && (
        <div className="absolute right-0 top-0 overflow-hidden rounded-tr-md">
          <div
            className={cn(
              'px-3 py-1',
              isEventInThePast
                ? 'bg-bg-danger-primary'
                : 'bg-bg-success-contrast-light dark:bg-bg-success-contrast-dark'
            )}
          >
            <Tiny
              className={cn(!isEventInThePast && 'text-dark dark:text-white')}
            >
              {isEventInThePast ? 'Ended' : 'Active'}
            </Tiny>
          </div>
        </div>
      )}
      <img
        src={bannerUrl || '/images/gradient-bg.png'}
        alt={title || 'Event Banner'}
        className="h-[130px] w-[130px] object-cover rounded-l-md"
      />

      <div className="flex flex-col gap-2 overflow-hidden">
        <H3 className="truncate">{title}</H3>

        <div className="flex items-center gap-0.5">
          <MapPin size={12} className="text-black dark:text-white" />
          <SmRegularLabel className="truncate flex-1">
            {location?.landmark}
          </SmRegularLabel>
        </div>

        {!isEventInThePast && (
          <Button
            className="h-7 w-24 text-xs"
            textClassName="text-xs/100 font-aeonik-black font-bold text-white dark:text-white"
          >
            Get Tickets
          </Button>
        )}
      </div>

      <div className="flex size-14 flex-col items-center justify-center gap-1 rounded-sm bg-brand-60 px-2 py-1">
        <H3>{month.toUpperCase()}</H3>
        <H3>{day}</H3>
      </div>
    </Link>
  );
};
