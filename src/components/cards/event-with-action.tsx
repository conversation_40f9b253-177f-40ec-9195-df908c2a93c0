import { MapPin } from 'lucide-react';
import { format, isPast, parseISO } from 'date-fns';
import Link from 'next/link';
import React from 'react';

import { type IEvent } from '@/api/events';
import { Button, H3, SmRegularLabel } from '@/components/ui';

interface EventCardWithActionProps extends Partial<IEvent> {
  attendees: number;
  id: string;
}

export const EventCardWithAction: React.FC<EventCardWithActionProps> = ({
  bannerUrl,
  title,
  startTime,
  endTime,
  slug,
  location,
}) => {
  const date = parseISO(startTime || '');
  const month = format(date, 'MMM');
  const day = format(date, 'd');
  const isEventInThePast = endTime ? isPast(endTime) : true;

  return (
    <Link
      href={`/events/${slug}`}
      className="grid grid-cols-[130px_1fr_56px] items-center gap-2 pr-2 rounded-md bg-bg-disabled-light dark:bg-bg-disabled-dark"
    >
      <img
        src={bannerUrl || '/images/gradient-bg.png'}
        alt={title || 'Event Banner'}
        className="h-[130px] w-[130px] object-cover rounded-l-md"
      />

      <div className="flex flex-col gap-2 overflow-hidden">
        <H3 className="truncate">{title}</H3>

        <div className="flex items-center gap-0.5">
          <MapPin size={12} className="text-black dark:text-white" />
          <SmRegularLabel className="truncate flex-1">
            {location?.landmark}
          </SmRegularLabel>
        </div>

        {!isEventInThePast && (
          <Button
            className="h-7 w-24 text-xs"
            textClassName="text-xs/100 font-aeonik-black font-bold text-white dark:text-white"
          >
            Get Tickets
          </Button>
        )}
      </div>

      <div className="flex size-14 flex-col items-center justify-center gap-1 rounded-sm bg-brand-60 px-2 py-1">
        <H3>{month.toUpperCase()}</H3>
        <H3>{day}</H3>
      </div>
    </Link>
  );
};
