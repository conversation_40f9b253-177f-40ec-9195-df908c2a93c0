import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

import { cn, useAccountFavourite } from '@/lib';
import { ConfirmationDialog } from '@/components/dialogs';
import { colors, HeartFilledIcon, Small, SmBoldLabel } from '@/components/ui';

interface FavouriteAccountCardProps {
  image: string;
  fullname: string;
  username: string;
  id: string;
}

export const FavouriteAccountCard: React.FC<FavouriteAccountCardProps> = ({
  image,
  fullname,
  username,
  id,
}) => {
  const { toggleFavourite } = useAccountFavourite(id);
  const [confirmVisible, setConfirmVisible] = React.useState(false);

  return (
    <div
      className={cn(
        'h-16 flex-1 flex flex-row gap-3.5 items-center cursor-pointer bg-bg-muted-light dark:bg-bg-muted-dark rounded-lg p-2'
      )}
    >
      <Link
        href={`/users/${username}`}
        className="flex flex-row flex-1 gap-3.5 items-center"
      >
        <Image
          src={image || '/images/avatar.png'}
          alt={fullname}
          width={40}
          height={40}
          className="rounded-full object-cover"
        />
        <div className="flex flex-col flex-1 gap-1">
          <SmBoldLabel className="truncate">{fullname}</SmBoldLabel>
          <Small className="truncate">{username}</Small>
        </div>
      </Link>

      <button
        type="button"
        className="size-10 flex items-center justify-center"
        onClick={() => setConfirmVisible(true)}
      >
        <HeartFilledIcon color={colors.red[50]} />
      </button>

      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to remove this account from your favourites?"
        onCancel={() => setConfirmVisible(false)}
        onConfirm={() => {
          setConfirmVisible(false);
          toggleFavourite();
        }}
      />
    </div>
  );
};
