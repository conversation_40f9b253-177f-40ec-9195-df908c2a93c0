import Image from 'next/image';
import React from 'react';
import { useAccountFavourite } from '@/lib';

interface FavouriteCardProps {
  id: string;
  name: string;
  username: string;
  image: string;
}

export const FavouriteCard = ({
  id,
  name,
  username,
  image,
}: FavouriteCardProps) => {
  const { favourited, toggleFavourite } = useAccountFavourite(id);

  return (
    <div className="flex w-full flex-row items-center justify-between gap-4">
      <div className="flex items-center">
        <Image
          src={image}
          alt={name}
          width={40}
          height={40}
          className="rounded-full object-cover mr-2"
        />
        <div className="flex flex-col gap-1">
          <span className="font-semibold">{name}</span>
          <span className="text-grey-60 dark:text-grey-50 text-sm">
            @{username}
          </span>
        </div>
      </div>
      <button
        onClick={toggleFavourite}
        className={`px-5 py-1 text-sm rounded border transition ${
          favourited
            ? 'border-gray-400 text-gray-700 dark:text-gray-300'
            : 'bg-brand-60 text-white hover:bg-brand-70'
        }`}
      >
        {favourited ? 'Remove' : 'Favourite'}
      </button>
    </div>
  );
};
