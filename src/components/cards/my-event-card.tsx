import { isPast } from 'date-fns';
import React from 'react';

import type { IEvent } from '@/api/events';
import { cn } from '@/lib';

import { Tiny } from '../ui';
import { EventFavoriteCard } from './event-favorite';

interface MyEventsCardProps extends IEvent {
  attendees: number;
}

export const MyEventsCard: React.FC<MyEventsCardProps> = ({
  endTime,
  status,
  ...eventProps
}) => {
  const hasEventEnded = endTime ? isPast(endTime) : true;

  return (
    <div className="relative">
      <EventFavoriteCard
        {...eventProps}
        endTime={endTime}
        status={status}
        isMyEventsScreen
      />
      {!hasEventEnded && (
        <div className="absolute right-0 top-0 overflow-hidden rounded-tr-lg">
          <div
            className={cn(
              'px-3 py-1',
              status === 'DRAFT'
                ? 'bg-bg-warning-contrast'
                : 'bg-bg-success-contrast-light dark:bg-bg-success-contrast-dark'
            )}
          >
            <Tiny
              className={cn(
                status === 'DRAFT'
                  ? 'text-black dark:text-black'
                  : 'text-white dark:text-white'
              )}
            >
              {status === 'DRAFT' ? 'Pending' : 'Active'}
            </Tiny>
          </div>
        </div>
      )}
    </div>
  );
};
