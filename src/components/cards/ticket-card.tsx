import { MoreHorizontal } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

import { formatCurrency } from '@/lib/utils';
import { type CreateEventFormType } from '@/lib';

type TicketCardProps = {
  ticket: CreateEventFormType['tickets'][number];
  fieldId: string;
  isMenuOpen: boolean;
  setMenuVisible: (id: string | null) => void;
  onEdit: () => void;
  onDelete?: () => void;
};

function PresaleBadge() {
  return (
    <div className="absolute right-2 top-1 z-10 h-5 w-[84px] flex items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
      <span className="text-xs font-bold text-green-60 dark:text-green-40">
        Presale incl.
      </span>
    </div>
  );
}

export const TicketCard = ({
  ticket,
  fieldId,
  isMenuOpen,
  setMenuVisible,
  onEdit,
  onDelete,
}: TicketCardProps) => {
  return (
    <div className="relative h-[98px] flex items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark">
      <div className="flex flex-1 items-center gap-2">
        <Image
          src="/icons/disc.png"
          alt="Ticket"
          width={40}
          height={40}
          className="rounded"
        />
        <div className="flex flex-col gap-2">
          <span className="font-bold text-fg-base-light dark:text-fg-base-dark">
            {ticket.name}
          </span>
          <span className="text-fg-subtle-light dark:text-fg-subtle-dark">
            {formatCurrency(ticket.price)}
          </span>
          {ticket.description && (
            <span className="text-fg-subtle-light dark:text-fg-subtle-dark">
              {ticket.description.length > 30
                ? ticket.description.slice(0, 30) + '...'
                : ticket.description}
            </span>
          )}
        </div>
      </div>

      {ticket.hasPresale && <PresaleBadge />}

      <div className="relative flex items-center gap-2">
        <button
          onClick={(e) => {
            e.stopPropagation();
            setMenuVisible(fieldId);
          }}
          className="cursor-pointer"
          aria-label="Open ticket options"
        >
          <MoreHorizontal size={24} className="text-dark dark:text-white" />
        </button>

        {isMenuOpen && (
          <>
            <div
              className="z-9999 absolute inset-0"
              onClick={() => setMenuVisible(null)}
            />
            <div className="z-60 absolute right-0 top-8 w-[120px] rounded-md bg-bg-interactive-secondary-light p-2 shadow-md dark:bg-bg-interactive-secondary-dark">
              <button
                onClick={onEdit}
                className="block w-full py-2 text-left text-fg-base-light dark:text-fg-base-light cursor-pointer"
              >
                Edit ticket
              </button>
              {onDelete && (
                <button
                  onClick={onDelete}
                  className="block w-full py-2 text-left text-fg-danger-light dark:text-fg-danger-light cursor-pointer"
                >
                  Delete ticket
                </button>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};
