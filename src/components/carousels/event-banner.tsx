'use client';

import { useRef, useState } from 'react';
import { Image } from '@/components/ui';

import { type IEventCategories, type ISingleEvent } from '@/api/events';
import { MdBoldLabel } from '../ui';

interface Props {
  event: ISingleEvent;
  eventCategory: IEventCategories;
  isCreator: boolean;
}

export const EventBannerCarousel: React.FC<Props> = ({
  event,
  eventCategory,
  isCreator,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollRef = useRef<HTMLDivElement | null>(null);

  const mediaItems = [
    {
      id: 'banner',
      url: event?.bannerUrl || '/images/gradient-bg/landscape-event-bg.png',
      type: 'banner',
    },
    ...(event?.media || []).map((item, index) => ({
      id: `media-${index}`,
      url: item || '/images/gradient-bg/landscape-event-bg.png',
      type: 'media',
    })),
  ];

  const handleScroll = () => {
    if (!scrollRef.current) return;
    const scrollPosition = scrollRef.current.scrollLeft;
    const index = Math.round(scrollPosition / scrollRef.current.clientWidth);
    setCurrentIndex(index);
  };

  const scrollToIndex = (index: number) => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({
        left: index * scrollRef.current.clientWidth,
        behavior: 'smooth',
      });
      setCurrentIndex(index);
    }
  };

  if (mediaItems.length <= 1) {
    return (
      <div className="relative max-h-[600px] w-full aspect-[16/16]">
        <Image
          src={mediaItems[0].url}
          alt="event banner"
          fill
          className="object-contain"
          priority
        />
        {!isCreator && (
          <div className="absolute top-4 w-full flex flex-row justify-between py-4 pl-4 pr-2">
            {eventCategory && (
              <div className="h-8 flex items-center justify-center rounded-xl bg-blue-100 px-4 dark:bg-blue-900">
                <MdBoldLabel className="text-blue-600 dark:text-blue-400">
                  {eventCategory.category}
                </MdBoldLabel>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="relative max-h-[600px] w-full aspect-[16/16]">
      <div
        ref={scrollRef}
        className="flex overflow-x-auto snap-x snap-mandatory scroll-smooth w-full h-full"
        onScroll={handleScroll}
      >
        {mediaItems.map((item) => (
          <div
            key={item.id}
            className="flex-shrink-0 w-full h-full snap-center relative"
          >
            <Image
              src={item.url}
              alt="event media"
              fill
              className="object-contain"
              priority
            />
          </div>
        ))}
      </div>

      {!isCreator && (
        <div className="absolute top-0 w-full flex flex-row justify-between py-4 pl-4 pr-2">
          {eventCategory && (
            <div className="h-8 flex items-center justify-center rounded-xl bg-blue-10 px-4 dark:bg-blue-80">
              <MdBoldLabel className="text-blue-60 dark:text-blue-40">
                {eventCategory.category}
              </MdBoldLabel>
            </div>
          )}
        </div>
      )}

      {/* Pagination dots */}
      <div className="absolute inset-x-0 bottom-4 flex flex-row justify-center gap-2">
        {mediaItems.map((_, index) => (
          <button
            key={index}
            onClick={() => scrollToIndex(index)}
            className={`size-2 rounded-full ${
              index === currentIndex ? 'bg-white' : 'bg-white/50'
            }`}
          />
        ))}
      </div>
    </div>
  );
};
