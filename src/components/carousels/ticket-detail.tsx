import * as React from 'react';
import { ITicketExtension } from '@/api/events';
import { TicketCard } from '../tickets/card';
import { PaginationDots } from '../ui/pagination-dots';

type TicketCarouselProps = {
  tickets: ITicketExtension[];
  onTicketChange: (index: number) => void;
  currentIndex: number;
  viewShotRefs: React.RefObject<HTMLDivElement>[];
};

export const TicketCarousel = ({
  tickets,
  onTicketChange,
  currentIndex,
  viewShotRefs,
}: TicketCarouselProps) => {
  const containerRef = React.useRef<HTMLDivElement>(null);

  const onScroll = () => {
    if (!containerRef.current) return;
    const scrollLeft = containerRef.current.scrollLeft;
    const slideWidth = containerRef.current.clientWidth;
    const index = Math.round(scrollLeft / slideWidth);
    if (index !== currentIndex && index >= 0 && index < tickets.length) {
      onTicketChange(index);
    }
  };

  if (tickets.length === 1) {
    return (
      <div className="flex justify-center items-center">
        <TicketCard ticket={tickets[0]} ref={viewShotRefs[0]} />
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col justify-center items-center gap-4">
      <div
        ref={containerRef}
        onScroll={onScroll}
        className="flex snap-x snap-mandatory overflow-x-auto scroll-smooth no-scrollbar"
      >
        {tickets.map((ticket, index) => (
          <div
            key={ticket.id}
            className="min-w-full flex justify-center snap-center rounded-lg flex-shrink-0"
          >
            <TicketCard ticket={ticket} ref={viewShotRefs[index]} />
          </div>
        ))}
      </div>
      <PaginationDots total={tickets.length} activeIndex={currentIndex} />
    </div>
  );
};
