'use client';

import React from 'react';
import { type Control, type FieldValues, type Path } from 'react-hook-form';
import { Button, ControlledInput, H2, Modal, P } from '@/components/ui';
import { cn } from '@/lib';

interface ConfirmationDialogProps<T extends FieldValues> {
  visible: boolean;
  title?: string;
  message: string;
  confirmLabel?: string;
  onConfirm: () => void;
  cancelLabel?: string;
  onCancel: () => void;
  control?: Control<T>;
  inputName?: Path<T>;
  inputLabel?: string;
  inputIcon?: any;
  backdropClassName?: string;
  isConfirming?: boolean;
  btnDisabled?: boolean;
}

export const ConfirmationDialog = <T extends FieldValues>({
  visible,
  title = 'Are you sure?',
  message,
  confirmLabel = 'Yes',
  onConfirm,
  cancelLabel,
  onCancel,
  control,
  inputName,
  inputLabel,
  inputIcon,
  backdropClassName,
  isConfirming,
  btnDisabled,
}: ConfirmationDialogProps<T>) => {
  if (!visible) return null;

  return (
    <Modal isOpen={visible} onClose={onCancel}>
      <div className={cn(backdropClassName)}>
        <div className="flex flex-col gap-6 rounded-lg p-2 shadow-lg">
          <div className="flex flex-col gap-2">
            <H2>{title}</H2>
            <P className="text-fg-muted-light dark:text-fg-muted-dark">
              {message}
            </P>
          </div>
          {control && inputName && (
            <ControlledInput
              control={control}
              name={inputName}
              label={inputLabel}
              autoFocus
              icon={inputIcon}
            />
          )}
          <div className="flex flex-row gap-3">
            <Button
              label={confirmLabel}
              onPress={onConfirm}
              variant="default"
              loading={isConfirming}
              disabled={isConfirming || btnDisabled}
            />
            {cancelLabel && (
              <Button label={cancelLabel} onPress={onCancel} variant="ghost" />
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};
