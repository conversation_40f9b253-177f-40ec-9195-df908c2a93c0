'use client';

import React from 'react';
import ReactDOM from 'react-dom';
import clsx from 'clsx';
import Image from 'next/image';

interface LocationAccessDialogProps {
  visible: boolean;
  title?: string;
  message: string;
  confirmLabel?: string;
  dismissLabel?: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export const LocationAccessDialog: React.FC<LocationAccessDialogProps> = ({
  visible,
  title = 'Find your vibe nearby!',
  message,
  onConfirm,
  onCancel,
  confirmLabel = 'Allow access',
  dismissLabel = 'Later',
}) => {
  if (!visible) return null;

  return ReactDOM.createPortal(
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="location-access-dialog-title"
      aria-describedby="location-access-dialog-description"
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
    >
      <div className="w-4/5 max-w-md rounded-lg bg-white p-6 pt-8 shadow-lg dark:bg-gray-900 flex flex-col items-center gap-6">
        <div className="relative w-[120px] h-[120px]">
          <Image
            src="/assets/images/location.png"
            alt="Location icon"
            fill
            className="object-contain"
          />
        </div>

        <div className="flex flex-col items-center gap-2 text-center">
          <h2
            id="location-access-dialog-title"
            className="text-2xl font-semibold"
          >
            {title}
          </h2>
          <p
            id="location-access-dialog-description"
            className="text-gray-600 dark:text-gray-300"
          >
            {message}
          </p>
        </div>

        <div className="flex w-full gap-3">
          <button
            onClick={onCancel}
            className={clsx(
              'flex-1 rounded bg-gray-200 py-2 px-4 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600',
              'focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-gray-500'
            )}
          >
            {dismissLabel}
          </button>

          <button
            onClick={onConfirm}
            className={clsx(
              'flex-1 rounded bg-blue-600 py-2 px-4 text-white hover:bg-blue-700',
              'focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            )}
          >
            {confirmLabel}
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};
