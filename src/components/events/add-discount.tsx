'use client';
import { FormProvider, useFormContext } from 'react-hook-form';
import { toast } from 'react-hot-toast';

import { useValidateEventDiscount } from '@/api/events';
import { AddDiscountForm } from '@/components/forms/add-discount';
import { Button } from '@/components/ui';
import { usePurchaseTicketContext } from '@/lib';
import { type PurchaseTicketFormType } from '@/lib/hooks/use-purchase-ticket';
interface AddDiscountProps {
  setDiscountVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export const AddDiscount = ({ setDiscountVisible }: AddDiscountProps) => {
  const formMethods = useFormContext<PurchaseTicketFormType>();
  const { eventId, getSelectedTicketsArray, setActiveDiscount } =
    usePurchaseTicketContext();
  const selectedTickets = getSelectedTicketsArray();

  const { isPending, mutate: validateDiscount } = useValidateEventDiscount({
    onSuccess: ({ discount, discountAmount }) => {
      setActiveDiscount({ ...discount, discountAmount });
      toast.success('discount validated successfully');
      setDiscountVisible(false);
    },
    onError: (error) => toast.error(error.message),
  });

  return (
    <div className="flex flex-col flex-1 bg-bg-canvas-light dark:bg-bg-canvas-dark px-4">
      <div className="mx-auto flex w-full max-w-md flex-1 flex-col">
        <FormProvider {...formMethods}>
          <AddDiscountForm />
        </FormProvider>

        <div className="mt-auto">
          <Button
            label="Continue"
            className="my-4"
            onPress={() => {
              validateDiscount({
                code: formMethods.watch('discountCode') || '',
                eventId,
                tickets: selectedTickets.map(
                  ({ category, quantity, cost }) => ({
                    category,
                    quantity,
                    originalPrice: cost,
                  })
                ),
              });
            }}
            loading={isPending}
            disabled={isPending}
          />
        </div>
      </div>
    </div>
  );
};
