'use client';
import React from 'react';
import { Small } from '@/components/ui';
import { useRecordingTimer } from '@/lib';

interface CheckoutTimerProps {
  initialTime?: number;
  onTimeExpired?: () => void;
}

export const CheckoutTimer: React.FC<CheckoutTimerProps> = ({
  initialTime = 605,
  onTimeExpired,
}) => {
  const handleTimeExpired = () => {
    window.alert('Your ticket reservation has expired. Please try again.');
    window.location.reload();
  };

  const { formattedTime, resetTimer } = useRecordingTimer(
    true,
    initialTime,
    () => {
      if (onTimeExpired) {
        onTimeExpired();
      } else {
        handleTimeExpired();
      }
      resetTimer();
    }
  );

  return (
    <div className="w-full flex items-center rounded-md bg-red-10 p-2 dark:bg-red-80">
      <Small className="text-red-60 dark:text-red-40">
        We&apos;ve reserved your ticket. Please complete checkout within{' '}
        <span className="text-brand-80 dark:text-brand-30">
          {formattedTime}
        </span>{' '}
        to secure your tickets.
      </Small>
    </div>
  );
};
