'use client';
import { useTheme } from 'next-themes';
import { toast } from 'react-hot-toast';

import { DiscountType, useUpdateReserveEventTicket } from '@/api/events';
import {
  colors,
  H4,
  H5,
  Image,
  MdRegularLabel,
  P,
  Skeleton,
  Small,
  SmBoldLabel,
  SmRegularLabel,
  XsBoldLabel,
} from '@/components/ui';
import { cn, formatAmount, toAmountInMajor } from '@/lib';
import { usePurchaseTicketContext } from '@/lib/contexts/purchase-ticket-context';
import { type AdditionalFees } from '@/lib/hooks/use-purchase-ticket';

import { Counter } from '../ui';
import { IoAdd } from 'react-icons/io5';
import { FiChevronRight } from 'react-icons/fi';
import { useState } from 'react';
import { Modal } from '@/components/ui';
import { AddDiscount } from '@/components/events';

export const CheckoutSummary: React.FC<{
  openAddTicketModal: () => void;
}> = ({ openAddTicketModal }) => {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';
  const {
    event,
    handleTicketQuantityChange,
    getSelectedTicketsArray,
    calculateTotalCost,
    activeDiscount,
    eventId,
  } = usePurchaseTicketContext();
  const hasDiscount = !!activeDiscount;

  const ticketCategories = event?.ticketCategories || {};
  const selectedTickets = getSelectedTicketsArray();

  const additionalFees: AdditionalFees = {
    fee: 0.05,
    ...(activeDiscount && {
      discountType: activeDiscount.discountType,
      discountValue: activeDiscount.discountValue,
    }),
  };
  const costBreakdown = calculateTotalCost(additionalFees);

  const { isPending: reservingTicket, mutate: reserveTicket } =
    useUpdateReserveEventTicket();

  const [discountVisible, setDiscountVisible] = useState<boolean>(false);

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex flex-col gap-4">
        <H5>Order summary</H5>
        <div className="flex flex-col gap-3">
          {selectedTickets.map((ticket, index) =>
            reservingTicket ? (
              <Skeleton key={index} className="h-[42px] rounded-md" />
            ) : (
              <div
                key={index}
                className="flex flex-row items-center justify-between"
              >
                <div className="flex flex-row items-center gap-2">
                  <Image
                    src={'/icons/ticket.png'}
                    width={37}
                    height={37}
                    alt=""
                  />
                  <div className="flex flex-col gap-2">
                    <MdRegularLabel>{ticket.category}</MdRegularLabel>
                    <Small className="text-grey-60 dark:text-grey-50">
                      {formatAmount(
                        toAmountInMajor(
                          ticket.cost ||
                            ticketCategories[ticket.category].convertedCost ||
                            ticketCategories[ticket.category].cost
                        )
                      )}
                    </Small>
                  </div>
                </div>
                <div className="flex flex-row items-center gap-4">
                  {ticket.isPresale && (
                    <div className="h-5 w-[58px] flex items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                      <XsBoldLabel className="text-green-60 dark:text-green-40">
                        Presale
                      </XsBoldLabel>
                    </div>
                  )}
                  <Counter
                    initialValue={ticket.quantity}
                    value={ticket.quantity}
                    minimum={1}
                    maximum={
                      ticketCategories[ticket.category].quantity > 10
                        ? 10
                        : ticketCategories[ticket.category].quantity
                    }
                    onValueChange={(quantity) => {
                      const previousTickets = [...selectedTickets];

                      const updatedTickets = [...selectedTickets];
                      const index = updatedTickets.findIndex(
                        (item) => item.category === ticket.category
                      );

                      if (index !== -1) {
                        updatedTickets[index].quantity = quantity;
                      } else {
                        updatedTickets.push({
                          category: ticket.category,
                          quantity,
                          cost: ticket.cost,
                        });
                      }
                      handleTicketQuantityChange(ticket.category, quantity);
                      reserveTicket(
                        {
                          id: event?.id || eventId,
                          reservations: updatedTickets
                            .filter(({ quantity }) => quantity !== 0)
                            .map(({ category, quantity }) => ({
                              category,
                              quantity,
                            })),
                        },
                        {
                          onError: (error) => {
                            toast.error(error.message);
                            previousTickets.forEach((t) =>
                              handleTicketQuantityChange(t.category, t.quantity)
                            );
                          },
                        }
                      );
                    }}
                    className="border border-border-subtle-light dark:border-border-subtle-dark"
                  />
                </div>
              </div>
            )
          )}

          <button
            className="h-8 w-[107px] flex flex-row items-center justify-center gap-1 rounded-[48px] bg-brand-20 dark:bg-brand-90"
            onClick={openAddTicketModal}
          >
            <SmBoldLabel className="text-brand-70 dark:text-brand-40">
              Add Ticket
            </SmBoldLabel>
            <IoAdd
              size={16}
              color={isDark ? colors.brand[40] : colors.brand[70]}
            />
          </button>
        </div>
      </div>

      <button
        className={cn(
          'flex flex-row justify-between items-center border-t-4 border-b-4 border-grey-30 dark:border-grey-70 py-4 cursor-pointer',
          hasDiscount && 'py-2'
        )}
        onClick={() => {
          setDiscountVisible(true);
        }}
      >
        <div className={cn('flex-1', hasDiscount && 'flex flex-col gap-2.5')}>
          {activeDiscount ? (
            <>
              <MdRegularLabel>
                {activeDiscount.name || activeDiscount.code}
              </MdRegularLabel>
              <SmRegularLabel className="text-grey-60 dark:text-grey-50">
                {activeDiscount.discountType === DiscountType.AMOUNT
                  ? formatAmount(toAmountInMajor(activeDiscount.discountValue))
                  : `${activeDiscount.discountValue}%`}{' '}
                discount
              </SmRegularLabel>
            </>
          ) : (
            <MdRegularLabel>Use discount code</MdRegularLabel>
          )}
        </div>
        <div className="flex items-center justify-end">
          <FiChevronRight
            size={24}
            color={isDark ? colors.white : colors.grey[100]}
          />
        </div>
      </button>

      <div className="flex flex-col gap-4">
        <H4>Payment Summary</H4>
        <div className="flex flex-row justify-between">
          <P>Ticket</P>
          <P>{formatAmount(costBreakdown.ticketSubtotal)}</P>
        </div>
        {!!costBreakdown.totalFees && (
          <div className="flex flex-row justify-between">
            <P>Service fee</P>
            <P>{formatAmount(costBreakdown.totalFees)}</P>
          </div>
        )}

        {!!costBreakdown.fees.discountAmount && (
          <div className="flex flex-row justify-between">
            <P>Discount</P>
            <P>-{formatAmount(costBreakdown.fees.discountAmount)}</P>
          </div>
        )}
        <div className="flex flex-row justify-between">
          <H4>Total</H4>
          <H4>{formatAmount(costBreakdown.total)}</H4>
        </div>
      </div>
      <Modal
        title="Add discount code"
        isOpen={discountVisible}
        onClose={() => setDiscountVisible(false)}
      >
        <AddDiscount setDiscountVisible={setDiscountVisible} />
      </Modal>
    </div>
  );
};
