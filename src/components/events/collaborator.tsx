'use client';
import Image from 'next/image';
import { type EventArtist } from '@/api/events';
import { IoCloseCircle } from 'react-icons/io5';
import { useTheme } from 'next-themes';

interface CollaboratorCardProps {
  artist: { id: string; avatar: string; name: string } | EventArtist;
  onRemove?: () => void;
  isCreator?: boolean;
  isCreation?: boolean;
  hasEventEnded?: boolean;
}

export const CollaboratorCard = ({
  artist,
  onRemove,
  isCreator,
  isCreation,
  hasEventEnded,
}: CollaboratorCardProps) => {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';
  const shouldShowRemove = isCreation || (isCreator && !hasEventEnded);
  return (
    <div key={artist.id} className="relative h-[92px] w-[95px] rounded-md">
      <div
        className="absolute top-0 left-0 right-0 h-full z-5 rounded-md"
        style={{
          background: isDark
            ? 'linear-gradient(to bottom, rgba(255,255,255,0.05), rgba(255,255,255,0))'
            : 'linear-gradient(to bottom, rgba(0,0,0,0.05), rgba(0,0,0,0))',
        }}
      />

      {shouldShowRemove && (
        <button
          onClick={onRemove}
          className="absolute -right-1 -top-1 z-10 flex items-center justify-center rounded-full bg-bg-danger-primary dark:bg-bg-danger-primary size-6"
        >
          <div className="flex items-center justify-center rounded-full border-2 border-accent-on-accent dark:border-accent-on-accent size-4">
            <IoCloseCircle size={12} color="#fff" />
          </div>
        </button>
      )}

      <div className="flex flex-col items-center justify-center gap-2.5 pt-2">
        <Image
          src={artist.avatar || '/images/users/dj-aisha.png'}
          alt={'name' in artist ? artist.name : artist.artistName}
          width={51}
          height={51}
          className="object-cover rounded-full"
        />
        <p
          className="text-md dark:text-white text-dark truncate max-w-full"
          title={'name' in artist ? artist.name : artist.artistName}
        >
          {'name' in artist ? artist.name : artist.artistName}
        </p>
      </div>
    </div>
  );
};
