'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { type Point } from '@/api';
import DatePicker from 'react-datepicker';
import { toast } from 'react-hot-toast';
import { z } from 'zod';

import { useEditEvent } from '@/api/events';
import { EventFormat, type ISingleEvent } from '@/api/events/types';
import {
  LocationInput,
  Button,
  H2,
  Modal,
  ControlledInput,
  Input,
} from '@/components/ui';
import {
  useAuth,
  useFieldBlurAndFilled,
  usePurchaseTicketContext,
} from '@/lib';
import { formatDateTime } from '@/lib';
import { Location, type LocationType } from '@/types';
import { IoLink } from 'react-icons/io5';

const schema = z.object({
  description: z.string(),
  startTime: z.date(),
  endTime: z.date(),
  onlineEventUrl: z.url('Invalid URL').optional(),
  location: Location.optional(),
  collaborators: z.array(
    z.object({ id: z.string(), name: z.string(), avatar: z.string() })
  ),
});

export type EventEditModalProps = {
  event: ISingleEvent;
  isOpen: boolean;
  onClose: () => void;
};

type EditEventType = z.infer<typeof schema>;

export function EventEditModal({
  event,
  isOpen,
  onClose,
}: EventEditModalProps) {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<EditEventType>([
      'description',
      'startTime',
      'endTime',
      'collaborators',
    ]);
  const { editType } = usePurchaseTicketContext();

  const [onlineModalVisible, setOnlineModalVisible] = useState(false);

  const { control, watch, setValue } = useForm<z.infer<typeof schema>>({
    defaultValues: {
      description: event?.description,
      ...(event?.startTime && { startTime: new Date(event?.startTime) }),
      ...(event?.endTime && { endTime: new Date(event?.endTime) }),
      collaborators:
        event?.artists?.map(({ id, artistName: name, avatar }) => ({
          id,
          name,
          avatar,
        })) ?? [],
      location: event?.location,
      onlineEventUrl: event?.onlineEventUrl,
    },
    resolver: zodResolver(schema),
    mode: 'onBlur',
  });

  const eventUrl = watch('onlineEventUrl');

  const onSelectLocation = useCallback(
    async (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: Point
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setValue('location', locationObject);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const isStartDateValid = (date: Date) => {
    const now = new Date();
    return date >= now;
  };

  const isEndDateValid = (startDate: Date, endDate: Date) => {
    return endDate >= startDate;
  };

  const startTime = watch('startTime');

  const startDatetimeLabel = startTime
    ? formatDateTime(startTime)
    : 'Start date and time';

  const endTime = watch('endTime');

  const endDatetimeLabel = endTime
    ? formatDateTime(endTime)
    : 'End date and time';

  const handleStartDateConfirm = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue(`startTime`, date);
  };

  const handleEndDateConfirm = (date: Date) => {
    const start = watch(`startTime`);
    if (!start || !isEndDateValid(new Date(start), date)) {
      alert('End date cannot be before the start date.');
      return;
    }
    setValue(`endTime`, date);
  };

  const { mutate: editEvent, isPending } = useEditEvent({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          'getEventWithSlug',
          { slug: event.slug, targetCurrency: 'NGN', userId: user?.id },
        ],
      });
      toast.success('Event updated successfully');
      onClose();
    },
    onError: (error) => toast.error(error.message),
  });

  const onSubmit = () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { collaborators: _collaborators, ...form } = watch();
    const formData = new FormData();
    Object.entries(form).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        let stringifiedValue = value as string;
        if (typeof value !== 'string') {
          stringifiedValue = JSON.stringify(value);
        }

        formData.append(key, stringifiedValue);
      }
    });
    editEvent({ form: formData, id: event.id });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="min-h-[255px] flex-1 justify-between gap-6 p-4">
        <H2>
          Edit Event
          {editType === 'desc'
            ? ' Description'
            : editType === 'date'
              ? ' Date'
              : editType === 'location'
                ? ' Location'
                : ''}
        </H2>
        {editType === 'desc' ? (
          <ControlledInput
            name="description"
            multiline
            rows={3}
            handleFieldBlur={() => handleFieldBlur('description')}
            handleFieldUnBlur={() => handleFieldUnBlur('description')}
            style={{ height: 80 }}
            label="Describe your event"
            control={control}
          />
        ) : editType === 'date' ? (
          <>
            <DatePicker
              selected={startTime}
              onChange={(date) => handleStartDateConfirm(date as Date)}
              showTimeSelect
              minDate={new Date()}
              dateFormat="MMMM d, yyyy h:mm aa"
              customInput={
                <button className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark">
                  <span
                    className={
                      startTime
                        ? 'dark:text-neutral-100'
                        : 'text-neutral-400 dark:text-neutral-500'
                    }
                  >
                    {startDatetimeLabel}
                  </span>
                </button>
              }
            />
            <button className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark">
              <span
                className={
                  endTime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {endDatetimeLabel}
              </span>
            </button>
            <DatePicker
              selected={endTime}
              onChange={(date) => handleEndDateConfirm(date as Date)}
              disabled={!startTime}
              minDate={startTime}
              dateFormat="MMMM d, yyyy h:mm aa"
              customInput={
                <button className="h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark">
                  <span
                    className={
                      endTime
                        ? 'dark:text-neutral-100'
                        : 'text-neutral-400 dark:text-neutral-500'
                    }
                  >
                    {endDatetimeLabel}
                  </span>
                </button>
              }
            />
          </>
        ) : editType === 'location' ? (
          <>
            {event.eventFormat !== EventFormat.ONLINE && (
              <LocationInput
                onSelectLocation={onSelectLocation}
                defaultValue={watch('location')}
              />
            )}
            {event.eventFormat !== EventFormat.IN_PERSON && (
              <ControlledInput
                name="onlineEventUrl"
                label="Streaming link"
                control={control}
                onClick={() => setOnlineModalVisible(true)}
                icon={
                  <IoLink
                    size={24}
                    className="text-fg-muted dark:text-fg-muted"
                  />
                }
              />
            )}
          </>
        ) : null}

        <Button
          variant="secondary"
          label="Done"
          loading={isPending}
          disabled={isPending}
          onPress={onSubmit}
          className="mb-4"
        />
      </div>

      <Modal
        isOpen={onlineModalVisible}
        onClose={() => setOnlineModalVisible(false)}
      >
        <div className="flex flex-col not-only:gap-2 p-4">
          <H2 className="text-start">Online events</H2>
          <div className="gap-6">
            <Input
              label="Enter link (URL)"
              value={eventUrl ? eventUrl : 'https://'}
              onChange={(e) => setValue('onlineEventUrl', e.target.value)}
            />

            <Button
              label="Save"
              disabled={!eventUrl}
              onPress={() => {
                setOnlineModalVisible(false);
              }}
            />
          </div>

          <Button
            label="Skip for now"
            variant="outline"
            onPress={() => setOnlineModalVisible(false)}
          />
        </div>
      </Modal>
    </Modal>
  );
}
