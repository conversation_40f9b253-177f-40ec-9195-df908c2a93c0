'use client';
import React, { useState } from 'react';
import { useTheme } from 'next-themes';
import {
  DeleteIcon,
  DiscountIcon,
  FileDownloadIcon,
  MoreHorizontal,
  semanticColors,
  ShareIcon,
} from '@/components/ui';
import { cn } from '@/lib';

type DropdownOptions =
  | 'share'
  | 'message'
  | 'delete'
  | 'discount'
  | 'export-guests';

const dropdownOptions: { label: string; value: DropdownOptions }[] = [
  { label: 'Share', value: 'share' },
  { label: 'Discount code', value: 'discount' },
  // { label: 'Message attendees', value: 'message' },
  { label: 'Export guest list', value: 'export-guests' },
  { label: 'Delete event', value: 'delete' },
];

export interface EventDetailMoreDropdownProps {
  onValueChange: (value: DropdownOptions) => void;
  disabled?: boolean;
}

export const EventDetailMoreDropdown: React.FC<
  EventDetailMoreDropdownProps
> = ({ onValueChange, disabled = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const renderOptionIcon = (value: DropdownOptions) => {
    switch (value) {
      case 'share':
        return (
          <ShareIcon
            color={
              isDark
                ? semanticColors.fg.base.dark
                : semanticColors.fg.base.light
            }
          />
        );
      case 'discount':
        return (
          <DiscountIcon
            color={
              isDark
                ? semanticColors.fg.base.dark
                : semanticColors.fg.base.light
            }
          />
        );
      case 'export-guests':
        return (
          <FileDownloadIcon
            color={
              isDark
                ? semanticColors.fg.base.dark
                : semanticColors.fg.base.light
            }
          />
        );
      case 'delete':
        return (
          <DeleteIcon
            color={
              isDark
                ? semanticColors.fg.danger.dark
                : semanticColors.fg.danger.light
            }
          />
        );
      default:
        return null;
    }
  };

  const handleOptionClick = (value: DropdownOptions) => {
    onValueChange(value);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          'size-8 flex items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark p-0 cursor-pointer',
          disabled && 'opacity-50'
        )}
      >
        <MoreHorizontal
          color={
            isDark
              ? semanticColors.accent.bold.dark
              : semanticColors.accent.bold.light
          }
          fill={
            isDark ? semanticColors.fg.base.dark : semanticColors.fg.base.light
          }
        />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />

          <div
            className={cn(
              'absolute top-full right-0 mt-2 w-[220px] bg-accent-subtle-light dark:bg-accent-subtle-dark border-0 rounded-lg shadow-lg z-50'
            )}
          >
            {dropdownOptions.map(({ label, value }) => (
              <button
                key={value}
                onClick={() => handleOptionClick(value)}
                className={cn(
                  'w-full cursor-pointer flex items-center gap-2 px-4 py-2.5 text-base text-left text-fg-base-light dark:text-fg-base-dark hover:bg-white/10',
                  value === 'delete' &&
                    'text-fg-danger-light dark:text-fg-danger-dark'
                )}
              >
                {renderOptionIcon(value)}
                {label}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
