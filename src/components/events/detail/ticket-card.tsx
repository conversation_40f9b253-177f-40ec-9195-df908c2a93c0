import React from 'react';

import { type TicketCategories } from '@/api/events';
import { Image, XsBoldLabel } from '@/components/ui';
import { formatCurrency } from '@/lib/utils';

import { EventTicketMoreDropdown } from './ticket-dropdown';

type TicketCategory = TicketCategories[keyof TicketCategories];

type EventTicketCardProps = {
  ticket: TicketCategory;
  index: number;
  category: string;
  onEdit: () => void;
  onDelete?: () => void;
  isDark: boolean;
  disabled?: boolean;
};

function PresaleBadge() {
  return (
    <div className="absolute right-2 top-1 z-10 h-5 w-[84px] flex items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
      <XsBoldLabel className="text-xs text-green-60 dark:text-green-40">
        Presale incl.
      </XsBoldLabel>
    </div>
  );
}

export const EventTicketCard = ({
  ticket,
  category,
  onEdit,
  onDelete,
  disabled,
}: EventTicketCardProps) => {
  const handleDropdownChange = (value: 'edit' | 'delete') => {
    if (value === 'edit') {
      onEdit();
    } else if (value === 'delete' && onDelete) {
      onDelete();
    }
  };

  return (
    <div className="relative h-[98px] flex flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark">
      <div className="flex flex-1 flex-row items-center gap-x-2">
        <div className="relative size-10">
          <Image src="/icons/disc.png" alt="" fill className="object-contain" />
        </div>

        <div className="flex flex-col gap-y-2">
          <span className="font-bold text-fg-base-light dark:text-fg-base-dark">
            {category}
          </span>
          <span className="text-fg-subtle-light dark:text-fg-subtle-dark">
            {formatCurrency(ticket.cost)}
          </span>
          {ticket.description && (
            <span className="text-fg-subtle-light dark:text-fg-subtle-dark">
              {ticket.description.length > 30
                ? ticket.description.slice(0, 30) + '...'
                : ticket.description}
            </span>
          )}
        </div>
      </div>
      {ticket.hasPresale && <PresaleBadge />}

      <div className="relative flex flex-row items-center gap-x-2">
        <EventTicketMoreDropdown
          onValueChange={handleDropdownChange}
          triggerClassName="size-6 bg-transparent dark:bg-transparent border-transparent"
          itemClassName="px-4 py-2.5 flex justify-between h-11"
          disabled={disabled}
        />
      </div>
    </div>
  );
};
