import { useTheme } from 'next-themes';
import React from 'react';

import { colors, DeleteIcon, semanticColors } from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/picker';
import { cn } from '@/lib';
import { FiEdit } from 'react-icons/fi';
import { FiMoreHorizontal } from 'react-icons/fi';
type DropdownOptions = 'edit' | 'delete';

const dropdownOptions: { label: string; value: DropdownOptions }[] = [
  { label: 'Edit ticket', value: 'edit' },
  { label: 'Delete ticket', value: 'delete' },
];

export interface EventTicketMoreDropdownProps {
  onValueChange: (value: DropdownOptions) => void;
  value?: DropdownOptions;
  disabled?: boolean;
  triggerClassName?: string;
  contentClassName?: string;
  itemClassName?: string;
  itemTextClassName?: string;
  contentInsets?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
}

export const EventTicketMoreDropdown: React.FC<
  EventTicketMoreDropdownProps
> = ({
  onValueChange,
  disabled = false,
  triggerClassName,
  contentClassName,
  itemClassName,
  itemTextClassName = 'text-grey-100 dark:text-white',
}) => {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const renderOptionIcon = (value: DropdownOptions) => {
    switch (value) {
      case 'edit':
        return (
          <FiEdit
            name="edit"
            size={16}
            color={
              isDark
                ? semanticColors.fg.base.dark
                : semanticColors.fg.base.light
            }
          />
        );
      case 'delete':
        return (
          <DeleteIcon
            color={
              isDark
                ? semanticColors.fg.danger.dark
                : semanticColors.fg.danger.light
            }
          />
        );
      default:
        return null;
    }
  };

  return (
    <Select
      onValueChange={(option) => {
        if (option && !disabled) {
          onValueChange(option as DropdownOptions);
        }
      }}
      disabled={disabled}
    >
      <SelectTrigger
        className={cn(
          'size-8 rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark items-center justify-center p-0',
          disabled && 'opacity-50',
          triggerClassName
        )}
        hideChevron
        disabled={disabled}
      >
        <FiMoreHorizontal color={isDark ? colors.white : colors.grey[100]} />
      </SelectTrigger>

      <SelectContent
        className={cn(
          'w-[220px] bg-black/45 border-0 dark:bg-black/70 rounded-lg',
          contentClassName
        )}
        // position="item-aligned"
      >
        <SelectGroup>
          {dropdownOptions.map(({ label, value }) => (
            <SelectItem
              key={value}
              value={value}
              textClassName={cn(
                'text-fg-base-light dark:text-fg-base-dark',
                value === 'delete' &&
                  'text-fg-danger-light dark:text-fg-danger-dark',
                itemTextClassName
              )}
              className={itemClassName}
              hasCheck={false}
              customIcon={renderOptionIcon(value as DropdownOptions)}
            >
              {label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};
