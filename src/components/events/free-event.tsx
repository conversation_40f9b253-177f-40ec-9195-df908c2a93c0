'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { getQueryClient } from '@/api';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { z } from 'zod';

import { useEventRegistration } from '@/api/events';
import { ConfirmationDialog } from '@/components/dialogs';
import { AgeRangeSelector } from '@/components/select/age-range';
import { CountrySelector } from '@/components/select/country';
import { GenderSelector } from '@/components/select/gender';
import {
  Button,
  ControlledInput,
  H1,
  H5,
  MdRegularLabel,
} from '@/components/ui';
import {
  kebabToSnakeCase,
  SUPPORTED_COUNTRIES,
  useLoggedInUser,
  usePurchaseTicketContext,
} from '@/lib';
import { IoChevronBack } from 'react-icons/io5';

enum FieldType {
  alpha_numeric = 'alpha_numeric',
  numeric = 'numeric',
  age_range_selector = 'age_range_selector',
  country_selector = 'country_selector',
  sex_selector = 'sex_selector',
}

const schema = z.object({
  fullName: z.string().trim().min(1, 'Full name is required'),
  email: z.email('Invalid email'),
  extraFields: z
    .array(
      z.object({
        name: z.string(),
        type: z.enum(FieldType),
        value: z.string().min(1, 'This field is required'),
      })
    )
    .optional(),
});

type RegistrationFormType = z.infer<typeof schema>;

export const FreeEvent = ({
  setLayoutCurrentStep,
}: {
  setLayoutCurrentStep: React.Dispatch<
    React.SetStateAction<'details' | 'next' | 'success'>
  >;
}) => {
  const { data: user } = useLoggedInUser();
  const router = useRouter();
  const queryClient = getQueryClient();
  const { event, eventId } = usePurchaseTicketContext();
  const [confirmRegisterVisible, setConfirmRegisterVisible] = useState(false);

  const form = useForm<RegistrationFormType>({
    defaultValues: {
      fullName: user ? user?.fullName : '',
      email: user ? user?.email : '',
      extraFields:
        event?.registrationFields?.map((field) => ({
          name: field.name,
          type: kebabToSnakeCase(field.type) as FieldType,
          value: '',
        })) || [],
    },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = form;
  const { fields } = useFieldArray({ control, name: 'extraFields' });

  const { mutate: registerEvent, isPending } = useEventRegistration();

  const onSubmit = (data: RegistrationFormType) => {
    registerEvent(
      {
        eventId,
        ...data,
      },
      {
        onSuccess: () => {
          setConfirmRegisterVisible(false);
          queryClient.invalidateQueries({ queryKey: ['getEventWithSlug'] });
          toast.success('Event registration successful');
          router.back();
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  return (
    <>
      <div className="md:max-h-[calc(100dvh-160px)] md:min-h-[calc(100dvh-160px)] md:overflow-y-auto flex-1">
        <div className="px-4 pb-6 pt-4 flex flex-col gap-y-4">
          <H1>{`Register for ${event?.title || 'Event'}`}</H1>
          <H5>Fill in your details below to secure your spot.</H5>
          <ControlledInput
            name="fullName"
            label="Full name"
            control={control}
            disabled={!!user}
            readOnly={!!user}
          />
          <ControlledInput
            name="email"
            label="Email"
            control={control}
            disabled={!!user}
            readOnly={!!user}
          />
          {fields.map((field, idx) => {
            const fieldName = `extraFields.${idx}.value` as const;

            switch (field.type) {
              case FieldType.alpha_numeric:
                return (
                  <ControlledInput
                    key={field.id}
                    name={fieldName}
                    label={field.name}
                    control={control}
                  />
                );
              case FieldType.numeric:
                return (
                  <ControlledInput
                    key={field.id}
                    name={fieldName}
                    label={field.name}
                    control={control}
                    type="numeric"
                  />
                );
              case FieldType.sex_selector:
                return (
                  <GenderSelector
                    key={field.id}
                    control={control}
                    name={fieldName}
                    placeholder={field.name}
                    error={errors.extraFields?.[idx]?.value?.message}
                    testID="gender-input"
                    className="mb-2"
                  />
                );
              case FieldType.country_selector:
                return (
                  <CountrySelector
                    key={field.id}
                    control={control}
                    name={fieldName}
                    countries={SUPPORTED_COUNTRIES}
                    placeholder={field.name}
                    className="mb-2"
                  />
                );
              case FieldType.age_range_selector:
                return (
                  <AgeRangeSelector
                    key={field.id}
                    control={control}
                    name={fieldName}
                    placeholder={field.name}
                    error={errors.extraFields?.[idx]?.value?.message}
                    testID="age-range-input"
                    className="mb-2"
                  />
                );
              default:
                return null;
            }
          })}
          <div className="flex items-center justify-between gap-2 p-4 border-t sticky bottom-0 md:bottom-4 bg-bg-canvas-light dark:bg-bg-canvas-dark">
            <Button
              label="Previous"
              variant="secondary"
              className="md:mx-auto"
              onPress={() => {
                setLayoutCurrentStep('details');
              }}
            />
            <Button
              label="Register"
              disabled={!isValid || isPending}
              className="md:mx-auto"
              loading={isPending}
              onPress={() => setConfirmRegisterVisible(true)}
            />
          </div>
        </div>
      </div>
      <ConfirmationDialog
        visible={confirmRegisterVisible}
        message="Are you sure you want to register for this event?"
        onCancel={() => setConfirmRegisterVisible(false)}
        onConfirm={handleSubmit(onSubmit)}
        cancelLabel="Dismiss"
        btnDisabled={!isValid || isPending}
        isConfirming={isPending}
      />
    </>
  );
};
