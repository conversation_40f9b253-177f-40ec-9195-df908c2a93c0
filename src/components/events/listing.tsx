'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  useGetEvents,
  useGetEventCategories,
  EventFormat,
  useValidateEventAccessCode,
} from '@/api';
import { PortraitEventCard } from '@/components/cards';
import {
  MdBoldLabel,
  LocationInput,
  Pagination,
  DatePicker,
  colors,
  LockIcon,
  EmptyState,
  Button,
} from '@/components/ui';
import { cn, useEventFilters, useSearch } from '@/lib';
import dayjs from 'dayjs';
import { Skeleton } from '@/components/skeletons';
import { ConfirmationDialog } from '@/components/dialogs';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'react-hot-toast';
import { getQueryClient } from '@/api';

import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';

const schema = z.object({
  accessCode: z.string(),
});

export const EventListing = () => {
  const queryClient = getQueryClient();
  const {
    categoryId,
    currentPage,
    variables,
    pageSize,
    setCategory,
    setPage,
    setDate,
    setLocation,
  } = useEventFilters();

  const { data: categoriesData, isLoading: isLoadingCategories } =
    useGetEventCategories();
  const categories = categoriesData ?? [];

  const router = useRouter();
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const {
    data: eventsData,
    isLoading,
    isError,
    error,
  } = useGetEvents({
    variables,
    refetchInterval: 60 * 1000,
    enabled: categoryId !== 'Private',
  });

  const events_ = eventsData?.events ?? [];
  const { query } = useSearch();
  const events = events_.filter((e) => {
    const keyword = query.toLowerCase();
    const categoryName =
      categories.find((c) => c.id === e.categoryId)?.category?.toLowerCase() ||
      '';

    return (
      e.title.toLowerCase().includes(keyword) ||
      categoryName.includes(keyword) ||
      e.location?.landmark?.toLowerCase().includes(keyword) ||
      e.location?.address?.toLowerCase().includes(keyword) ||
      e.organizer?.fullName?.toLowerCase().includes(keyword)
    );
  });
  // const totalEvents = eventsData?.total ?? 0;
  const totalEvents = events.length;
  const totalPages = Math.ceil(totalEvents / pageSize);

  const {
    control,
    getValues,
    formState: { isValid },
    reset,
  } = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  const { mutate: validateAccessCode, isPending: isValidatingAccessCode } =
    useValidateEventAccessCode({
      onSuccess: ({ slug, accessGranted, token }) => {
        if (!accessGranted) toast.success('Access code invalid');

        queryClient.setQueryData(['event-access-token', slug], token);

        setAccessCodeModalVisible(false);
        reset();
        toast.success('Access code valid');

        router.push(`/events/${slug}`);
      },
      onError: (error) => toast.error(error.message),
    });

  const [accessCodeModalVisible, setAccessCodeModalVisible] = useState(false);

  return (
    <div className="space-y-4">
      <DatePicker
        onSelected={(d) => setDate(d ? dayjs(d).format('YYYY-MM-DD') : null)}
      />
      <div className="px-4">
        <LocationInput
          onSelectLocation={setLocation}
          borderRadius={9999}
          hasBorder={false}
          hasCurrentLocationIcon={false}
          className="bg-bg-muted-light dark:bg-bg-muted-dark rounded-full"
        />
      </div>

      <div className="max-h-12 flex flex-row items-center">
        {isLoadingCategories && (
          <div className="flex flex-row gap-4 flex-grow-0 shrink-0 snap-center rounded-none p-4">
            <Skeleton className="h-10 w-40" />
            <Skeleton className="h-10 w-40" />
            <Skeleton className="h-10 w-40" />
            <Skeleton className="h-10 w-40" />
            <Skeleton className="h-10 w-40" />
            <Skeleton className="h-10 w-40" />
          </div>
        )}
        {categoriesData && (
          <button
            className={cn(
              'h-12 flex justify-center items-center mr-4 cursor-pointer',
              categoryId === 'Private' &&
                'border-b border-b-fg-error-light dark:border-b-fg-error-dark'
            )}
            onClick={() => setCategory('Private', 'Private')}
          >
            <LockIcon
              width={20}
              color={isDark ? colors.red[40] : colors.red[60]}
            />
          </button>
        )}
        {categoriesData && (
          <div className="flex overflow-x-auto whitespace-nowrap scrollbar-hide">
            {[{ id: 'all', category: 'All' }, ...(categories || [])].map(
              ({ category, id }, index) => (
                <button
                  key={index}
                  className={cn(
                    'h-12 px-2 inline-flex items-center justify-center shrink-0 cursor-pointer',
                    index !== 0 && 'ml-8',
                    categoryId === id && 'border-b border-b-brand-60'
                  )}
                  onClick={() => setCategory(id, category)}
                >
                  <MdBoldLabel
                    className={cn(
                      'text-grey-50 dark:text-grey-60',
                      categoryId === id && 'text-brand-60 dark:text-brand-60'
                    )}
                  >
                    {category}
                  </MdBoldLabel>
                </button>
              )
            )}
          </div>
        )}
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Skeleton className="aspect-[4/4]" />
          <Skeleton className="aspect-[4/4]" />
          <Skeleton className="aspect-[4/4]" />
          <Skeleton className="aspect-[4/4]" />
        </div>
      ) : (
        <div
          className={cn(
            'flex-1 flex flex-col gap-4 py-4',
            categoryId === 'Private' && 'pt-8'
          )}
        >
          {categoryId === 'Private' ? (
            <div className="flex self-center h-[40px]">
              <Button
                label="Enter event code"
                variant="secondary"
                className="h-8 pl-7"
                iconClassName="size-4 left-2"
                icon={
                  <LockIcon
                    width={16}
                    color={isDark ? colors.brand[40] : colors.brand[70]}
                  />
                }
                onPress={() => setAccessCodeModalVisible(true)}
              />
            </div>
          ) : isError ? (
            <div className="flex justify-center items-center h-[40px]">
              Error: {error?.message}
            </div>
          ) : events.length === 0 ? (
            <div className="flex justify-center items-center py-12">
              <EmptyState />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {events.map((event) => (
                <PortraitEventCard
                  key={event.id}
                  {...event}
                  id={event.id.toString()}
                  startTime={event.startTime}
                  bannerUrl={
                    event.bannerUrl ||
                    '/images/gradient-bg/portrait-event-gradient.png'
                  }
                  title={event.title}
                  hasPhysicalLocation={event.eventFormat !== EventFormat.ONLINE}
                />
              ))}
            </div>
          )}
        </div>
      )}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setPage}
      />
      <ConfirmationDialog
        visible={accessCodeModalVisible}
        title="Event code"
        message="Enter private event code below"
        control={control}
        // inputLabel="Access Code"
        inputName="accessCode"
        inputIcon={
          <LockIcon color={isDark ? colors.grey[60] : colors.grey[50]} />
        }
        onCancel={() => setAccessCodeModalVisible(false)}
        confirmLabel="Continue"
        onConfirm={() => validateAccessCode({ code: getValues('accessCode') })}
        isConfirming={isValidatingAccessCode}
        btnDisabled={!isValid}
      />
    </div>
  );
};
