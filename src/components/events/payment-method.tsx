'use client';
import { useTheme } from 'next-themes';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { colors, H5, MdRegularLabel, SmRegularLabel } from '@/components/ui';
import { RadioGroup, RadioGroupItem } from '@/components/ui';
import {
  type PurchaseTicketFormType,
  formatAmount,
  toAmountInMajor,
  useLoggedInUser,
} from '@/lib';

import { LoadingScreen } from '../loaders';
import { IoWalletOutline } from 'react-icons/io5';
import { MdWeb } from 'react-icons/md';

export const PaymentMethod = () => {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const { data: user, isLoading: isUserFetching } = useLoggedInUser();

  const { watch, setValue } = useFormContext<PurchaseTicketFormType>();
  const paymentMethod = watch('paymentMethod');

  const onValueChange = (value: string) =>
    setValue('paymentMethod', value as PurchaseTicketFormType['paymentMethod']);

  if (isUserFetching) return <LoadingScreen />;

  return (
    <RadioGroup
      value={paymentMethod}
      onChange={onValueChange}
      className="flex flex-col gap-4"
    >
      <H5>Payment method</H5>

      {user && (
        <button
          className="flex flex-row items-center justify-between px-4 cursor-pointer"
          onClick={() => {
            setValue('paymentMethod', 'wallet');
          }}
        >
          <div className="flex-1 flex flex-row items-center gap-4">
            <div className="flex items-center gap-x-4 py-3">
              <div className="bg-accent-subtle-light dark:bg-accent-subtle-dark p-2 rounded-full size-10 flex items-center justify-center">
                <IoWalletOutline
                  size={16}
                  color={isDark ? colors.brand[40] : colors.brand[70]}
                />
              </div>
            </div>

            <div className="flex flex-col space-y-2 py-px items-start">
              <MdRegularLabel>Wallet</MdRegularLabel>
              <SmRegularLabel className="text-grey-60 dark:text-grey-50">
                Available balance -{' '}
                {formatAmount(toAmountInMajor(user?.walletBalance || 0))}
              </SmRegularLabel>
            </div>
          </div>
          <RadioGroupItem
            value="wallet"
            aria-selected={paymentMethod === 'wallet'}
            aria-labelledby={`label-for-wallet`}
          />
        </button>
      )}

      <button
        className="flex flex-row items-center justify-between px-4 cursor-pointer"
        onClick={() => {
          setValue('paymentMethod', 'online');
        }}
      >
        <div className="flex-1 flex flex-row items-center gap-4">
          <div className="flex items-center gap-x-4 py-3">
            <div className="bg-accent-subtle-light dark:bg-accent-subtle-dark p-2 rounded-full size-10 flex items-center justify-center">
              <MdWeb
                size={16}
                color={isDark ? colors.brand[40] : colors.brand[70]}
              />
            </div>
          </div>

          <div className="flex flex-col space-y-2 py-px items-start">
            <MdRegularLabel>Pay Online</MdRegularLabel>
          </div>
        </div>
        <RadioGroupItem
          value="online"
          aria-selected={paymentMethod === 'online'}
          aria-labelledby={`label-for-online`}
        />
      </button>
    </RadioGroup>
  );
};
