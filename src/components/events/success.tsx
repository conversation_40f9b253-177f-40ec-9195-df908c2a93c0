'use client';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { formatDate, formatTime, formatDateTime } from '@/lib/utils';
import { isSameDay } from 'date-fns';
import { ISingleEvent, EventFormat } from '@/api/events';
import {
  Button,
  H1,
  H2,
  H5,
  MdRegularLabel,
  SmRegularLabel,
} from '@/components/ui';

export const TicketSuccessPage = ({
  event,
}: {
  event: ISingleEvent;
  slug: string;
}) => {
  const hasPhysicalLocation = event?.eventFormat !== EventFormat.ONLINE;

  const eventAddress = hasPhysicalLocation
    ? event?.location.landmark ||
      event?.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : event.onlineEventUrl || 'Online';

  const router = useRouter();

  return (
    <main className="bg-black text-white px-6 py-6">
      <div className="text-sm text-muted-foreground mb-6">
        <span className="font-medium text-white">Events</span>
        <span className="mx-1">/</span>
        <span className="text-white font-medium">{event.title}</span>
        <span className="mx-1">/</span>
        <span className="text-muted-foreground">Checkout</span>
      </div>

      <div className="grid lg:grid-cols-2">
        <div className="space-y-4">
          <Image
            src="/images/success.png"
            alt="Event Banner"
            width={148}
            height={148}
            className="object-cover"
            unoptimized
          />
          <div className="space-y-2">
            <H2>Thank you!</H2>
            <MdRegularLabel>Your ticket purchase was successful</MdRegularLabel>
            <H1>{event.title}</H1>
            <div className="space-y-[14px]">
              <div className="space-y-2">
                <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                  DATE
                </SmRegularLabel>
                <H5>
                  {isSameDay(
                    new Date(event.startTime),
                    new Date(event.endTime)
                  ) ? (
                    <>
                      {formatDate(event.startTime)}{' '}
                      {formatTime(event.startTime)} –{' '}
                      {formatTime(event.endTime)}
                    </>
                  ) : (
                    <>
                      {formatDateTime(new Date(event.startTime))} –{' '}
                      {formatDateTime(new Date(event.endTime))}
                    </>
                  )}
                </H5>
              </div>

              <div className="space-y-2">
                <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
                  LOCATION
                </SmRegularLabel>
                <H5 className="text-white font-medium">{eventAddress}</H5>
              </div>
            </div>
          </div>
          <Button label="View Ticket" onPress={() => router.push(`/tickets`)} />
        </div>

        <div className="px-4">
          <Image
            src="/images/barcode"
            alt="Organizer"
            width={340}
            height={340}
            className="rounded-full"
          />
        </div>
      </div>
    </main>
  );
};
