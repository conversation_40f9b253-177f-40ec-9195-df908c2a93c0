'use client';

import { ControlledInput } from '@/components/ui';
import { type PurchaseTicketFormType } from '@/lib';
import { useFormContext } from 'react-hook-form';

export const TicketForm = () => {
  const { control } = useFormContext<PurchaseTicketFormType>();

  return (
    <div className="flex flex-col gap-4">
      <ControlledInput
        control={control}
        name="fullName"
        label="Full name"
        required
      />
      <ControlledInput control={control} name="email" label="Email" required />
    </div>
  );
};
