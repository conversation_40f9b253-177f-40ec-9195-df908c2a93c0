'use client';
import { useState } from 'react';
import toast from 'react-hot-toast';
import {
  getRemainingTimerSeconds,
  PurchaseTicketFormType,
  useAuth,
  usePurchaseTicketContext,
} from '@/lib';
import { EmptyState, H1, MdRegularLabel, Modal } from '@/components/ui';
import { Button } from '@/components/ui';
import { type AdditionalFees } from '@/lib';

import {
  usePurchaseEventTicket,
  useUpdateReserveEventTicket,
} from '@/api/events';
import { usePaystackCheckout } from '@/lib/hooks/use-paystack-checkout';
import { getQueryClient } from '@/api';
import { CheckoutSummary, CheckoutTimer, PaymentMethod } from './';
import { useFormContext } from 'react-hook-form';
import { GetTicketModal } from '@/components/modals';
import Link from 'next/link';
import { IoChevronBack } from 'react-icons/io5';

export const TicketedEventPage = ({
  expiresAt,
  setLayoutCurrentStep,
}: {
  expiresAt: string | null;
  setLayoutCurrentStep: React.Dispatch<
    React.SetStateAction<'details' | 'next' | 'success'>
  >;
}) => {
  const { user } = useAuth();
  const queryClient = getQueryClient();

  const [getTicketModalVisible, setGetTicketModalVisible] = useState(false);

  const { watch } = useFormContext<PurchaseTicketFormType>();
  const paymentMethod = watch('paymentMethod');

  const {
    event,
    handleTicketQuantityChange,
    hasSelectedTickets,
    selectedTickets: tickets,
    getSelectedTicketsArray,
    eventId,
    calculateTotalCost,
    activeDiscount,
  } = usePurchaseTicketContext();
  const selectedTickets = getSelectedTicketsArray();

  const initialTime = getRemainingTimerSeconds({
    expiresAt: expiresAt || undefined,
    defaultTimer: 11 * 60,
  });

  const { isPending: reservingTicket, mutate: reserveTicket } =
    useUpdateReserveEventTicket({
      onSuccess: () => setGetTicketModalVisible(false),
      onError: (error) => toast.error(error.message),
    });

  const [currentStep, setCurrentStep] = useState<'summary' | 'payment'>(
    'summary'
  );

  const additionalFees: AdditionalFees = {
    fee: 0.05,
    ...(activeDiscount && {
      discountType: activeDiscount.discountType,
      discountValue: activeDiscount.discountValue,
    }),
  };

  const { total: totalWithDiscount } = calculateTotalCost(additionalFees);

  const { mutate: payWithWallet, isPending } = usePurchaseEventTicket();

  const { checkout } = usePaystackCheckout({
    amount: totalWithDiscount,
    ...(activeDiscount && { discountCode: activeDiscount.code }),
  });

  const handlePayment = () => {
    if (paymentMethod === 'wallet') {
      payWithWallet(
        {
          id: event?.id || eventId,
          category: selectedTickets.map(
            ({ category, quantity, isPresale }) => ({
              category,
              quantity,
              isPresale,
            })
          ),
          userCurrency: 'NGN',
          ...(activeDiscount && { discountCode: activeDiscount.code }),
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({
              queryKey: ['getUserTickets', user?.id],
            });
            queryClient.invalidateQueries({
              queryKey: ['getEvent'],
            });
            queryClient.invalidateQueries({
              queryKey: ['getEventWithSlug'],
            });
            setLayoutCurrentStep('success');
          },
          onError: (error) => toast.error(error.message),
        }
      );
    } else {
      checkout();
    }
  };

  if (!event) return <EmptyState />;

  return (
    <>
      <div className="w-full h-full flex flex-col gap-6 p-4">
        <div className="flex-1 flex flex-col gap-6">
          <button
            className="flex flex-row items-center gap-2 cursor-pointer text-fg-link"
            onClick={() => setLayoutCurrentStep('details')}
          >
            <IoChevronBack size={24} />
            <MdRegularLabel className="text-fg-link">Go Back</MdRegularLabel>
          </button>
          <H1>Checkout</H1>
          {initialTime && <CheckoutTimer initialTime={initialTime} />}
          {currentStep === 'summary' ? (
            <CheckoutSummary
              openAddTicketModal={() => setGetTicketModalVisible(true)}
            />
          ) : (
            <>
              <button
                className="flex flex-row items-center gap-2 cursor-pointer text-fg-link"
                onClick={() => setCurrentStep('summary')}
              >
                <IoChevronBack size={24} />
                <MdRegularLabel className="text-fg-link">
                  Go Back to Summary
                </MdRegularLabel>
              </button>
              <PaymentMethod />
            </>
          )}
        </div>
        <div className="sticky bottom-0 md:bottom-4 mt-auto bg-bg-primary-light dark:bg-bg-primary-dark py-2">
          <Button
            label="Continue"
            className="md:mx-auto"
            disabled={selectedTickets.length < 1 || isPending}
            loading={isPending}
            onPress={() => {
              if (currentStep === 'payment') {
                handlePayment();
              } else {
                setCurrentStep('payment');
              }
            }}
          />
        </div>
      </div>
      <Modal
        isOpen={getTicketModalVisible}
        onClose={() => setGetTicketModalVisible(false)}
      >
        <div className="relative min-h-80 flex-1 flex items-center justify-end rounded-t-lg pt-4">
          <GetTicketModal
            visible
            onClose={() => setGetTicketModalVisible(false)}
            event={event}
            handleTicketQuantityChange={handleTicketQuantityChange}
            hasSelectedTickets={hasSelectedTickets}
            selectedTickets={tickets}
            onProceed={() =>
              reserveTicket({
                id: event?.id || eventId,
                reservations: selectedTickets
                  .filter(({ quantity }) => quantity !== 0)
                  .map(({ category, quantity }) => ({
                    category,
                    quantity,
                  })),
              })
            }
            isLoading={reservingTicket}
          />
        </div>
      </Modal>
    </>
  );
};
