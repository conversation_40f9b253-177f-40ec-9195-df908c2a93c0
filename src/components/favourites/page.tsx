'use client';
import { useState, useMemo } from 'react';

import { IEvent, useGetUserFavourites, UserObjectData } from '@/api';
import {
  FavouriteCreatorsTab,
  FavouriteEventsTab,
} from '@/components/tab-views';
import { AppTab, H1, P } from '@/components/ui';
import { type TabScreenItem } from '@/types';

export const FavouritesPage = () => {
  const [index, setIndex] = useState(0);

  const { isLoading: isEventsLoading, data: events } = useGetUserFavourites({
    variables: {
      type: 'EVENT',
    },
    enabled: index === 0,
  });

  const { isLoading: isAccountsLoading, data: accounts } = useGetUserFavourites(
    {
      variables: {
        type: 'ACCOUNT',
      },
      enabled: index === 1,
    }
  );

  const tabItems: TabScreenItem[] = useMemo(() => {
    return [
      {
        key: 'EVENT',
        title: 'Events',
        component: () => (
          <FavouriteEventsTab
            events={events as IEvent[] | undefined}
            index={index}
            isLoading={isEventsLoading}
          />
        ),
      },
      {
        key: 'ACCOUNT',
        title: 'Accounts',
        component: () => (
          <FavouriteCreatorsTab
            accounts={accounts as UserObjectData[] | undefined}
            index={index}
            isLoading={isAccountsLoading}
          />
        ),
      },
    ];
  }, [accounts, events, index, isAccountsLoading, isEventsLoading]);

  return (
    <div className="flex-1 ">
      <div className="gap-1 md:px-4 pb-4">
        <H1>Favourites</H1>
        <P className="text-grey-60 dark:text-grey-50">
          All your favourite events and accounts in one place.
        </P>
      </div>
      <AppTab
        items={tabItems}
        tabIndex={index}
        tabSetIndex={(i) => setIndex(i)}
      />
    </div>
  );
};
