'use client';

import React from 'react';
import ChatIcon from '~/svgs/tabs/chat.svg';
import { cn } from '@/lib/utils';
import { RegularAvatar } from '@/components/avatars';
import { MdBoldLabel, Small } from '@/components/ui/typography';

interface UserFollowingItemProps {
  avatar: string;
  displayName: string;
  /** User's username/handle (without @) */
  username: string;
  className?: string;
  avatarSize?: number;
  showChatIcon?: boolean;
  chatIconSize?: number;
  onClick?: () => void;
  onChatClick?: () => void;
  hasAvatarRing?: boolean;
}

export const UserFollowingItem: React.FC<UserFollowingItemProps> = ({
  avatar,
  displayName,
  username,
  className,
  avatarSize = 64,
  showChatIcon = true,
  chatIconSize = 24,
  onClick,
  onChatClick,
  hasAvatarRing = true,
}) => {
  const handleChatClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChatClick?.();
  };

  return (
    <div
      className={cn(
        'h-16 flex items-center gap-2.5 text-fg-base-light dark:text-fg-base-dark',
        onClick &&
          'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
        className
      )}
      onClick={onClick}
    >
      <RegularAvatar
        avatar={avatar}
        size={avatarSize}
        hasRing={hasAvatarRing}
        alt={`${displayName}'s avatar`}
      />

      <div className="flex flex-1 flex-col gap-1">
        <MdBoldLabel themed weight="bold">
          {displayName}
        </MdBoldLabel>
        <Small themed className="text-fg-muted-light dark:text-fg-muted-dark">
          @{username}
        </Small>
      </div>

      {showChatIcon && (
        <ChatIcon
          width={chatIconSize}
          height={chatIconSize}
          className="cursor-pointer hover:opacity-70 transition-opacity"
          onClick={handleChatClick}
        />
      )}
    </div>
  );
};
