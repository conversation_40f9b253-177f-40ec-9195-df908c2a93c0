import React from 'react';
import { useFormContext } from 'react-hook-form';
import { IoChevronDown, IoCheckmark } from 'react-icons/io5';

import { useGuessNuban, useVerifyNuban } from '@/api/transactions';
import { Modal, P, colors } from '@/components/ui';
import { ControlledInput } from '@/components/ui';
import { SearchInput } from '@/components/ui/search-input';
import { Spinner } from '@/components/ui/spinner';
import { type BankFormType as BankSetupFormType } from '@/lib';

export const BankForm: React.FC = () => {
  const { watch, control, setValue, reset, formState } =
    useFormContext<BankSetupFormType>();

  const [bankModalOpen, setBankModalOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');

  const isAcctNoFieldDirty = formState.dirtyFields.accountNumber;
  const selectedBank = watch('bankName');
  const accountNumber = watch('accountNumber');
  const bankCode = watch('bankCode');

  const { data, isLoading: isVerifyingNuban } = useVerifyNuban({
    variables: { bankCode, accountNumber },
    enabled: accountNumber.length === 10 && !!bankCode && isAcctNoFieldDirty,
  });
  const accountDetails = data?.[0];

  React.useEffect(() => {
    setValue('accountName', accountDetails?.account_name || '');
  }, [accountDetails, setValue]);

  React.useEffect(() => {
    if (isAcctNoFieldDirty) {
      setValue('accountName', '');
      setValue('bankName', '');
      setValue('bankCode', '');
    } else {
      reset(undefined, { keepDirtyValues: true });
    }
  }, [isAcctNoFieldDirty, reset, setValue]);

  const { data: possibleBanks, isLoading: isGuessingBanks } = useGuessNuban({
    variables: { accountNumber },
    enabled: accountNumber.length === 10,
  });

  const filteredBanks = (possibleBanks ?? []).filter((bank) =>
    bank.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectBank = React.useCallback(
    (bank: { label: string; value: string; code: string }) => {
      setValue('bankName', bank.value);
      setValue('bankCode', bank.code);
      setBankModalOpen(false);
    },
    [setValue]
  );

  const bankLabel = possibleBanks?.find(
    (bank) => bank.name === selectedBank
  )?.name;

  return (
    <>
      <div className="flex flex-col gap-4">
        <ControlledInput
          control={control}
          name="accountNumber"
          label="Account number"
        />

        {isGuessingBanks ? (
          <Spinner />
        ) : (possibleBanks && possibleBanks.length > 0) || selectedBank ? (
          <button
            type="button"
            className="h-12 flex items-center justify-between rounded-md border p-3"
            onClick={() => setBankModalOpen(true)}
            disabled={!isAcctNoFieldDirty}
          >
            <span className="flex items-center gap-2">
              <span
                className={bankLabel ? 'text-neutral-900' : 'text-neutral-400'}
              >
                {bankLabel ?? 'Bank'}
              </span>
            </span>
            <IoChevronDown size={24} color={colors.grey[70]} />
          </button>
        ) : null}

        {isVerifyingNuban ? <Spinner /> : <P>{watch('accountName')}</P>}
      </div>

      <Modal isOpen={bankModalOpen} onClose={() => setBankModalOpen(false)}>
        <div className="px-4 pb-4">
          <SearchInput
            placeholder="Find a bank"
            value={searchQuery}
            onChangeText={(e) => setSearchQuery(e)}
          />

          {filteredBanks?.map((bank) => (
            <button
              key={bank.name}
              className="flex items-center justify-between w-full border-b py-4"
              onClick={() =>
                selectBank({
                  label: bank.name,
                  value: bank.name,
                  code: bank.bank_code,
                })
              }
            >
              <span>{bank.name}</span>
              {selectedBank === bank.name && (
                <IoCheckmark size={20} color={colors.brand[60]} />
              )}
            </button>
          ))}
        </div>
      </Modal>
    </>
  );
};
