'use client';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import {
  Spinner,
  AtIcon,
  colors,
  ControlledInput,
  MessageFilledIcon,
  UserFilledIcon,
  SmBoldLabel,
} from '@/components/ui';
import {
  type EditAccountFormType,
  useAccountSetup,
  useLoggedInUser,
} from '@/lib';
import { LoadingScreen } from '@/components/loaders';

export const EditUserDetailsForm = () => {
  const { usernameAvailable } = useAccountSetup();
  const {
    control,
    setValue,
    formState: { isValidating },
  } = useFormContext<EditAccountFormType>();

  const { isLoading } = useLoggedInUser();

  const handleUsernameChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value.trim().toLowerCase();
    setValue('username', value, { shouldValidate: true });
    // const valid = await usernameRefinement(value);
  };

  if (isLoading) return <LoadingScreen />;

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>First Name</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            name="firstName"
            readOnly
            icon={<UserFilledIcon color={colors.brand['60']} />}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>Last Name</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            readOnly
            name="lastName"
            icon={<UserFilledIcon color={colors.brand['60']} />}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>Email</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            name="email"
            readOnly
            icon={<MessageFilledIcon color={colors.brand['60']} />}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>Username</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <div className="space-y-2 items-start">
            <ControlledInput
              control={control}
              name="username"
              onChange={handleUsernameChange}
              icon={<AtIcon color={colors.brand['60']} />}
            />
            {isValidating && <Spinner size="small" />}
            {usernameAvailable ? (
              <p className="text-green-600">✅ Username available</p>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};
