import React from 'react';
import { useFormContext } from 'react-hook-form';
import { type FormType as ForgotPasswordFormType } from '@/lib';
import {
  colors,
  ControlledInput,
  MessageFilledIcon,
  MessageIcon,
  SmBoldLabel,
} from '@/components/ui';

export const ForgotPasswordForm = () => {
  const { control, watch } = useFormContext<ForgotPasswordFormType>();
  const emailValue = watch('email');

  const isFilled = emailValue?.trim().length > 0;

  return (
    <div className="flex flex-row gap-x-8">
      <SmBoldLabel>Email</SmBoldLabel>
      <ControlledInput
        control={control}
        name="email"
        icon={
          isFilled ? (
            <MessageFilledIcon color={colors.brand['60']} />
          ) : (
            <MessageIcon color={colors.brand['60']} />
          )
        }
      />
    </div>
  );
};
