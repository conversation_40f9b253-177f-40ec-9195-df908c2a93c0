'use client';
import { useFormContext } from 'react-hook-form';
import { ControlledInput, SmBoldLabel } from '@/components/ui';
import { type EditAccountFormType } from '@/lib';

import { FaInstagram, FaFacebook, FaTiktok, FaYoutube } from 'react-icons/fa';
import { SiX, SiSnapchat } from 'react-icons/si';

export const SocialMediaForm = () => {
  const { control } = useFormContext<EditAccountFormType>();

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>Instagram</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            name="socials.instagram"
            icon={<FaInstagram size={24} color="#bc2a8d" />}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>X</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            name="socials.x"
            icon={<SiX size={24} color="#14171a" />}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>Snapchat</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            name="socials.snapchat"
            icon={<SiSnapchat size={24} color="#fffc00" />}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>Facebook</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            name="socials.facebook"
            icon={<FaFacebook size={24} color="#1877F2" />}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>TikTok</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            name="socials.tiktok"
            icon={<FaTiktok size={24} color="#000000" />}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>YouTube</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput
            control={control}
            name="socials.youtube"
            icon={<FaYoutube size={24} color="#FF0000" />}
          />
        </div>
      </div>
    </div>
  );
};
