import { useFormContext } from 'react-hook-form';
import { ControlledInput, SmBoldLabel } from '@/components/ui';

import { UpdatePasswordFormType } from '@/lib';

export const UpdatePasswordForm = () => {
  const { control } = useFormContext<UpdatePasswordFormType>();

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>Password</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput control={control} name="password" />
        </div>
      </div>
      <hr className="text-fg-muted-light dark:text-fg-muted-dark" />
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>New Password</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput control={control} name="newPassword" />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 md:gap-x-4 gap-y-2 items-start md:items-center">
        <div className="md:col-span-1">
          <SmBoldLabel>Confirm password</SmBoldLabel>
        </div>
        <div className="md:col-span-3">
          <ControlledInput control={control} name="confirmPassword" />
        </div>
      </div>
      <hr className="text-fg-muted-light dark:text-fg-muted-dark" />
    </div>
  );
};
