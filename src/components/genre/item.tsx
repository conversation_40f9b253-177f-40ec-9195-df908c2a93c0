import React from 'react';
import { Check } from 'lucide-react';
import { Tiny } from '@/components/ui/typography';
import { NextImage } from '@/components/ui/NextImage';
import { Button } from '@/components/ui/button';

interface GenreProps {
  genre: string;
  imageSource: string;
  isSelected: boolean;
  onToggle: () => void;
}

export const GenreItem = ({
  genre,
  imageSource,
  isSelected,
  onToggle,
}: GenreProps) => {
  return (
    <Button
      className="flex h-auto w-[74px] bg-transparent flex-col items-center gap-2 transition-opacity hover:opacity-70 focus:outline-hidden rounded-lg"
      onClick={onToggle}
      variant="ghost"
      type="button"
    >
      <div className="relative flex h-[74px] w-[74px] items-center justify-center overflow-hidden rounded-full">
        {isSelected && (
          <div className="absolute z-50 flex h-8 w-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90">
            <Check className="h-4 w-4 text-accent-bold-light dark:text-accent-bold-dark" />
          </div>
        )}
        {isSelected && <div className="absolute inset-0 z-40 bg-brand-50/49" />}
        <NextImage
          src={imageSource}
          alt={genre}
          fill
          className="h-full w-full rounded-full object-cover"
        />
      </div>
      <Tiny themed>{genre}</Tiny>
    </Button>
  );
};
