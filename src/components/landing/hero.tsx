import { Image } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { D1, P, SmRegularLabel } from '@/components/ui/typography';
import { ArrowUpRight } from 'lucide-react';

export const HeroSection = () => {
  return (
    <section className="relative min-h-[calc(100vh-144px)] lg:h-screen h-full w-full lg:max-w-[1600px] mx-auto overflow-hidden flex flex-col lg:flex-row gap-2">
      <div className="flex-1 min-h-[calc(50vh-72px)] lg:min-h-[calc(100vh-144px)] basis-0 bg-[#121212] rounded-b-[42px] lg:rounded-t-[42px] lg:pt-[208px]">
        <div className="max-w-xl pl-4 lg:pl-20 flex flex-col items-start gap-9">
          <div className="flex flex-col gap-4 md:gap-6 lg:gap-8">
            <D1>Here, we make our choices</D1>
            <SmRegularLabel>
              Discover events, connect with artists, and control the vibe – all
              in one app.
            </SmRegularLabel>
            <P asChild>
              <Button
                variant="outline"
                size="lg"
                className="!w-auto flex flex-row self-start gap-2 md:gap-3 border px-4 py-2"
              >
                <SmRegularLabel>Download The App</SmRegularLabel>
                <div className="size-9 rounded-full flex items-center justify-center bg-white">
                  <ArrowUpRight className="text-black self-center hover:translate-x-0.5 hover:-translate-y-0.5 transition-transform duration-300 size-5" />
                </div>
              </Button>
            </P>
          </div>

          <div className="flex items-center">
            <Image
              src={'/images/hero-users.png'}
              alt=""
              width={120}
              height={48}
            />
            <P className="text-xs md:text-sm lg:text-base">
              20k+ <br /> Popla users worldwide
            </P>
          </div>
        </div>
      </div>
      <div className="flex-1 min-h-[calc(50vh+72px)] lg:min-h-screen relative rounded-[42px] bg-[linear-gradient(135deg,#7257FF_0%,#121212_100%)] overflow-hidden">
        <Image
          src={'/images/popla-bg-logo.png'}
          alt="Popla background logo"
          fill
          className="object-contain"
          priority
        />

        <div className="absolute bottom-0 right-0 w-full h-full flex items-end justify-end">
          <div className="relative w-full max-w-[450px] sm:max-w-[500px] lg:max-w-[650px] xl:max-w-[720px] h-auto aspect-650/700">
            <Image
              src={'/images/held-mobile-home.png'}
              alt=""
              fill
              className="object-contain object-bottom"
              priority
            />
          </div>
        </div>
      </div>

      {/* Ticket Mockup */}
      <div className="absolute z-10 left-[20%] sm:left-[35%] md:left-[42%] lg:left-[46%] xl:left-[48%] bottom-[15%] sm:bottom-[18%] md:bottom-[20%] lg:bottom-[140px] w-[80px] sm:w-[100px] md:w-[140px] lg:w-[364px]">
        <Image
          src={'/images/ticket.png'}
          alt="Ticket Mockup"
          width={364}
          height={104}
          className="w-full h-auto"
          priority
        />
      </div>

      {/* Event Mockup */}
      <div className="absolute z-10 left-[12%] sm:left-[28%] md:left-[35%] lg:left-[38%] xl:left-[40.6%] bottom-[5%] sm:bottom-[6%] md:bottom-[8%] lg:bottom-[20px] w-[100px] sm:w-[120px] md:w-[160px] lg:w-[380px] rotate-3">
        <Image
          src={'/images/public-event.png'}
          alt="Public Event Mockup"
          width={380}
          height={81}
          className="w-full h-auto"
          priority
        />
      </div>
    </section>
  );
};
