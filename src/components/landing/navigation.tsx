'use client';
import Link from 'next/link';
import { Menu } from 'lucide-react';
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '@/components/ui/sheet';
import { P } from '@/components/ui/typography';
import { cn } from '@/lib/utils';
import { StoreDownloadLink } from '@/components/links/store';
import { AuthButton } from '../ui/auth-button';
import { Image } from '@/components/ui';

export const Navigation = ({ className }: { className?: string }) => {
  return (
    <header className={cn('absolute w-full rounded-t-[42px]', className)}>
      <nav className="relative max-w-[1600px] mx-auto z-10 flex items-center justify-between px-4 py-12 lg:px-20">
        <Link href="/" className="size-full">
          <Image
            src={'/svgs/logo.svg'}
            alt="Popla Logo"
            width={114}
            height={48}
            className="dark:invert"
          />
        </Link>

        <div className="hidden md:flex items-center space-x-[60px]">
          <P asChild>
            <Link href="/features">Features</Link>
          </P>
          <P asChild>
            <Link href="/support">Support</Link>
          </P>
          <StoreDownloadLink />
          <P asChild>
            <Link href="/events">Events</Link>
          </P>
          <AuthButton />
        </div>

        <Sheet>
          <SheetTrigger asChild>
            <button className="md:hidden text-white cursor-pointer">
              <Menu size={22} />
              <span className="sr-only">Toggle menu</span>
            </button>
          </SheetTrigger>
          <SheetContent
            side="right"
            className={cn(
              'fixed top-0 right-0 h-full w-3/5 md:w-1/2 max-w-sm bg-bg-canvas-light dark:bg-bg-canvas-dark z-50 shadow-lg'
            )}
          >
            <ul className="flex flex-col gap-2 p-4">
              <Link
                href="/features"
                className="flex items-center gap-4 text-lg p-2 rounded-lg transition-colors hover:bg-bg-subtle-light dark:hover:bg-bg-subtle-dark"
              >
                Features
              </Link>
              <Link
                href="/support"
                className="flex items-center gap-4 text-lg p-2 rounded-lg transition-colors hover:bg-bg-subtle-light dark:hover:bg-bg-subtle-dark"
              >
                Support
              </Link>
              <Link
                href="/download"
                className="flex items-center gap-4 text-lg p-2 rounded-lg transition-colors hover:bg-bg-subtle-light dark:hover:bg-bg-subtle-dark"
              >
                Download
              </Link>
              <Link
                href="/events"
                className="flex items-center gap-4 text-lg p-2 rounded-lg transition-colors hover:bg-bg-subtle-light dark:hover:bg-bg-subtle-dark"
              >
                Events
              </Link>
              <div className="absolute bottom-4 left-4 right-4 flex flex-row w-full">
                <AuthButton />
              </div>
            </ul>
          </SheetContent>
        </Sheet>
      </nav>
    </header>
  );
};
