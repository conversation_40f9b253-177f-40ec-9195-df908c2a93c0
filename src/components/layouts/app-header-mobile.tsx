'use client';

import { useState } from 'react';
import { usePathname } from 'next/navigation';
import { H3, Image, SearchInput } from '@/components/ui';
import { Search, Menu, X } from 'lucide-react';
import { useSearch } from '@/lib';
import Link from 'next/link';
import { MobileMenu } from './mobile-menu';

export const AppHeaderMobile = () => {
  const [showSearch, setShowSearch] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const pathname = usePathname();
  const isEventsPage = pathname.startsWith('/events');
  const isCreateEventPage = pathname.startsWith('/events/create');

  const segments = pathname.split('/').filter(Boolean);

  let rawTitle: string;
  if (segments[0] === 'users' && segments[1]) {
    rawTitle = segments[1];
  } else {
    rawTitle = segments[0] || 'home';
  }

  const title = rawTitle
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  const { query, setQuery } = useSearch();

  return (
    <>
      <header className="sticky top-0 z-10 h-16 bg-bg-canvas-light/90 dark:bg-bg-canvas-dark/90">
        <div className="px-4 grid grid-cols-[auto_1fr_auto] items-center h-16">
          <div className="flex items-start justify-start w-20 flex-shrink-0">
            <Link href="/">
              <Image
                src="/svgs/logo.svg"
                alt="logo"
                width={114}
                height={48}
                className="dark:invert"
              />
            </Link>
          </div>

          <div className="flex justify-center">
            {showSearch ? (
              <SearchInput
                value={query}
                onChangeText={setQuery}
                placeholder="Search events"
                className="
                  w-full
                  rounded-xl
                "
                autoFocus
                onBlur={() => setShowSearch(false)}
              />
            ) : (
              <H3 className="text-center">{title}</H3>
            )}
          </div>

          <div className="flex items-center justify-end w-20 gap-4 flex-shrink-0">
            {isEventsPage && !isCreateEventPage && (
              <button onClick={() => setShowSearch((prev) => !prev)}>
                <Search size={20} />
              </button>
            )}
            <button onClick={() => setShowMenu((prev) => !prev)}>
              {showMenu ? <X size={22} /> : <Menu size={22} />}
            </button>
          </div>
        </div>
      </header>

      {showMenu && (
        <div
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
          onClick={() => setShowMenu(false)}
        />
      )}
      <MobileMenu showMenu={showMenu} setShowMenu={setShowMenu} />
    </>
  );
};
