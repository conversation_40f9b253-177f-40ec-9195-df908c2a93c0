'use client';

import { usePathname, useRouter } from 'next/navigation';
import {
  Image,
  LgBoldLabel,
  MdRegularLabel,
  SmBoldLabel,
} from '@/components/ui';
import { ThemeToggleSwitch } from '@/components/ui/theme-toggle';
import { NAV_SECTIONS } from '@/lib/constants/generic';
import { Button, SearchInput } from '@/components/ui';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown';
import { User, Wallet, LogOut, LogIn, Plus } from 'lucide-react';
import { cn, useAuth, useLoggedInUser, useSearch } from '@/lib';

export const AppHeader = () => {
  const { logout, isAuthenticated } = useAuth();
  const { data: user } = useLoggedInUser();
  const router = useRouter();
  const pathname = usePathname();

  const isCreator = user?.role === 'CREATOR';

  const allNavItems = NAV_SECTIONS.flatMap((section) => section.items);
  const currentNavItem =
    allNavItems.find((item) => pathname.startsWith(item.href)) ?? null;

  const isEventsPage = pathname.startsWith('/events');
  const isMyEventsPage = pathname.startsWith('/my-events');
  const isCreateEventPage = pathname.startsWith('/events/create');
  const isProfilePage = pathname.startsWith('/profile');
  const isReallyEventsPage = pathname === '/events';

  const segments = pathname.split('/').filter(Boolean);

  let rawTitle: string;
  if (segments[0] === 'users' && segments[1]) {
    rawTitle = segments[1];
  } else {
    rawTitle = segments[0] || 'home';
  }

  const title = rawTitle
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  const { query, setQuery } = useSearch();

  return (
    <header
      className={cn(
        'sticky top-0 z-10 h-20 py-4 mx-4 shrink-0 flex items-center justify-between transition-colors',
        isProfilePage
          ? 'bg-transparent'
          : 'bg-bg-canvas-light/90 dark:bg-bg-canvas-dark/90'
      )}
    >
      {!isProfilePage ? (
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center rounded-full size-12 bg-bg-subtle-light dark:bg-bg-subtle-dark [&>svg]:fill-accent-moderate">
            {currentNavItem?.Icon && (
              <Image
                src={currentNavItem.Icon}
                width={24}
                height={24}
                alt=""
                className="dark:invert"
              />
            )}
            {isMyEventsPage && (
              <Image
                src={'/svgs/tabs/event.svg'}
                width={24}
                height={24}
                alt=""
                className="dark:invert"
              />
            )}
          </div>
          <SmBoldLabel weight="bold" themed={!isProfilePage}>
            {title}
          </SmBoldLabel>
        </div>
      ) : (
        <div className="flex items-center gap-2">
          <LgBoldLabel weight="bold" themed={!isProfilePage}>
            {title}
          </LgBoldLabel>
        </div>
      )}

      {isReallyEventsPage && (
        <SearchInput
          value={query}
          onChangeText={setQuery}
          placeholder="Search by event or venue"
          className="
    md:flex
    md:max-w-[361px]
    w-full
    rounded-xl
  "
        />
      )}

      <div className="items-center gap-4 hidden md:flex">
        <ThemeToggleSwitch />
        {isEventsPage && isCreator && (
          <div className="flex flex-row gap-x-4">
            {!isCreateEventPage && (
              <Button
                label="Create"
                icon={<Plus className="size-6" />}
                className="w-[127px] h-8"
                onPress={() => router.push('/events/create')}
              />
            )}
            {!isMyEventsPage && (
              <Button
                label="My Events"
                className="w-[127px] h-8"
                onPress={() => router.push('/my-events')}
              />
            )}
          </div>
        )}

        {/* {isAuthenticated && (
          <div className="flex items-center justify-center size-8 rounded-full border border-accent-bold-light dark:border-accent-bold-dark">
            <Image
              src={'/svgs/bell.svg'}
              width={24}
              height={24}
              alt="Bell"
              className="dark:invert"
            />
          </div>
        )} */}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="cursor-pointer outline-0">
              <Image
                src={user?.profileImageUrl || '/images/avatar.png'}
                alt={user?.fullName || ''}
                height={32}
                width={32}
                className="rounded-full"
              />
            </button>
          </DropdownMenuTrigger>
          {isAuthenticated ? (
            <DropdownMenuContent
              align="end"
              className="w-[240px] p-4 flex flex-col gap-2 bg-bg-canvas-light dark:bg-bg-canvas-dark divide-y divide-border-subtle-light dark:divide-border-subtle-dark rounded-xl"
            >
              <DropdownMenuItem
                onClick={() => router.push('/profile')}
                className="p-4 flex flex-row items-center gap-2 cursor-pointer"
              >
                <div className="flex items-center justify-center size-5">
                  <User size={14} />
                </div>
                <MdRegularLabel>Profile</MdRegularLabel>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() => router.push('/wallet')}
                className="p-4 flex flex-row items-center gap-2 cursor-pointer"
              >
                <div className="flex items-center justify-center size-5">
                  <Wallet size={14} />
                </div>
                <MdRegularLabel>Wallet</MdRegularLabel>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() => logout()}
                className="p-4 flex flex-row items-center gap-2 text-fg-error-light dark:text-fg-error-dark cursor-pointer"
              >
                <div className="flex items-center justify-center size-5">
                  <LogOut size={14} />
                </div>
                <MdRegularLabel>Logout</MdRegularLabel>
              </DropdownMenuItem>
            </DropdownMenuContent>
          ) : (
            <DropdownMenuContent
              align="center"
              className="w-[240px] p-4 bg-bg-canvas-light dark:bg-bg-canvas-dark rounded-xl"
            >
              <DropdownMenuItem
                onClick={() => router.push('/login')}
                className="p-4 flex flex-row items-center gap-2 cursor-pointer"
              >
                <div className="flex items-center justify-center size-5">
                  <LogIn size={14} />
                </div>
                <MdRegularLabel>Login</MdRegularLabel>
              </DropdownMenuItem>
            </DropdownMenuContent>
          )}
        </DropdownMenu>
      </div>
    </header>
  );
};
