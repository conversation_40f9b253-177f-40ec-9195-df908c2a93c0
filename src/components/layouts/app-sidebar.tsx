'use client';
import * as React from 'react';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import Link from 'next/link';
import { SmBoldLabel } from '@/components/ui/typography';
import { UnstyledLink } from '@/components/links/unstyled';
import { usePathname } from 'next/navigation';
import { NAV_SECTIONS } from '@/lib/constants/generic';
import { Image } from '@/components/ui';

const isPathActive = (itemHref: string, currentPath: string): boolean => {
  if (itemHref === '/home' && currentPath === '/') return true;
  if (itemHref === '/home') return currentPath === '/home';
  return currentPath.startsWith(itemHref);
};

export const AppSidebar = ({
  ...props
}: React.ComponentProps<typeof Sidebar>) => {
  const pathname = usePathname();

  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <Link
          href="/"
          className="h-full max-h-12 w-full max-w-28 text-accent-moderate dark:text-white"
        >
          <Image
            src={'/svgs/logo.svg'}
            alt="Popla Logo"
            width={114}
            height={48}
            className="dark:invert"
          />
        </Link>
      </SidebarHeader>
      <SidebarContent>
        {NAV_SECTIONS.map((section, idx) => {
          if (section.title == 'Profile' || section.title == 'Wallet')
            return null;

          return (
            <SidebarGroup key={`section-${idx}`}>
              {section.title && (
                <SidebarGroupLabel asChild>
                  <SmBoldLabel themed weight="bold">
                    {section.title}
                  </SmBoldLabel>
                </SidebarGroupLabel>
              )}
              <SidebarGroupContent>
                <SidebarMenu>
                  {section.items
                    .filter(
                      ({ title }) => title !== 'Profile' && title !== 'Wallet'
                    )
                    .map(({ title, href, Icon }) => {
                      const isActive = isPathActive(href, pathname);
                      return (
                        <SidebarMenuItem key={title}>
                          <SidebarMenuButton asChild isActive={isActive}>
                            <UnstyledLink href={href}>
                              <Image
                                src={Icon}
                                width={24}
                                height={24}
                                alt=""
                                className="dark:invert"
                              />
                              {title}
                            </UnstyledLink>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      );
                    })}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          );
        })}
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
};
