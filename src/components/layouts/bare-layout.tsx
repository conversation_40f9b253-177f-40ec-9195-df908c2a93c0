'use client';

import React from 'react';
import { cn } from '@/lib';
import { H1, P } from '@/components/ui';

type BareLayoutProps = {
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  contentClassName?: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
};

export const BareLayout = ({
  title,
  subTitle,
  children,
  footer,
  contentClassName,
}: BareLayoutProps) => {
  return (
    <div className="flex flex-col flex-1 bg-bg-canvas-light dark:bg-grey-100">
      <div className="mx-4 justify-start gap-1 pb-4">
        {title && <H1>{title}</H1>}
        {subTitle && (
          <P className="text-grey-60 dark:text-grey-50">{subTitle}</P>
        )}
      </div>

      <div
        className={cn(
          'mx-auto flex w-full max-w-md flex-1 flex-col gap-4',
          contentClassName
        )}
      >
        {children}
        {footer && <div className="mt-auto">{footer}</div>}
      </div>
    </div>
  );
};
