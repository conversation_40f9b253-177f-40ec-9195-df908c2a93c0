'use client';

import React from 'react';
import { cn } from '@/lib';
import { Breadcrumbs, H1, P } from '@/components/ui';

type CreateEventLayoutProps = {
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  fullWidth?: boolean;
};

export const CreateEventLayout: React.FC<CreateEventLayoutProps> = ({
  title,
  subTitle,
  children,
  footer,
  fullWidth = false,
}) => {
  return (
    <div className="flex flex-col flex-1 px-4">
      <main className="mt-4 flex flex-col gap-8 pb-4 flex-1 min-h-[calc(100dvh-120px)]">
        <Breadcrumbs />
        <div>
          <div
            className={cn(
              fullWidth
                ? 'justify-start items-start'
                : 'flex flex-1 flex-col gap-2 max-w-md w-md mx-auto'
            )}
          >
            <div className="w-fit">
              {title && <H1>{title}</H1>}
              {subTitle && (
                <P className="text-gray-600 dark:text-gray-400">{subTitle}</P>
              )}
            </div>
          </div>
        </div>

        <section
          className={cn(
            fullWidth
              ? 'justify-start items-start'
              : 'flex w-full max-w-md flex-1 flex-col gap-4 mx-auto'
          )}
        >
          {children}
          <div className="mt-auto lg:mt-0 w-full max-w-md mx-auto">
            {footer}
          </div>
        </section>
        {/* {footer && (
          <div className="mt-auto lg:mt-0 w-full max-w-md mx-auto">
            {footer}
          </div>
        )} */}
      </main>
    </div>
  );
};
