import Link from 'next/link';
import { Image } from '@/components/ui';
import { cn } from '@/lib';

interface MobileMenuItemProps {
  href?: string;
  onClick?: () => void;
  icon?: React.ReactNode | string;
  title: string;
}

export const MobileMenuItem = ({
  href,
  onClick,
  icon,
  title,
}: MobileMenuItemProps) => {
  const commonClasses =
    'flex items-center gap-4 text-lg p-2 rounded-lg transition-colors hover:bg-bg-subtle-light dark:hover:bg-bg-subtle-dark';

  const renderIcon = () => {
    if (typeof icon === 'string') {
      return (
        <Image
          src={icon as string}
          alt={title}
          width={24}
          height={24}
          className="shrink-0 dark:invert"
        />
      );
    }
    return icon;
  };

  if (href) {
    return (
      <li>
        <Link href={href} className={cn(commonClasses)} onClick={onClick}>
          {renderIcon()}
          {title}
        </Link>
      </li>
    );
  }

  return (
    <li>
      <button
        onClick={onClick}
        className={cn(commonClasses, 'w-full text-left')}
      >
        {renderIcon()}
        {title}
      </button>
    </li>
  );
};
