'use client';

import { Home, User, Wallet, X, LogOut } from 'lucide-react';
import { NAV_SECTIONS } from '@/lib/constants/generic';
import { ThemeToggleSwitch } from '@/components/ui/theme-toggle';
import { cn, useAuth, useLoggedInUser } from '@/lib';
import { MobileMenuItem } from './mobile-menu-item';
import { Button } from '@/components/ui';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface MobileMenuProps {
  showMenu: boolean;
  setShowMenu: (show: boolean) => void;
}

export const MobileMenu = ({ showMenu, setShowMenu }: MobileMenuProps) => {
  const { logout, isAuthenticated } = useAuth();
  const { data: user } = useLoggedInUser();
  const isCreator = user?.role === 'CREATOR';
  const pathname = usePathname();

  const navItems = [
    { href: '/', icon: <Home size={20} />, title: 'Home' },
    ...NAV_SECTIONS[0].items
      .filter(({ title }) => title !== 'Profile' && title !== 'Wallet')
      .map((item) => ({ ...item, icon: item.Icon })),
  ];

  const isMyEventsPage = pathname.startsWith('/events/my-events');
  const isCreateEventPage = pathname.startsWith('/events/create');

  return (
    <nav
      className={cn(
        'fixed top-0 right-0 h-full w-4/5 md:w-1/2 max-w-sm transition-transform duration-300 ease-in-out transform',
        'bg-bg-canvas-light dark:bg-bg-canvas-dark z-50 shadow-lg',
        showMenu ? 'translate-x-0' : 'translate-x-full'
      )}
    >
      <div className="flex flex-row justify-between p-4">
        <div className="p-2">
          <ThemeToggleSwitch />
        </div>
        <button onClick={() => setShowMenu(false)}>
          <X size={24} />
        </button>
      </div>

      <ul className="flex flex-col gap-2 p-4">
        {navItems.map((item) => (
          <MobileMenuItem
            onClick={() => setShowMenu(false)}
            key={item.href}
            href={item.href}
            icon={item.icon}
            title={item.title}
          />
        ))}

        {isAuthenticated ? (
          <>
            <MobileMenuItem
              onClick={() => setShowMenu(false)}
              href="/profile"
              icon={<User size={20} />}
              title="Profile"
            />
            <MobileMenuItem
              onClick={() => setShowMenu(false)}
              href="/wallet"
              icon={<Wallet size={20} />}
              title="Wallet"
            />
            <MobileMenuItem
              onClick={() => {
                setShowMenu(false);
                logout();
              }}
              icon={<LogOut color="red" size={20} />}
              title="Logout"
            />
          </>
        ) : (
          <MobileMenuItem
            href="/login"
            icon={<User size={20} />}
            title="Login"
            onClick={() => setShowMenu(false)}
          />
        )}
      </ul>
      {isCreator && (
        <div className="absolute bottom-4 left-4 right-4 flex flex-row gap-4">
          {!isMyEventsPage && (
            <Link
              href="/my-events"
              className="flex-1"
              onClick={() => setShowMenu(false)}
            >
              <Button label="My Events" variant="outline" />
            </Link>
          )}
          {!isCreateEventPage && (
            <Link
              href="/events/create"
              className="flex-1"
              onClick={() => setShowMenu(false)}
            >
              <Button label="Create Event" />
            </Link>
          )}
        </div>
      )}
    </nav>
  );
};
