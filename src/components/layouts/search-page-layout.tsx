import React from 'react';
import { Back } from '../ui/back';
import { SearchInput } from '../ui/search-input';

interface SearchPageLayoutProps {
  placeholder?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  searchValue?: string;
  onSearchChange?: (text: string) => void;
  onBackPress?: () => void;
}

export const SearchPageLayout = ({
  placeholder,
  children,
  footer,
  searchValue = '',
  onSearchChange = () => {},
  onBackPress,
}: SearchPageLayoutProps) => {
  return (
    <div className="flex flex-col">
      <div className="flex items-center gap-2 px-4 py-6 border-b border-neutral-200 dark:border-neutral-800">
        <Back onBackPress={onBackPress} />
        <div className="flex-1">
          <SearchInput
            value={searchValue}
            onChangeText={(text) => onSearchChange(text)}
            placeholder={placeholder}
            showClearButton={!!searchValue}
          />
        </div>
      </div>

      <div className="flex-1 p-4 space-y-4 overflow-y-auto">{children}</div>

      {footer && (
        <div className="border-t border-neutral-200 dark:border-neutral-800 p-4 mt-auto">
          {footer}
        </div>
      )}
    </div>
  );
};
