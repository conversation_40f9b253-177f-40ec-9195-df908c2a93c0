'use client';

import React from 'react';
import Link from 'next/link';
import { usePlatform } from '@/lib/hooks/usePlatform';
import { APP_STORE_LINK, PLAY_STORE_LINK } from '@/lib/constants';
import { P } from '@/components/ui/typography';

export const StoreDownloadLink = () => {
  const { platform, runtime, mounted } = usePlatform();

  if (!mounted || runtime === 'server') return null;

  const isAppStorePlatform = platform === 'ios' || platform === 'macos';
  const storeLink = isAppStorePlatform ? APP_STORE_LINK : PLAY_STORE_LINK;

  return (
    <P asChild>
      <Link href={storeLink} target="_blank" rel="noopener noreferrer">
        Download
      </Link>
    </P>
  );
};
