import * as React from 'react';

import { cn } from '@/lib/utils';

import { UnstyledLink, UnstyledLinkProps } from '@/components/links/unstyled';

export const UnderlineLink = React.forwardRef<
  HTMLAnchorElement,
  UnstyledLinkProps
>(({ children, className, ...rest }, ref) => {
  return (
    <UnstyledLink
      ref={ref}
      {...rest}
      className={cn(
        'animated-underline custom-link inline-flex items-center font-medium',
        'focus-visible:ring-primary-500 focus:outline-hidden focus-visible:rounded focus-visible:ring-3 focus-visible:ring-offset-2',
        'border-dark border-b border-dotted hover:border-black/0',
        className
      )}
    >
      {children}
    </UnstyledLink>
  );
});

UnderlineLink.displayName = 'UnderlineLink';
