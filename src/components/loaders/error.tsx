'use client';

import React, { useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { RefreshCcw } from 'lucide-react';
interface ErrorScreenProps {
  title: string;
  message: string;
}

export const ErrorScreen: React.FC<ErrorScreenProps> = ({ title, message }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [spinning, setSpinning] = useState(false);

  const handleRefresh = () => {
    setSpinning(true);
    setTimeout(() => setSpinning(false), 800);
    router.replace(pathname ?? '/');
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-bg-canvas-light dark:bg-bg-canvas-dark p-4 gap-4 flex-col">
      <h3 className="text-center text-xl font-semibold">{title}</h3>
      <p className="text-center text-fg-muted-light dark:text-fg-muted-dark">
        {message}
      </p>

      <button onClick={handleRefresh} className="focus:outline-none">
        <span
          className={`inline-block transition-transform duration-700 ${
            spinning ? 'animate-spin' : ''
          }`}
        >
          <RefreshCcw size={64} color="#666" />
        </span>
      </button>
    </div>
  );
};
