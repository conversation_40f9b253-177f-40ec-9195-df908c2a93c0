import { cn } from '@/lib';

import { Skeleton } from '../ui';

interface Props {
  className?: string;
}
export const EventsLoading: React.FC<Props> = ({ className }) => (
  <div className={cn('flex-1 gap-4 py-4', className)}>
    <div className="grid grid-cols-2 gap-4">
      {Array.from({ length: 10 }).map((_, index) => (
        <Skeleton
          key={index}
          className={cn(
            'h-[300px] rounded-lg w-full',
            index % 2 === 0 ? 'mr-2' : 'ml-2'
          )}
        />
      ))}
    </div>
  </div>
);
