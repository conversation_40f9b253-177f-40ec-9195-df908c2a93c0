import React from 'react';

import { colors } from '../ui';
import { ActivityIndicator } from '@/components/loaders/activity-indicator';
interface LoadingScreenProps {
  message?: string;
}

export const LoadingScreen = ({ message }: LoadingScreenProps) => {
  return (
    <div className="flex min-h-[400px] w-full flex-col items-center justify-center space-y-4">
      <ActivityIndicator color={colors.brand[60]} />
      {message && (
        <p className="text-fg-muted-light dark:text-fg-muted-dark text-md">
          {message}
        </p>
      )}
    </div>
  );
};

export * from './events';
export * from './my-events';
export * from './trending-events';
export * from './users';
export * from './user-row';
export * from './error';
export * from './home';
export * from './activity-indicator';
