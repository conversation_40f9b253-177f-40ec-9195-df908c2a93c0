import { cn } from '@/lib';

import { Skeleton } from '../ui';

interface Props {
  className?: string;
  itemClassName?: string;
}
export const MyEventsLoading: React.FC<Props> = ({
  className,
  itemClassName,
}) => (
  <div className={cn('flex-1 gap-4', className)}>
    <div className="flex flex-col space-y-4">
      {Array.from({ length: 10 }, (_, index) => (
        <Skeleton
          key={index}
          className={cn('mx-2 h-[152px] flex-1 rounded-lg', itemClassName)}
        />
      ))}
    </div>
  </div>
);
