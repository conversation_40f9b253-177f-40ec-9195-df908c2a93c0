import { cn } from '@/lib';

import { Skeleton } from '../ui';

interface Props {
  className?: string;
}
export const TrendingEventsLoading: React.FC<Props> = ({ className }) => (
  <div className={cn('flex-1 gap-4 p-4', className)}>
    <div className="flex flex-col gap-4">
      {Array.from({ length: 2 }).map((_, index) => (
        <Skeleton key={index} className="h-[278px] flex-1 rounded-lg" />
      ))}
    </div>
  </div>
);
