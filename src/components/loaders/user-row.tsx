import { cn } from '@/lib';

import { Skeleton } from '../ui';

export const UserRowLoading: React.FC = () => (
  <div className="flex-1">
    <div className="flex flex-row gap-4 overflow-x-auto">
      {Array.from({ length: 6 }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'flex flex-col gap-y-2 items-center',
            index === 0 && 'ml-4'
          )}
        >
          <Skeleton className="size-16 rounded-full" />
          <Skeleton className="h-3 w-[74px]" />
        </div>
      ))}
    </div>
  </div>
);
