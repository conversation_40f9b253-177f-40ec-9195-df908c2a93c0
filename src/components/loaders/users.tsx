import { cn } from '@/lib';

import { Skeleton } from '../ui';

interface Props {
  className?: string;
  length?: number;
}
export const UsersLoading: React.FC<Props> = ({ className, length = 10 }) => (
  <div className={cn('flex-1 px-4', className)}>
    <div className="flex flex-col gap-2">
      {Array.from({ length }).map((_, index) => (
        <div key={index} className="h-16 flex flex-row items-center gap-x-4">
          <Skeleton className="size-10 rounded-full" />
          <div className="flex-1 flex flex-col gap-1">
            <Skeleton className="h-4 w-24 rounded-sm" />
            <Skeleton className="h-3 w-48 rounded-sm" />
          </div>
        </div>
      ))}
    </div>
  </div>
);
