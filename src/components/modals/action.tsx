import { FC } from 'react';

interface ActionModalProps {
  title: string;
  info: string;
  isOpen: boolean;
  actionText?: string;
  closeText?: string;
  onClose?: () => void;
  onAction?: () => void;
}

export const ActionModal: FC<ActionModalProps> = ({
  title,
  info,
  isOpen,
  actionText,
  closeText,
  onClose,
  onAction,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm mx-auto shadow-lg">
        <h2 className="text-lg font-semibold mb-2">{title}</h2>
        <p className="text-gray-600 mb-4">{info}</p>
        <div className="flex justify-between space-x-4">
          {onClose && (
            <button
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-full w-full"
              onClick={onClose}
            >
              {closeText || 'Close'}
            </button>
          )}
          {onAction && (
            <button
              className="bg-linear-to-l from-[#664FB0] to-[#A992F5] text-white font-bold py-2 px-4 rounded-full w-full"
              onClick={onAction}
            >
              {actionText || 'Complete'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
