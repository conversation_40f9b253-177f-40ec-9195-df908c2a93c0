'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib';
import { useDeleteUser } from '@/api/users';
import { Button, H2, P } from '../ui';

interface ConfirmDeleteAccountModalProps {
  visible: boolean;
  onClose: () => void;
}

export const ConfirmDeleteAccountModal: React.FC<
  ConfirmDeleteAccountModalProps
> = ({ visible, onClose }) => {
  const router = useRouter();
  const { user } = useAuth();
  const { mutate: deleteUser, isPending: deletingUser } = useDeleteUser();

  if (!user || !visible) return null;

  return (
    <div
      aria-modal="true"
      role="dialog"
      className="fixed inset-0 z-50 flex items-center justify-center"
      onClick={onClose}
    >
      {/* Backdrop with blur and dark overlay */}
      <div
        className="absolute inset-0 bg-black bg-opacity-70 backdrop-blur-xs"
        onClick={(e) => e.stopPropagation()}
      />

      <div
        className="relative z-10 w-full max-w-md rounded-lg bg-white p-8 dark:bg-gray-900 flex flex-col items-center gap-7"
        onClick={(e) => e.stopPropagation()}
      >
        <H2>Are you sure?</H2>
        <P className="text-gray-600 dark:text-gray-300">
          Your account will be permanently deleted.
        </P>

        <div className="flex w-full gap-4 px-5">
          <Button
            label="Cancel"
            className="flex-1 bg-brand-100 dark:bg-brand-900 text-brand-700 dark:text-brand-400"
            onClick={onClose}
          />
          <Button
            label="Delete"
            loading={deletingUser}
            className="flex-1 bg-red-600 text-white hover:bg-red-700"
            onPress={() => {
              deleteUser({ id: user.id });
              router.replace('/onboarding');
            }}
          />
        </div>
      </div>
    </div>
  );
};
