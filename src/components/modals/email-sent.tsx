import React from 'react';
import { Button, H2, P, Image } from '../ui';

export const EmailCheckModal: React.FC<{
  visible: boolean;
  onClose: () => void;
}> = ({ visible = true, onClose }) => {
  if (!visible) return null;

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="bg-bg-surface-light dark:bg-bg-surface-dark h-[357px] w-full flex items-center gap-6 rounded-lg p-6 pt-8">
        <div className="size-[120px] rounded-full">
          <Image src={'/images/email-message.png'} alt="Email icon" />
        </div>

        <div className="w-[279px] flex items-center gap-2">
          <H2>Check your inbox</H2>
          <P className="text-center text-grey-60 dark:text-grey-50">
            We sent you instructions to reset your email
          </P>
        </div>

        <Button label="Okay" className="w-full" onClick={onClose} />
      </div>
    </div>
  );
};
