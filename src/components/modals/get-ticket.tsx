'use client';
import React from 'react';

import { type ISingleEvent } from '@/api/events';
import { formatAmount, isPresaleActive, toAmountInMajor } from '@/lib';

import { Button, Counter, H2, H4, H6, Small, XsBoldLabel } from '../ui';

interface GetTicketModalProps {
  visible: boolean;
  onClose: () => void;
  handleTicketQuantityChange: (category: string, quantity: number) => void;
  hasSelectedTickets: boolean;
  selectedTickets: Record<string, number>;
  onProceed: () => void;
  event?: ISingleEvent;
  isLoading?: boolean;
}
export const GetTicketModal: React.FC<GetTicketModalProps> = ({
  visible = true,
  onClose,
  event,
  handleTicketQuantityChange,
  hasSelectedTickets,
  selectedTickets,
  onProceed,
  isLoading,
}) => {
  const ticketCategories = event?.ticketCategories || {};

  if (!visible) return null;

  return (
    <React.Fragment>
      <div className="w-full flex flex-col gap-2 bg-transparent px-4 pb-4">
        <H2>Tickets</H2>
        <div>
          {Object.entries(ticketCategories).map(
            ([categoryKey, ticketCategory], index) => {
              const presaleTicket = event?.presaleConfig?.find(
                (presale) => presale.ticketCategoryId === ticketCategory.id
              );

              const shouldUsePresale =
                presaleTicket && isPresaleActive(presaleTicket);

              const activeTicket = shouldUsePresale
                ? {
                    ...ticketCategory,
                    cost: presaleTicket.price,
                    quantity: presaleTicket.quantity,
                    purchaseLimit:
                      presaleTicket.purchaseLimit ||
                      ticketCategory.purchaseLimit,
                    description:
                      presaleTicket.description || ticketCategory.description,
                  }
                : ticketCategory;

              return (
                <div
                  key={index}
                  className="flex flex-row items-start gap-6 py-4"
                >
                  <div className="flex-1 gap-2">
                    <div className="flex flex-row items-center justify-between">
                      <H4>{categoryKey}</H4>
                      {shouldUsePresale && (
                        <div className="flex h-5 w-[58px] items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                          <XsBoldLabel className="text-green-60 dark:text-green-40">
                            Presale
                          </XsBoldLabel>
                        </div>
                      )}
                    </div>
                    <H6 className="text-fg-muted-light dark:text-fg-muted-dark">
                      {formatAmount(
                        Number(
                          toAmountInMajor(
                            activeTicket.convertedCost || activeTicket.cost
                          )
                        )
                      )}
                    </H6>
                    <Small className="text-fg-muted-light dark:text-fg-muted-dark">
                      {activeTicket.description}
                    </Small>
                  </div>
                  <Counter
                    initialValue={selectedTickets[categoryKey] || 0}
                    minimum={1}
                    maximum={
                      activeTicket.quantity > 10 ? 10 : activeTicket.quantity
                    }
                    onValueChange={(quantity) =>
                      handleTicketQuantityChange(categoryKey, quantity)
                    }
                    className="border border-border-subtle-light dark:border-border-subtle-dark"
                  />
                </div>
              );
            }
          )}
        </div>

        <Button
          label="Continue"
          disabled={!hasSelectedTickets || isLoading}
          loading={isLoading}
          onPress={onProceed}
        />
        <Button
          label="Cancel"
          variant="outline"
          disabled={isLoading}
          onPress={onClose}
        />
      </div>
    </React.Fragment>
  );
};
