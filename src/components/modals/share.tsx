'use client';

import React from 'react';
import clsx from 'clsx';
import { motion, AnimatePresence } from 'framer-motion';
import { MdLink, MdMailOutline } from 'react-icons/md';
import { IoLogoWhatsapp, IoLogoFacebook } from 'react-icons/io5';
import { FaInstagram } from 'react-icons/fa';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { BsX } from 'react-icons/bs';
import { semanticColors } from '@/components/ui';

const SHARE_OPTIONS = [
  {
    id: 'copy',
    name: 'Copy',
  },
  {
    id: 'message',
    name: 'Message',
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    bgColor: '#25D366',
  },
  {
    id: 'instagram',
    name: 'Instagram',
    bgColor: '#DA337A',
  },
  {
    id: 'twitter',
    name: 'X (Twitter)',
    bgColor: '#3D3D3D',
  },
  {
    id: 'facebook',
    name: 'Facebook',
    bgColor: '#1877F2',
  },
  {
    id: 'menu',
    name: 'More',
    size: '16',
    color: '#5A2D82',
    bgColor: '#EDE9FE',
  },
];

interface ShareModalProps {
  isOpen: boolean;
  onDismiss: () => void;
  content: string;
}

export const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onDismiss,
  content,
}) => {
  const handleShare = async (platform: string) => {
    onDismiss();

    try {
      switch (platform) {
        case 'copy':
          await navigator.clipboard.writeText(content);
          alert('Link copied!');
          break;
        case 'message':
          window.location.href = `sms:&body=${encodeURIComponent(content)}`;
          break;
        case 'whatsapp':
          window.open(
            `https://wa.me/?text=${encodeURIComponent(content)}`,
            '_blank'
          );
          break;
        case 'twitter':
          window.open(
            `https://twitter.com/intent/tweet?text=${encodeURIComponent(content)}`,
            '_blank'
          );
          break;
        case 'facebook':
          window.open(
            `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(content)}`,
            '_blank'
          );
          break;
        case 'instagram':
        case 'menu':
          if (navigator.share) {
            await navigator.share({ text: content });
          } else {
            console.warn('Sharing not supported on this browser.');
          }
          break;
        default:
          console.warn(`${platform} sharing is not supported.`);
      }
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          role="dialog"
          aria-modal="true"
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 px-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onDismiss}
        >
          <motion.div
            className="flex flex-row flex-wrap justify-center gap-7 px-4 py-6 bg-bg-interactive-primary-light dark:bg-bg-interactive-primary-dark rounded-md"
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            onClick={(e) => e.stopPropagation()}
          >
            {SHARE_OPTIONS.map((option) => (
              <button
                key={option.id}
                onClick={() => handleShare(option.id)}
                className="flex flex-col h-[76px] w-16 items-center justify-center gap-2 px-px"
                aria-label={`Share via ${option.name}`}
              >
                <div
                  className={clsx(
                    'flex size-12 items-center justify-center rounded-full bg-accent-moderate dark:bg-accent-moderate'
                  )}
                  style={{ backgroundColor: option.bgColor ?? undefined }}
                >
                  {option.id === 'copy' && <MdLink size={24} color="white" />}
                  {option.id === 'message' && (
                    <MdMailOutline
                      size={option.size ? parseInt(option.size) : 24}
                      color="white"
                    />
                  )}
                  {option.id === 'whatsapp' && (
                    <IoLogoWhatsapp
                      size={option.size ? parseInt(option.size) : 24}
                      color="white"
                    />
                  )}
                  {option.id === 'facebook' && (
                    <IoLogoFacebook
                      size={option.size ? parseInt(option.size) : 24}
                      color="white"
                    />
                  )}
                  {option.id === 'instagram' && (
                    <FaInstagram
                      size={option.size ? parseInt(option.size) : 24}
                      color="white"
                    />
                  )}
                  {option.id === 'twitter' && (
                    <BsX
                      size={option.size ? parseInt(option.size) : 24}
                      color="white"
                    />
                  )}
                  {option.id === 'menu' && (
                    <DotsHorizontalIcon
                      color={semanticColors.accent.bold.dark}
                    />
                  )}
                </div>
                <span className="text-center text-xs/[150%] font-[400] text-fg-muted-light dark:text-fg-muted-dark">
                  {option.name}
                </span>
              </button>
            ))}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
