import React, { useState } from 'react';
import {
  type Control,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form';

import { ControlledSelect } from '@/components/ui';

export interface AgeRangeOption {
  label: string;
  value: string;
  minAge: number;
  maxAge: number | null; // null for "65+" type ranges
}

const DEFAULT_AGE_RANGES: AgeRangeOption[] = [
  { label: '18-24', value: '18-24', minAge: 18, maxAge: 24 },
  { label: '25-34', value: '25-34', minAge: 25, maxAge: 34 },
  { label: '35-44', value: '35-44', minAge: 35, maxAge: 44 },
  { label: '45-54', value: '45-54', minAge: 45, maxAge: 54 },
  { label: '55-64', value: '55-64', minAge: 55, maxAge: 64 },
  { label: '65+', value: '65+', minAge: 65, maxAge: null },
];

interface AgeRangeSelectorProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  ageRanges?: AgeRangeOption[];
  placeholder?: string;
  disabled?: boolean;
  testID?: string;
  className?: string;
  error?: string;
  modalTitle?: string;
}

export const AgeRangeSelector = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  ageRanges = DEFAULT_AGE_RANGES,
  placeholder = 'Select age range',
  disabled = false,
  className = '',
  error,
  modalTitle = 'Select age range',
}: AgeRangeSelectorProps<TFieldValues, TName>) => {
  const [ageRangeModal, setAgeRangeModal] = useState(false);

  const getAgeRangeLabel = (value: string | undefined): string => {
    if (!value) return placeholder;
    return (
      ageRanges.find((range) => range.value === value)?.label || placeholder
    );
  };

  // const getAgeRangeData = (
  //   value: string | undefined
  // ): AgeRangeOption | null => {
  //   if (!value) return null;
  //   return ageRanges.find((range) => range.value === value) || null;
  // };

  return (
    <ControlledSelect
      name={name}
      control={control}
      options={ageRanges}
      placeholder={placeholder}
      disabled={disabled}
    />
  );
};

export { DEFAULT_AGE_RANGES };

export const isAgeInRange = (age: number, range: AgeRangeOption): boolean => {
  if (age < range.minAge) return false;
  if (range.maxAge === null) return age >= range.minAge; // For "65+" type ranges
  return age <= range.maxAge;
};

export const findAgeRangeForAge = (
  age: number,
  ranges: AgeRangeOption[] = DEFAULT_AGE_RANGES
): AgeRangeOption | null => {
  return ranges.find((range) => isAgeInRange(age, range)) || null;
};
