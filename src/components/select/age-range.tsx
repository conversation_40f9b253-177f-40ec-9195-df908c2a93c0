import React, { useState } from 'react';
import {
  type Control,
  Controller,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form';

import { colors, Modal } from '@/components/ui';
import { IoCheckmark, IoChevronDown } from 'react-icons/io5';

export interface AgeRangeOption {
  label: string;
  value: string;
  minAge: number;
  maxAge: number | null; // null for "65+" type ranges
}

const DEFAULT_AGE_RANGES: AgeRangeOption[] = [
  { label: '18-24', value: '18-24', minAge: 18, maxAge: 24 },
  { label: '25-34', value: '25-34', minAge: 25, maxAge: 34 },
  { label: '35-44', value: '35-44', minAge: 35, maxAge: 44 },
  { label: '45-54', value: '45-54', minAge: 45, maxAge: 54 },
  { label: '55-64', value: '55-64', minAge: 55, maxAge: 64 },
  { label: '65+', value: '65+', minAge: 65, maxAge: null },
];

interface AgeRangeSelectorProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  ageRanges?: AgeRangeOption[];
  placeholder?: string;
  disabled?: boolean;
  testID?: string;
  className?: string;
  error?: string;
  modalTitle?: string;
}

export const AgeRangeSelector = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  ageRanges = DEFAULT_AGE_RANGES,
  placeholder = 'Select age range',
  disabled = false,
  className = '',
  error,
  modalTitle = 'Select age range',
}: AgeRangeSelectorProps<TFieldValues, TName>) => {
  const [ageRangeModal, setAgeRangeModal] = useState(false);

  const getAgeRangeLabel = (value: string | undefined): string => {
    if (!value) return placeholder;
    return (
      ageRanges.find((range) => range.value === value)?.label || placeholder
    );
  };

  // const getAgeRangeData = (
  //   value: string | undefined
  // ): AgeRangeOption | null => {
  //   if (!value) return null;
  //   return ageRanges.find((range) => range.value === value) || null;
  // };

  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <div className={`flex flex-col gap-1 ${className}`}>
            <button
              className={`h-12 flex flex-row items-center justify-between rounded-md border p-3 ${
                error
                  ? 'border-red-500'
                  : 'border-border-subtle-light dark:border-border-subtle-dark'
              } ${disabled ? 'opacity-50' : ''}`}
              onClick={() => {
                if (!disabled) {
                  setAgeRangeModal(true);
                }
              }}
              disabled={disabled}
            >
              <span
                className={
                  field.value
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {getAgeRangeLabel(field.value)}
              </span>
              <IoChevronDown size={20} color={colors.grey[70]} />
            </button>
            {error && (
              <span className="px-1 text-sm text-red-500">{error}</span>
            )}
          </div>
        )}
      />

      <Modal
        isOpen={ageRangeModal}
        onClose={() => setAgeRangeModal(false)}
        title={modalTitle}
      >
        <div className="flex flex-col items-start gap-4 px-4">
          {ageRanges.map((range) => (
            <Controller
              key={range.value}
              control={control}
              name={name}
              render={({ field }) => (
                <button
                  className="w-full flex flex-row items-center justify-between"
                  onClick={() => {
                    field.onChange(range.value);
                    setAgeRangeModal(false);
                  }}
                >
                  <div className="flex-1">
                    <span className="py-4 text-base font-bold dark:text-neutral-100">
                      {range.label}
                    </span>
                  </div>
                  {field.value === range.value && (
                    <IoCheckmark
                      size={20}
                      color={colors.brand['60'] || colors.grey[70]}
                    />
                  )}
                </button>
              )}
            />
          ))}
        </div>
      </Modal>
    </>
  );
};

export { DEFAULT_AGE_RANGES };

export const isAgeInRange = (age: number, range: AgeRangeOption): boolean => {
  if (age < range.minAge) return false;
  if (range.maxAge === null) return age >= range.minAge; // For "65+" type ranges
  return age <= range.maxAge;
};

export const findAgeRangeForAge = (
  age: number,
  ranges: AgeRangeOption[] = DEFAULT_AGE_RANGES
): AgeRangeOption | null => {
  return ranges.find((range) => isAgeInRange(age, range)) || null;
};
