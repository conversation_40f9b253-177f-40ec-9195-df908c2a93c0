import React from 'react';
import {
  type Control,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form';

import { ControlledSelect } from '@/components/ui';

export interface CountryOption {
  label: string;
  value: string;
  code: string; // ISO country code for flag
}

interface CountrySelectorProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  countries: CountryOption[];
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export const CountrySelector = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  countries,
  disabled = false,
  className = '',
}: CountrySelectorProps<TFieldValues, TName>) => {
  return (
    <div className={className}>
      <ControlledSelect
        name={name}
        control={control}
        options={countries}
        placeholder="Select country"
        disabled={disabled}
      />
    </div>
  );
};
