import React, { useState } from 'react';
import {
  type Control,
  Controller,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form';

import { colors, Image, Modal } from '@/components/ui';
import { cn } from '@/lib';
import { IoCheckmark, IoChevronDown } from 'react-icons/io5';

export interface CountryOption {
  label: string;
  value: string;
  code: string; // ISO country code for flag
}

interface CountrySelectorProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  countries: CountryOption[];
  placeholder?: string;
  disabled?: boolean;
  testID?: string;
  className?: string;
  error?: string;
  modalSnapPoints?: string[];
  flagBaseUrl?: string;
}

export const CountrySelector = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  countries,
  placeholder = 'Select Country',
  disabled = false,
  className = '',
  error,
  flagBaseUrl = 'https://flagcdn.com/128x96',
}: CountrySelectorProps<TFieldValues, TName>) => {
  const [countryModal, setCountryModal] = useState(false);

  const getCountryData = (value: string | undefined) => {
    if (!value) return null;
    return countries.find((country) => country.value === value) || null;
  };

  const getFlagUrl = (countryCode: string) => {
    return `${flagBaseUrl}/${countryCode}.png`;
  };

  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field }) => {
          const selectedCountry = getCountryData(field.value);

          return (
            <div className={cn('flex flex-col gap-1', className)}>
              <button
                className={cn(
                  'h-12 flex flex-row items-center justify-between rounded-md border p-3',
                  error
                    ? 'border-red-500'
                    : 'border-border-subtle-light dark:border-border-subtle-dark',
                  disabled && 'opacity-50'
                )}
                onClick={() => {
                  if (!disabled) {
                    setCountryModal(true);
                  }
                }}
                disabled={disabled}
              >
                <div className="flex-row items-center gap-x-1">
                  {selectedCountry && (
                    <Image
                      src={getFlagUrl(selectedCountry.code)}
                      alt={selectedCountry.label}
                      className="size-6 rounded-xl"
                    />
                  )}
                  <span
                    className={
                      selectedCountry
                        ? 'dark:text-neutral-100'
                        : 'text-neutral-400 dark:text-neutral-500'
                    }
                  >
                    {selectedCountry?.label ?? placeholder}
                  </span>
                </div>
                <IoChevronDown size={24} color={colors.grey[70]} />
              </button>
              {error && (
                <span className="px-1 text-sm text-red-500">{error}</span>
              )}
            </div>
          );
        }}
      />

      <Modal isOpen={countryModal} onClose={() => setCountryModal(false)}>
        <div className="px-4 pb-4">
          {countries.map((country) => (
            <Controller
              key={country.value}
              control={control}
              name={name}
              render={({ field }) => (
                <button
                  className="flex flex-row items-center border-b border-neutral-300 dark:border-neutral-700"
                  onClick={() => {
                    field.onChange(country.value);
                    setCountryModal(true);
                  }}
                >
                  <div className="p-2 size-10">
                    <Image
                      src={getFlagUrl(country.code)}
                      alt=""
                      fill
                      className="size-10 gap-2 rounded-xl"
                    />
                  </div>
                  <span className="flex-1 dark:text-neutral-100">
                    {country.label}
                  </span>
                  {field.value === country.value && (
                    <IoCheckmark
                      name="checkmark"
                      size={20}
                      color={colors.brand['60']}
                    />
                  )}
                </button>
              )}
            />
          ))}
        </div>
      </Modal>
    </>
  );
};
