import {
  type Control,
  Controller,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form';

import { colors, Modal } from '@/components/ui';
import { cn } from '@/lib';
import { useState } from 'react';
import { IoCheckmark, IoChevronDown } from 'react-icons/io5';

export type GenderOption = 'male' | 'female' | 'non-binary';

const GENDER_OPTIONS: {
  label: string;
  value: GenderOption;
}[] = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
  { label: 'Non binary', value: 'non-binary' },
];

interface GenderSelectorProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  placeholder?: string;
  disabled?: boolean;
  testID?: string;
  className?: string;
  error?: string;
}

export const GenderSelector = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  placeholder = 'Select gender',
  disabled = false,
  className = '',
  error,
}: GenderSelectorProps<TFieldValues, TName>) => {
  const [genderModal, setGenderModal] = useState(false);

  const getGenderLabel = (value: GenderOption | undefined): string => {
    if (!value) return placeholder;
    return (
      GENDER_OPTIONS.find((option) => option.value === value)?.label ||
      placeholder
    );
  };

  return (
    <>
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <div className={cn('flex flex-col gap-1', className)}>
            <button
              className={cn(
                'h-12 flex flex-row items-center justify-between rounded-md border p-3',
                error
                  ? 'border-red-500'
                  : 'border-border-subtle-light dark:border-border-subtle-dark',
                disabled && 'opacity-50'
              )}
              onClick={() => {
                if (!disabled) {
                  setGenderModal(true);
                }
              }}
              disabled={disabled}
            >
              <span
                className={
                  field.value
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {getGenderLabel(field.value)}
              </span>
              <IoChevronDown
                name="chevron-down"
                size={20}
                color={colors.grey[70]}
              />
            </button>
            {error && (
              <span className="px-1 text-sm text-red-500">{error}</span>
            )}
          </div>
        )}
      />

      <Modal
        isOpen={genderModal}
        onClose={() => setGenderModal(false)}
        title="Select gender"
      >
        <div className="flex flex-col items-start gap-2 px-4">
          {GENDER_OPTIONS.map((option) => (
            <Controller
              key={option.value}
              control={control}
              name={name}
              render={({ field }) => (
                <button
                  className="w-full flex flex-row items-center justify-between"
                  onClick={() => {
                    field.onChange(option.value);
                    setGenderModal(true);
                  }}
                >
                  <span className="py-4 text-base font-bold dark:text-neutral-100">
                    {option.label}
                  </span>
                  {field.value === option.value && (
                    <IoCheckmark size={20} color={colors.grey[70]} />
                  )}
                </button>
              )}
            />
          ))}
        </div>
      </Modal>
    </>
  );
};
