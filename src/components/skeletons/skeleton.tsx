'use client';

import { cn } from '@/lib/utils';

type SkeletonProps = React.HTMLAttributes<HTMLDivElement>;

export const Skeleton = ({ className, ...rest }: SkeletonProps) => {
  return (
    <div
      className={cn(
        'relative overflow-hidden rounded-md bg-bg-muted-light dark:bg-bg-muted-dark',
        className
      )}
      {...rest}
    >
      <div className="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/20 to-transparent" />
    </div>
  );
};

<div className="flex justify-center items-center h-48 w-full bg-grey-80 rounded-md animate-pulse"></div>;
