'use client';

import * as React from 'react';

import { type UserObjectData } from '@/api/auth';
import { FavouriteAccountCard } from '../cards/favourite-account';
import { EmptyState } from '../ui/empty';
import { LoadingScreen } from '@/components/loaders';

type Props = {
  accounts?: UserObjectData[];
  index: number;
  isLoading?: boolean;
};

export const FavouriteCreatorsTab: React.FC<Props> = ({
  accounts,
  index,
  isLoading = false,
}) => {
  const listData = (
    Array.isArray(accounts) ? accounts : []
  ) as UserObjectData[];

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="flex flex-col w-full h-full bg-white dark:bg-grey-100">
      <div className="flex-1 overflow-y-auto p-4">
        {listData.length === 0 ? (
          <div className="flex flex-1 h-full items-center justify-center">
            <EmptyState />
          </div>
        ) : (
          <ul
            key={index}
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4"
          >
            {listData.map((item) => (
              <li key={item?.id?.toString?.() ?? Math.random().toString()}>
                <FavouriteAccountCard
                  id={item.id}
                  image={item.profileImageUrl}
                  fullname={item.fullName}
                  username={item.username}
                />
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};
