import * as React from 'react';

import { type IEvent } from '@/api/events';
import { EmptyState } from '../ui/empty';
import { LoadingScreen } from '@/components/loaders';
import { MyEventsCard } from '@/components/cards/my-event-card';

type Props = {
  events?: IEvent[];
  index: number;
  isPending: boolean;
  isFetchingNextPage: boolean;
  handleLoadMore?: () => void;
};

export const CreatorEventsTab = ({
  events,
  index,
  isPending,
  handleLoadMore,
  isFetchingNextPage,
}: Props) => {
  const loadMoreRef = React.useRef<HTMLDivElement | null>(null);

  React.useEffect(() => {
    if (!handleLoadMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          handleLoadMore();
        }
      },
      { rootMargin: '200px' }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [handleLoadMore]);

  return (
    <div
      className="flex-1 bg-white dark:bg-grey-100 min-h-screen overflow-y-auto"
      key={index}
    >
      <div className="flex-1 p-4">
        {isPending ? (
          <LoadingScreen />
        ) : events && events.length > 0 ? (
          <>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {events.map((event) => (
                <MyEventsCard
                  key={event.id}
                  {...event}
                  attendees={event.ticketsSold}
                />
              ))}
            </div>

            <div
              ref={loadMoreRef}
              className="h-10 flex items-center justify-center"
            >
              {isFetchingNextPage && <p>Loading more…</p>}
            </div>
          </>
        ) : (
          <EmptyState />
        )}
      </div>
    </div>
  );
};
