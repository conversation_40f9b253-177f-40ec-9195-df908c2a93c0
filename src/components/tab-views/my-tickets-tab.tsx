import * as React from 'react';
import { ITicketExtension } from '@/api/events';

import { TicketItem } from '../tickets/item';
import { EmptyState } from '../ui/empty';

type Props = {
  tickets?: ITicketExtension[];
  index: number;
};

export const UserTicketsTab: React.FC<Props> = ({ tickets, index }) => {
  const hasTickets = tickets && tickets.length > 0;

  return (
    <div className="flex-1 bg-white dark:bg-grey-100">
      {hasTickets ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:p-4 py-4">
          {tickets.map((item) => (
            <TicketItem
              key={item.id}
              {...item}
              title={`${item.title} - ${Object.keys(item.meta.breakdown || {})[0]}`}
              id={item.id}
              hasPresale={false}
              isPast={index === 1}
            />
          ))}
        </div>
      ) : (
        <div className="flex flex-1 h-full items-center justify-center p-4">
          <EmptyState />
        </div>
      )}
    </div>
  );
};
