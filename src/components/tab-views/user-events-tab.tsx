'use client';

import * as React from 'react';

import { type IEvent } from '@/api/events';
import { EventFavoriteCard } from '../cards/event-favorite';
import { EmptyState } from '../ui/empty';
import { LoadingScreen } from '@/components/loaders';

type Props = {
  events?: IEvent[];
  index: number;
  isLoading?: boolean;
};

export const UserEventsTab: React.FC<Props> = ({
  events,
  index,
  isLoading = false,
}) => {
  if (isLoading) {
    return <LoadingScreen />;
  }
  return (
    <div className="flex-1 bg-white dark:bg-grey-100 min-h-screen p-4">
      {Array.isArray(events) && events.length > 0 ? (
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          key={index}
        >
          {events.map((item: any) => (
            <EventFavoriteCard
              key={item?.id?.toString?.() ?? Math.random().toString()}
              {...item}
              isfavourite={true}
              attendees={item.ticketsSold}
              isFavoriteTab
            />
          ))}
        </div>
      ) : (
        <div className="flex flex-1 h-full items-center justify-center">
          <EmptyState />
        </div>
      )}
    </div>
  );
};
