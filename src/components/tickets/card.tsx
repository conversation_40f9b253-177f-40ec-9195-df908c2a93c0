'use client';

import React from 'react';
import moment from 'moment';
import QRCode from 'react-qr-code';
import { useTheme } from 'next-themes';

import { EventFormat, type ITicketExtension } from '@/api/events';

import { H2, Image, MdBoldLabel, P, colors } from '@/components/ui';

interface TicketCardProps {
  ticket: ITicketExtension;
}

export const TicketCard = React.forwardRef<HTMLDivElement, TicketCardProps>(
  ({ ticket }, ref) => {
    const { resolvedTheme } = useTheme();
    const isDark = resolvedTheme === 'dark';

    const isPhysicalLocation = ticket.event.eventFormat !== EventFormat.ONLINE;
    const locationText =
      isPhysicalLocation && ticket?.location
        ? ticket.location.landmark ||
          ticket.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
        : ticket?.event.onlineEventUrl || 'Online';

    return (
      <div
        ref={ref}
        className={`
    w-[350px] 
    rounded-[16px] 
    overflow-hidden 
    ${isDark ? 'text-gray-100' : 'text-white'} 
  `}
        style={{
          background: `linear-gradient(180deg, #131214 82%, #53575A 100%)`,
        }}
      >
        <div className="relative h-[270px] rounded-t-lg overflow-hidden">
          <Image
            src={typeof ticket.bannerUrl === 'string' ? ticket.bannerUrl : ''}
            alt="Event Banner"
            className="w-full h-full object-cover rounded-t-lg"
            fill
          />
          <div
            style={{
              position: 'absolute',
              inset: 0,
              background:
                'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%)',
            }}
          />
          <div className="absolute bottom-3 mx-3 flex flex-col gap-3">
            <H2 themed={false}>{ticket.title}</H2>
            <div className="flex flex-row w-full justify-between gap-4">
              <div className="flex-1 flex-col gap-2">
                <P className="text-grey-60 dark:text-grey-50">DATE</P>
                <MdBoldLabel themed={false}>
                  {moment.utc(ticket.startTime).format('MMM D · LT')}
                </MdBoldLabel>
              </div>
              <div className="flex-col gap-2">
                <P className="text-grey-60 dark:text-grey-50">LOCATION</P>
                <MdBoldLabel themed={false} className="truncate">
                  {locationText}
                </MdBoldLabel>
              </div>
            </div>
          </div>
        </div>

        <div className="p-3 pb-6 space-y-3 bg-linear-to-b from-transparent to-black/50">
          {ticket.isUsed &&
            moment().isAfter(moment.utc(ticket.event.endTime)) && (
              <div className="h-8 w-[102px] flex items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
                <MdBoldLabel
                  themed={false}
                  className="text-green-60 dark:text-green-40"
                >
                  Attended
                </MdBoldLabel>
              </div>
            )}

          <div className="flex flex-col gap-5">
            <P className="text-grey-60 dark:text-grey-50">BARCODE</P>
            <div className="mx-auto">
              <QRCode
                value={ticket.id}
                size={215}
                bgColor="transparent"
                fgColor={colors.white}
                level="Q"
              />
            </div>
          </div>

          <div className="flex flex-row justify-between">
            <div className="flex-1 space-y-1">
              <P className="text-grey-60 dark:text-grey-50">NAME</P>
              <MdBoldLabel themed={false}>{ticket?.user?.fullName}</MdBoldLabel>
            </div>
            <div className="flex-1 space-y-1">
              <P className="text-grey-60 dark:text-grey-50">TICKET GRADE</P>
              <MdBoldLabel themed={false}>
                {Object.keys(ticket?.meta.breakdown || {})[0]}
              </MdBoldLabel>
            </div>
          </div>

          <div className="flex flex-row justify-between">
            <div className="flex-1 space-y-1">
              <P className="text-grey-60 dark:text-grey-50">TICKET ID</P>
              <MdBoldLabel themed={false}>{ticket?.ticketId}</MdBoldLabel>
            </div>
            <div className="flex-1 space-y-1">
              <P className="text-grey-60 dark:text-grey-50">QUANTITY</P>
              <MdBoldLabel themed={false}>
                {Object.values(ticket?.meta.breakdown || {})[0]}
              </MdBoldLabel>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

TicketCard.displayName = 'TicketCard';
