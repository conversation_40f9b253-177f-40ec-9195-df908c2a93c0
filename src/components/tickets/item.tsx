'use client';

import { useRouter } from 'next/navigation';
import React from 'react';
import QRCode from 'react-qr-code';
import { useTheme } from 'next-themes';

import { type ITicketExtension } from '@/api/events';
import { colors, H5, Image, TicketQrBg } from '@/components/ui';

interface TicketItemProps extends ITicketExtension {
  isPast?: boolean;
  hasPresale?: boolean;
}

export const TicketItem: React.FC<TicketItemProps> = ({
  id,
  title,
  hasPresale,
  isPast,
  bannerUrl,
  transactionId,
}) => {
  const router = useRouter();
  const qrColor = '#000';
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  return (
    <div
      onClick={() =>
        router.push(`/tickets/${id}?transactionId=${transactionId}`)
      }
      className="flex h-[103px] cursor-pointer select-none overflow-hidden rounded-xl shadow-md"
    >
      <div className="relative flex-1">
        <Image
          src={
            isDark
              ? '/images/ticket/ticket-bg-subtle-dark.png'
              : '/images/ticket/ticket-bg-subtle-light.png'
          }
          alt="Ticket Background"
          className="h-full w-full rounded-l-xl object-cover"
          fill
        />

        <div className="absolute left-3.5 top-2.5 flex items-center gap-2">
          <Image
            src={bannerUrl}
            alt={title}
            className="rounded-md aspect-square"
            width={68}
            height={68}
          />
          <div className="flex flex-col">
            <H5 className="line-clamp-2">{title}</H5>

            {hasPresale && (
              <div
                className={`mt-1 h-5 w-[58px] rounded-full text-center text-xs font-bold ${
                  isDark
                    ? 'bg-green-800 text-green-200'
                    : 'bg-green-100 text-green-800'
                } flex items-center justify-center`}
              >
                Presale
              </div>
            )}

            <div
              className={`mt-1 h-7 w-[94px] rounded-full border text-center text-xs font-bold flex items-center justify-center ${
                isDark
                  ? 'border-blue-400 text-blue-400'
                  : 'border-blue-900 text-blue-900'
              }`}
            >
              See details
            </div>
          </div>
        </div>
      </div>

      <div className="relative flex w-[74px] justify-end">
        <TicketQrBg
          color={
            !isPast
              ? colors.brand[50]
              : isDark
                ? colors.grey[70]
                : colors.grey[30]
          }
          className="h-full w-full"
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <QRCode
            size={58}
            value={id}
            bgColor="transparent"
            fgColor={qrColor}
          />
        </div>
      </div>
    </div>
  );
};
