'use client';
import dayjs from 'dayjs';
import { useState, useMemo } from 'react';
import { type ITicketExtension } from '@/api/events';
import { useGetUserTickets } from '@/api/users';
import { LoadingScreen } from '@/components/loaders';
import { UserTicketsTab } from '@/components/tab-views';
import { AppTab } from '@/components/ui';
import { useAuth } from '@/lib';
import { type TabScreenItem } from '@/types';

export const TicketsPage = () => {
  const { user } = useAuth();
  const [index, setIndex] = useState(0);

  const { data: ticketsData, isLoading } = useGetUserTickets({
    variables: { id: user?.id || '' },
    enabled: !!user?.id,
  });

  const ticketsList = useMemo(() => {
    if (!ticketsData?.eventsWithTicket || !user?.id) return [];

    const uniqueTransactions = new Set<string>();

    return ticketsData.eventsWithTicket.reduce((tickets, event) => {
      const eventTickets = event.tickets
        .filter((ticket) => ticket.userId === user.id)
        .map((ticket) => ({
          ...ticket,
          title: event.title,
          bannerUrl: event.bannerUrl,
          startTime: event.startTime,
          location: event.location,
          organizer: event.organizer,
          eventId: event.id,
          event,
        }))
        .filter((ticket) => {
          if (uniqueTransactions.has(ticket.transactionId)) return false;
          uniqueTransactions.add(ticket.transactionId);
          return true;
        });

      return [...tickets, ...eventTickets];
    }, [] as ITicketExtension[]);
  }, [ticketsData?.eventsWithTicket, user?.id]);

  const { upcomingTickets, pastTickets } = useMemo(() => {
    const now = dayjs();

    const upcoming: ITicketExtension[] = [];
    const past: ITicketExtension[] = [];

    ticketsList.forEach((ticket) => {
      const eventEndTime = dayjs(ticket.event.endTime);

      if (eventEndTime.isAfter(now)) {
        upcoming.push(ticket);
      } else {
        past.push(ticket);
      }
    });

    return { upcomingTickets: upcoming, pastTickets: past };
  }, [ticketsList]);

  const tabItems: TabScreenItem[] = useMemo(() => {
    return [
      {
        key: 'Upcoming',
        title: 'Upcoming',
        component: () => <UserTicketsTab tickets={upcomingTickets} index={0} />,
      },
      {
        key: 'Past',
        title: 'Past',
        component: () => <UserTicketsTab tickets={pastTickets} index={1} />,
      },
    ];
  }, [upcomingTickets, pastTickets]);

  if (isLoading) return <LoadingScreen />;

  return (
    <div className="flex-1 ">
      <AppTab
        items={tabItems}
        tabIndex={index}
        tabSetIndex={(i) => setIndex(i)}
      />
    </div>
  );
};
