'use client';

import * as React from 'react';
import { Tabs, Tabs<PERSON>ist, TabsTrigger, TabsContent } from '@/components/ui';
import { cn } from '@/lib';
import { type AppTabProps } from '@/types';

import { XsBoldLabel, XsRegularLabel } from '@/components/ui';

export const AppTab: React.FC<AppTabProps> = ({
  items,
  containerClassName,
  contentContainerClassName,
  countClassName,
  countContainerClassName,
  tabBarClassName,
  tabBarFocusedClassName,
  tabBarLabelClassName,
  tabBarLabelActiveClassName,
  tabBarLabelInactiveClassName,
  tabIndex = 0,
  tabSetIndex,
}) => {
  const [index, setIndex] = React.useState(tabIndex);

  React.useEffect(() => {
    setIndex(tabIndex);
  }, [tabIndex]);

  return (
    <div className={cn('md:px-4 py-2', containerClassName)}>
      <Tabs
        value={items[index]?.key}
        onValueChange={(val) => {
          const i = items.findIndex((item) => item.key === val);
          if (i !== -1) {
            setIndex(i);
            tabSetIndex?.(i);
          }
        }}
      >
        <TabsList
          className={cn(
            'flex h-11 w-full items-center rounded-full bg-grey-10 dark:bg-grey-90 p-0.5',
            contentContainerClassName
          )}
        >
          {items.map((item, i) => {
            const isFocused = index === i;

            return (
              <TabsTrigger
                key={item.key}
                value={item.key}
                className={cn(
                  'relative flex-1 h-full rounded-full',
                  tabBarClassName,
                  isFocused
                    ? cn('bg-white dark:bg-grey-80', tabBarFocusedClassName)
                    : 'bg-transparent'
                )}
              >
                <XsBoldLabel
                  className={cn(
                    tabBarLabelClassName,
                    isFocused
                      ? cn(
                          'text-brand-60 dark:text-brand-50',
                          tabBarLabelActiveClassName
                        )
                      : cn(
                          'text-grey-50 dark:text-grey-60',
                          tabBarLabelInactiveClassName
                        )
                  )}
                >
                  {item.title}
                </XsBoldLabel>

                {!!item.notificationCount && (
                  <div
                    className={cn(
                      'absolute -top-[9px] right-[7px] flex size-5 items-center justify-center rounded-full bg-red-60 dark:bg-red-60',
                      countContainerClassName
                    )}
                  >
                    <XsRegularLabel
                      className={cn('text-white', countClassName)}
                    >
                      {item.notificationCount}
                    </XsRegularLabel>
                  </div>
                )}
              </TabsTrigger>
            );
          })}
        </TabsList>

        {items.map((item) => (
          <TabsContent key={item.key} value={item.key} className="mt-2">
            <item.component />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};
