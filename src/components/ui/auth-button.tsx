'use client';

import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui';

export const AuthButton = () => {
  const { data: session, status } = useSession();

  if (status === 'loading') {
    return null;
  }

  if (session) {
    return (
      <Link href="/events" passHref>
        <Button variant="outline">Dashboard</Button>
      </Link>
    );
  }

  return (
    <Link href="/login" passHref>
      <Button
        variant="outline"
        className="text-white border-white hover:bg-white/10"
      >
        Login
      </Button>
    </Link>
  );
};
