'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

export const Breadcrumbs = () => {
  const pathname = usePathname();
  const segments = pathname.split('/').filter(Boolean);

  return (
    <div className="text-sm flex flex-wrap items-center gap-1 text-fg-muted-light dark:text-fg-muted-dark">
      {segments.map((seg, i) => {
        const href = '/' + segments.slice(0, i + 1).join('/');
        const isLast = i === segments.length - 1;

        const label =
          seg.charAt(0).toUpperCase() + seg.slice(1).replace(/-/g, ' ');

        return (
          <span key={i} className="flex items-center">
            {!isLast ? (
              <Link href={href} className="hover:underline">
                {label}
              </Link>
            ) : (
              <span className={cn('dark:text-fg-base-dark text-fg-base-light')}>
                {label}
              </span>
            )}
            {!isLast && <span className="mx-1">/</span>}
          </span>
        );
      })}
    </div>
  );
};
