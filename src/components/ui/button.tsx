import React, { forwardRef, useMemo } from 'react';
import type { ButtonHTMLAttributes } from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

const button = tv({
  slots: {
    container:
      'relative flex h-12 flex-row items-center justify-center rounded-full px-2 cursor-pointer gap-1',
    label: 'font-aeonik-black text-base/100 font-bold',
    indicator: 'h-6 w-6 animate-spin text-white fill-current',
    iconContainer: 'flex items-center justify-center',
  },
  variants: {
    variant: {
      default: {
        container: 'bg-accent-moderate dark:bg-accent-moderate',
        label: 'text-accent-on-accent dark:text-accent-on-accent',
        indicator: 'text-accent-on-accent dark:text-accent-on-accent',
        iconContainer: 'text-accent-on-accent dark:text-accent-on-accent',
      },
      secondary: {
        container: 'bg-accent-subtle-light dark:bg-accent-subtle-dark',
        label: 'text-accent-bold-light dark:text-accent-bold-dark',
        indicator: 'text-white dark:text-white',
        iconContainer: 'text-accent-bold-light dark:text-accent-bold-dark',
      },
      outline: {
        container: 'border border-brand-70 dark:border-brand-40',
        label: 'text-brand-70 dark:text-brand-40',
        indicator: 'text-brand-70 dark:text-brand-40',
        iconContainer: 'text-brand-70 dark:text-brand-40',
      },
      destructive: {
        container: 'bg-red-50 dark:bg-red-50',
        label: 'text-white',
        indicator: 'text-white',
        iconContainer: 'text-white',
      },
      ghost: {
        container: 'bg-transparent',
        label: 'text-accent-bold-light dark:text-accent-bold-dark',
        indicator: 'text-accent-bold-light dark:text-accent-bold-dark',
        iconContainer: 'text-accent-bold-light dark:text-accent-bold-dark',
      },
      link: {
        container: 'bg-transparent',
        label: 'text-accent-bold-light dark:text-accent-bold-dark',
        indicator: 'text-accent-bold-light dark:text-accent-bold-dark',
        iconContainer: 'text-accent-bold-light dark:text-accent-bold-dark',
      },
    },
    size: {
      default: {
        container: 'h-12 px-4',
        label: 'text-base',
        iconContainer: 'w-7 h-7',
      },
      lg: {
        container: 'h-14 px-8',
        label: 'text-xl',
        iconContainer: 'w-6 h-6',
      },
      sm: {
        container: 'h-10 px-3',
        label: 'text-sm',
        indicator: 'h-2 w-2',
        iconContainer: 'w-4 h-4',
      },
      xs: {
        container: 'h-7 items-center justify-between px-2.5',
        label: 'text-xs',
        indicator: 'h-2 w-2',
        iconContainer: 'w-4 h-4',
      },
      modal: {
        container: 'h-8 px-4',
        label: 'text-base',
        indicator: 'h-4 w-4',
        iconContainer: 'w-4 h-4',
      },
      icon: {
        container: 'w-9 h-9',
        iconContainer: 'w-6 h-6',
      },
    },
    disabled: {
      true: {
        container:
          'bg-bg-disabled-light dark:bg-bg-disabled-dark cursor-not-allowed',
        label: 'text-fg-disabled-light dark:text-fg-disabled-dark',
        indicator: 'text-neutral-400 dark:text-neutral-400',
        iconContainer: 'text-fg-disabled-light dark:text-fg-disabled-light',
      },
    },
    fullWidth: {
      true: {
        container: 'w-full',
      },
      false: {
        container: 'self-center',
      },
    },
    iconPosition: {
      left: {
        iconContainer: 'left-4',
      },
      right: {
        iconContainer: 'right-4',
      },
    },
  },
  defaultVariants: {
    variant: 'default',
    disabled: false,
    fullWidth: true,
    size: 'default',
    iconPosition: 'left',
  },
});

export type ButtonVariants = VariantProps<typeof button>;
interface Props
  extends ButtonVariants,
    Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'disabled'> {
  label?: string;
  loading?: boolean;
  className?: string;
  textClassName?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  iconClassName?: string;
  onPress?: () => void;
}

export const Button = forwardRef<HTMLButtonElement, Props>(
  (
    {
      label: text,
      loading = false,
      variant = 'default',
      disabled = false,
      size = 'default',
      className = '',
      textClassName = '',
      icon,
      iconPosition = 'left',
      iconClassName = '',
      children,
      onPress,
      ...props
    },
    ref
  ) => {
    const styles = useMemo(
      () => button({ variant, disabled, size, iconPosition }),
      [variant, disabled, size, iconPosition]
    );

    const renderContent = () => {
      if (children) {
        return children;
      }

      if (loading) {
        return (
          <svg
            className={styles.indicator({ className: textClassName })}
            viewBox="0 0 50 50"
          >
            <circle
              className="opacity-25"
              cx="25"
              cy="25"
              r="20"
              stroke="currentColor"
              strokeWidth="5"
              fill="none"
            />
            <path
              className="opacity-75 animate-spin"
              fill="currentColor"
              d="M43.94 25c0-10.494-8.506-19-19-19v5a14 14 0 1014 14h5z"
            />
          </svg>
        );
      }

      const textElement = text ? (
        <span className={styles.label({ className: textClassName })}>
          {text}
        </span>
      ) : null;

      const iconElement = icon ? (
        <span className={styles.iconContainer({ className: iconClassName })}>
          {icon}
        </span>
      ) : null;

      return iconPosition === 'left' ? (
        <>
          {iconElement}
          {textElement}
        </>
      ) : (
        <>
          {textElement}
          {iconElement}
        </>
      );
    };

    return (
      <button
        disabled={disabled || loading}
        className={styles.container({ className })}
        ref={ref}
        {...props}
        onClick={onPress ? onPress : props.onClick}
      >
        {renderContent()}
      </button>
    );
  }
);

Button.displayName = 'Button';
