import React from 'react';
import { useRouter } from 'next/navigation';
import { IoCloseOutline } from 'react-icons/io5';

import { colors } from '../ui';

interface CloseProps {
  onClick?: () => void;
}

export const Close: React.FC<CloseProps> = ({ onClick }) => {
  const router = useRouter();

  return (
    <button
      className="size-8 flex items-center justify-center"
      onClick={() => {
        if (onClick) {
          onClick();
        } else {
          router.replace('/');
        }
      }}
      aria-label="Close"
      type="button"
    >
      <IoCloseOutline
        size={32}
        color={colors.brand['60']}
        className="text-fg-link"
      />
    </button>
  );
};
