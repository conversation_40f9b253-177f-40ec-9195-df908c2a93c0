'use client';

import React from 'react';
import {
  useController,
  type FieldValues,
  type Control,
  type Path,
  type RegisterOptions,
} from 'react-hook-form';
import * as SelectPrimitive from '@radix-ui/react-select';
import { IoChevronDownOutline } from 'react-icons/io5';

import { tv } from 'tailwind-variants';
import { colors } from '@/components/ui/colors';

const selectTv = tv({
  slots: {
    trigger:
      'flex h-[52px] w-full items-center justify-between rounded-md border border-border-subtle-light dark:border-border-subtle-dark cursor-pointer appearance-none p-3 text-base font-medium transition-colors hover:border-accent-moderate',
    value:
      'text-base text-fg-base-light dark:text-fg-base-dark peer-placeholder-shown:text-base peer-placeholder-shown:text-fg-muted-light dark:peer-placeholder-shown:text-fg-muted-light peer-placeholder-shown:font-medium',
    content:
      'relative z-50 min-w-[8rem] overflow-hidden rounded-md border border-border-subtle-light dark:border-border-subtle-dark bg-white dark:bg-black text-popover-foreground shadow-md',
    item: 'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-gray-100 dark:data-[highlighted]:bg-gray-800 data-[disabled]:opacity-50',
    itemIndicator:
      'absolute left-2 flex h-3.5 w-3.5 items-center justify-center',
    separator: '-mx-1 my-1 h-px bg-muted dark:bg-gray-700',
    icon: 'size-5 text-gray-400',
  },
  variants: {
    hasError: {
      true: {
        trigger: 'border-red-60 dark:border-red-80',
      },
    },
    disabled: {
      true: {
        trigger: 'cursor-not-allowed opacity-50',
      },
    },
  },
});

export type OptionType = { label: string; value: string | number };

interface SelectProps {
  label?: string;
  error?: string;
  options?: OptionType[];
  placeholder?: string;
  disabled?: boolean;
  onSelect?: (value: string | number) => void;
  value?: string | number;
}

export const Select: React.FC<SelectProps> = ({
  label,
  error,
  options = [],
  placeholder = 'Select...',
  disabled,
  onSelect,
  value,
}) => {
  const {
    trigger,
    value: valueStyles,
    content,
    item,
    icon,
  } = selectTv({ hasError: !!error, disabled });

  return (
    <div className="flex flex-col gap-2">
      {label && <label className="text-sm font-medium">{label}</label>}

      <SelectPrimitive.Root
        value={value ? String(value) : ''}
        onValueChange={(val) => onSelect?.(val)}
        disabled={disabled}
      >
        <SelectPrimitive.Trigger className={trigger()}>
          <SelectPrimitive.Value
            placeholder={placeholder}
            className={valueStyles()}
          />
          <SelectPrimitive.Icon className={icon()}>
            <IoChevronDownOutline size={20} color={colors.grey[70]} />
          </SelectPrimitive.Icon>
        </SelectPrimitive.Trigger>
        <SelectPrimitive.Portal>
          <SelectPrimitive.Content
            className={content()}
            position="popper"
            sideOffset={8}
          >
            <SelectPrimitive.Viewport className="max-h-60 overflow-y-auto">
              {options.map((opt) => (
                <SelectPrimitive.Item
                  key={opt.value}
                  value={String(opt.value)}
                  className={item()}
                >
                  <SelectPrimitive.ItemText>
                    {opt.label}
                  </SelectPrimitive.ItemText>
                </SelectPrimitive.Item>
              ))}
            </SelectPrimitive.Viewport>
          </SelectPrimitive.Content>
        </SelectPrimitive.Portal>
      </SelectPrimitive.Root>
      {error && (
        <p className="text-red-500 dark:text-red-600 text-sm mt-1">{error}</p>
      )}
    </div>
  );
};

interface ControlledSelectProps<T extends FieldValues>
  extends Omit<SelectProps, 'value' | 'onSelect' | 'error'> {
  name: Path<T>;
  control: Control<T>;
  rules?: RegisterOptions<T, Path<T>>;
}

export const ControlledSelect = <T extends FieldValues>({
  name,
  control,
  rules,
  ...selectProps
}: ControlledSelectProps<T>) => {
  const { field, fieldState } = useController({ control, name, rules });

  return (
    <Select
      {...selectProps}
      value={field.value}
      onSelect={field.onChange}
      error={fieldState.error?.message}
    />
  );
};
