'use client';

import React from 'react';
import type {
  Control,
  FieldValues,
  Path,
  UseFormSetValue,
} from 'react-hook-form';

import { CostOption } from './cost-option';
import {
  formatAmount,
  formatNumberWithCommas,
  type CreateEventFormType,
} from '@/lib';
import { ControlledInput } from './input';

interface CostSelectorProps<T extends FieldValues = CreateEventFormType> {
  control: Control<T>;
  setValue: UseFormSetValue<T>;
  name?: Path<T>;
  label?: string;
  costOptions?: number[];
  testID?: string;
}

export const CostSelector = <T extends FieldValues = CreateEventFormType>({
  control,
  setValue,
  name = 'cost' as Path<T>,
  label = 'Ticket price',
  costOptions = [0, 10000, 20000, 50000],
}: CostSelectorProps<T>) => {
  return (
    <div className="flex flex-col gap-2">
      <ControlledInput
        control={control}
        name={name}
        label={label}
        onChange={(event) => {
          const value = event.target.value;
          const formatted = formatNumberWithCommas(value);
          setValue(name, formatted as any);
        }}
        inputMode="numeric"
      />

      <div className="flex gap-2 overflow-x-auto">
        {costOptions.map((cost, index) => (
          <CostOption
            key={index}
            option={cost === 0 ? 'Free' : formatAmount(cost)}
            onPress={() =>
              setValue(
                name,
                (cost === 0 ? '0' : formatNumberWithCommas(String(cost))) as any
              )
            }
          />
        ))}
      </div>
    </div>
  );
};
