'use client';
import React, { useState } from 'react';

import { cn } from '@/lib';
import { MdRegularLabel } from '../ui';

import { AiOutlineMinus } from 'react-icons/ai';
import { IoAddOutline } from 'react-icons/io5';

export interface CounterProps {
  value?: number;
  initialValue?: number;
  minimum?: number;
  maximum?: number;
  onValueChange?: (value: number) => void;
  className?: string;
}

export const Counter: React.FC<CounterProps> = ({
  value,
  initialValue = 0,
  minimum = 0,
  maximum = 100,
  onValueChange,
  className,
}) => {
  const [internalCount, setInternalCount] = useState(value ?? initialValue);

  const displayValue = value ?? internalCount;

  const handleDecrement = () => {
    const newValue = displayValue - 1;
    if (newValue >= minimum) {
      if (value === undefined) {
        setInternalCount(newValue); // Only update internal state if uncontrolled
      }
      onValueChange?.(newValue);
    }
  };

  const handleIncrement = () => {
    const newValue = displayValue + 1;
    if (newValue <= maximum) {
      if (value === undefined) {
        setInternalCount(newValue); // Only update internal state if uncontrolled
      }
      onValueChange?.(newValue);
    }
  };

  const isDecrementDisabled = displayValue <= minimum;
  const isIncrementDisabled = displayValue >= maximum;

  return (
    <div
      className={cn(
        'w-[100px] flex flex-row items-center justify-between rounded-full bg-white p-2 dark:bg-gray-800',
        className
      )}
    >
      <button
        onClick={handleDecrement}
        disabled={isDecrementDisabled}
        style={{ opacity: isDecrementDisabled ? 0.2 : 1 }}
        aria-label="Decrement"
        type="button"
      >
        <AiOutlineMinus size={16} color="#4F46E5" />
      </button>

      <MdRegularLabel>{displayValue}</MdRegularLabel>

      <button
        onClick={handleIncrement}
        disabled={isIncrementDisabled}
        style={{ opacity: isIncrementDisabled ? 0.2 : 1 }}
        aria-label="Increment"
        type="button"
      >
        <IoAddOutline size={16} color="#4F46E5" />
      </button>
    </div>
  );
};
