'use client';
import React, { useCallback, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import {
  addDays,
  format,
  isSameYear,
  isToday,
  isTomorrow,
  startOfDay,
  startOfWeek,
  isSameDay,
  subDays,
} from 'date-fns';
import {
  MdBoldLabel,
  SmBoldLabel,
  XsBoldLabel,
} from '@/components/ui/typography';
import { cn } from '@/lib/utils';
import { Image } from '@/components/ui/image';
import { DateTimePicker } from '@/components/ui/date-time-picker';

interface DatePickerProps {
  onSelected?: (v: Date | null) => void;
}

const initDays = (date: Date) => {
  const weekStart = startOfWeek(date);
  const newDays: Date[] = [];
  for (let i = 0; i < 7; i++) {
    newDays.push(addDays(weekStart, i));
  }
  return newDays;
};

export const DatePicker: React.FC<DatePickerProps> = ({ onSelected }) => {
  const [days, setDays] = useState<Date[]>(initDays(new Date()));
  const [currentDate, setCurrentDate] = useState<Date | null>(null);

  const addNewDays = useCallback((date: Date) => {
    setDays(initDays(date));
  }, []);

  const handlePrev = (): void => {
    const dayStart = startOfDay(currentDate ?? new Date());
    const newDate = subDays(dayStart, 1);
    addNewDays(newDate);
    setCurrentDate(newDate);
    onSelected?.(newDate);
  };

  const handleNext = (): void => {
    const dayStart = startOfDay(currentDate ?? new Date());
    const newDate = addDays(dayStart, 1);
    addNewDays(newDate);
    setCurrentDate(newDate);
    onSelected?.(newDate);
  };

  const handleDayClick = (day: Date) => {
    if (currentDate && isSameDay(day, currentDate)) {
      setCurrentDate(null);
      onSelected?.(null);
    } else {
      setCurrentDate(day);
      onSelected?.(day);
    }
  };

  return (
    <div className="w-full max-w-7xl mx-auto">
      <div className="flex items-center justify-between gap-4 p-4">
        <div className="size-8" />
        <div className="flex items-center gap-4">
          <button
            onClick={handlePrev}
            disabled={!currentDate}
            className="p-1 hover:bg-bg-interactive-primary-light dark:hover:bg-bg-interactive-primary-dark rounded-full transition-colors cursor-pointer"
          >
            <ChevronLeft
              size={24}
              className={cn(`text-fg-base-light dark:text-fg-base-dark`, {
                'text-fg-disabled-light dark:text-fg-disabled-dark':
                  !currentDate,
              })}
            />
          </button>
          <div>
            <MdBoldLabel weight="bold" themed>
              {currentDate
                ? isToday(currentDate)
                  ? 'Today'
                  : isTomorrow(currentDate)
                    ? 'Tomorrow'
                    : format(
                        currentDate,
                        `EEE, dd MMMM${
                          isSameYear(currentDate, new Date()) ? '' : ' yyyy'
                        }`
                      )
                : 'Filter by date'}
            </MdBoldLabel>
          </div>
          <button
            onClick={handleNext}
            disabled={!currentDate}
            className="p-1 hover:bg-bg-interactive-primary-light dark:hover:bg-bg-interactive-primary-dark rounded-full transition-colors cursor-pointer"
          >
            <ChevronRight
              size={24}
              className={cn(`text-fg-base-light dark:text-fg-base-dark`, {
                'text-fg-disabled-light dark:text-fg-disabled-dark':
                  !currentDate,
              })}
            />
          </button>
        </div>
        <DateTimePicker
          selected={currentDate || undefined}
          onChange={(date) => {
            if (date instanceof Date) {
              handleDayClick(date);
            }
          }}
          showTimeSelect={false}
          label="Select date"
          customInput={
            <button className="size-8 cursor-pointer">
              <Image
                src={'/svgs/calendar.svg'}
                alt="Calendar"
                width={32}
                height={32}
              />
            </button>
          }
        />
      </div>

      <div className="flex flex-col gap-2 px-2 md:px-5 py-2">
        <div className="flex justify-between gap-3">
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
            <div
              key={index}
              className="h-10 flex-1 flex items-center justify-center"
            >
              <XsBoldLabel themed weight="bold">
                {day}
              </XsBoldLabel>
            </div>
          ))}
        </div>

        <div className="flex justify-between gap-3">
          {days.map((day, index) => {
            const isSelected = currentDate && isSameDay(day, currentDate);
            return (
              <button
                key={index}
                onClick={() => handleDayClick(day)}
                className={`h-10 flex-1 flex items-center justify-center rounded-full transition-colors cursor-pointer ${
                  isSelected
                    ? 'bg-brand-60'
                    : 'bg-transparent hover:bg-brand-10 dark:hover:bg-brand-80'
                }`}
              >
                <SmBoldLabel
                  weight="bold"
                  className={cn(
                    isSelected
                      ? 'text-white'
                      : 'text-fg-disabled-light dark:text-fg-disabled-dark'
                  )}
                >
                  {format(day, 'd')}
                </SmBoldLabel>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};
