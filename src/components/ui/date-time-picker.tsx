'use client';
import React from 'react';
import DatePicker from 'react-datepicker';
import { InputPickerButton } from './picker-input';

interface DateTimePickerProps {
  selected?: Date;
  onChange?: (date: Date) => void;
  minDate?: Date;
  showTimeSelect?: boolean;
  maxDate?: Date;
  calendarClassName?: string;
  popperClassName?: string;
  customInput?: React.ReactElement | undefined;
  label?: string;
  withPortal?: boolean;
}

export const DateTimePicker: React.FC<DateTimePickerProps> = ({
  selected,
  onChange,
  minDate,
  label = 'Select date and time',
  maxDate,
  customInput = <InputPickerButton value={selected} label={label} />,
  showTimeSelect = true,
  withPortal = false,
}) => {
  return (
    <DatePicker
      selected={selected}
      onChange={(date) => {
        if (onChange && date instanceof Date) {
          onChange(date);
        }
      }}
      showTimeSelect={showTimeSelect}
      timeFormat="HH:mm"
      timeIntervals={15}
      timeCaption="Time"
      dateFormat="MMMM d, yyyy h:mm aa"
      minDate={minDate}
      maxDate={maxDate}
      customInput={customInput ?? undefined}
      popperPlacement="bottom-end"
      portalId="modal"
      withPortal={withPortal}
    />
  );
};
