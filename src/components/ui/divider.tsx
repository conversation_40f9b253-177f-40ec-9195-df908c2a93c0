'use client';
import React from 'react';
import { cn } from '@/lib';

interface DividerProps {
  className?: string;
  color?: string;
  thickness?: number;
}

export const Divider: React.FC<DividerProps> = ({
  className,
  color = 'bg-grey-20 dark:bg-grey-70',
  thickness = 1,
}) => {
  return (
    <div
      className={cn('w-full mb-2', color, className)}
      style={{ height: thickness }}
    />
  );
};
