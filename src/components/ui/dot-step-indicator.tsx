'use client';

import React, { useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';

import { cn } from '@/lib';

export interface DotStepIndicatorProps {
  activeStep: number;
  totalSteps: number;
  activeStepClassName?: string;
  hasOneActiveStep?: boolean;
}

export const DotStepIndicator: React.FC<DotStepIndicatorProps> = ({
  activeStep,
  totalSteps,
  activeStepClassName = '',
  hasOneActiveStep,
}) => {
  const controls = useAnimation();

  useEffect(() => {
    if (activeStep === 1) {
      controls.start({
        scale: [1, 1.2, 1],
        width: ['8px', '32px', '8px'],
        transition: { duration: 0.6 },
      });
    } else {
      controls.start({
        scale: 1,
        width: '8px',
        transition: { duration: 0.3 },
      });
    }
  }, [activeStep, controls]);

  const steps = Array.from({ length: totalSteps }, (_, i) => i + 1);

  return (
    <div className="flex flex-row gap-2">
      {steps.map((step) => {
        const isActive = step === activeStep;
        const isCompleted = step <= activeStep;

        const baseClass = 'rounded-full';
        const indicatorClass = hasOneActiveStep
          ? isActive
            ? `bg-accent-moderate dark:bg-accent-moderate ${activeStepClassName}`
            : 'bg-grey-20 dark:bg-grey-80'
          : isCompleted
            ? `bg-accent-moderate dark:bg-accent-moderate ${activeStepClassName}`
            : 'bg-grey-20 dark:bg-grey-80';

        if (isActive) {
          return (
            <motion.div
              key={step}
              className={cn(baseClass, indicatorClass)}
              style={{ height: 8 }}
              animate={controls}
              initial={{ scale: 1, width: 8 }}
            />
          );
        }

        return (
          <div
            key={step}
            className={cn(baseClass, indicatorClass)}
            style={{ width: 8, height: 8 }}
          />
        );
      })}
    </div>
  );
};
