'use client';
import React from 'react';
import { useTheme } from 'next-themes';

import { DarkEmptyIcon, LightEmptyIcon, Tiny } from '@/components/ui';

interface EmptyStateProps {
  text?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ text }) => {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  return (
    <div className="flex-1 flex flex-col items-center justify-center gap-3.5">
      {isDark ? <DarkEmptyIcon /> : <LightEmptyIcon />}
      <Tiny className="text-gray-500 dark:text-gray-400">
        {text || 'There is nothing here'}
      </Tiny>
    </div>
  );
};
