import * as React from 'react';
import { cn } from '@/lib/utils';

export interface FloatingInputProps extends React.ComponentProps<'input'> {
  label: string;
  containerClassName?: string;
  labelClassName?: string;
  prefixIcon?: React.ReactNode;
  postIcon?: React.ReactNode;
}

export const FloatingInput = React.forwardRef<
  HTMLInputElement,
  FloatingInputProps
>(
  (
    {
      className,
      containerClassName,
      labelClassName,
      prefixIcon,
      postIcon,
      type,
      label,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = React.useId();
    const finalId = id || `floating-input-${inputId}`;

    return (
      <div className={cn('relative flex-1 group', containerClassName)}>
        {prefixIcon && (
          <div className="absolute left-0 p-3 flex items-center justify-center text-fg-muted-light dark:text-fg-muted-dark group-focus-within:text-primary">
            {prefixIcon}
          </div>
        )}
        {postIcon && (
          <div className="absolute right-0 p-3 flex items-center justify-center text-fg-muted-light dark:text-fg-muted-dark group-focus-within:text-primary">
            {postIcon}
          </div>
        )}
        <input
          type={type}
          id={finalId}
          className={cn(
            'block h-12 w-full rounded-lg border border-border-subtle-light dark:border-border-subtle-dark bg-transparent! autofill:shadow-[inset_0_0_0px_1000px_transparent] p-4 text-sm text-fg-base-light dark:text-fg-base-dark placeholder-transparent focus:border-2 focus:border-primary dark:focus:border-primary focus:outline-hidden focus:ring-0 focus:shadow-[0_0_0_4px_rgba(114,87,255,0.3)] disabled:cursor-not-allowed disabled:opacity-50 peer',
            prefixIcon && 'pl-10',
            postIcon && 'pr-10',
            className
          )}
          placeholder=" "
          ref={ref}
          {...props}
        />
        <label
          htmlFor={finalId}
          className={cn(
            'absolute start-4 top-3.5 z-10 origin-[0] -translate-y-3.5 scale-75 transform text-sm text-fg-subtle-light dark:text-fg-subtle-dark duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:text-primary peer-focus:rtl:left-auto peer-focus:rtl:translate-x-1/4',
            labelClassName
          )}
        >
          {label}
        </label>
      </div>
    );
  }
);

FloatingInput.displayName = 'FloatingInput';
