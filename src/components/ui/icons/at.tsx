import React from 'react';

interface AtIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const AtIcon: React.FC<AtIconProps> = ({ color = '#7257FF', ...props }) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <path
      d="M15.2 8.7995V12.7995C15.2 13.436 15.4529 14.0465 15.9029 14.4966C16.353 14.9466 16.9635 15.1995 17.6 15.1995C18.2365 15.1995 18.847 14.9466 19.2971 14.4966C19.7471 14.0465 20 13.436 20 12.7995V11.9995C19.9999 10.1939 19.389 8.44148 18.2666 7.02713C17.1443 5.61277 15.5764 4.61968 13.8181 4.20934C12.0598 3.799 10.2144 3.99554 8.58188 4.767C6.94942 5.53846 5.62592 6.83947 4.82661 8.45848C4.0273 10.0775 3.79918 11.9193 4.17934 13.6844C4.5595 15.4495 5.52559 17.0341 6.92051 18.1805C8.31543 19.3269 10.0572 19.9678 11.8625 19.9988C13.6678 20.0299 15.4305 19.4493 16.864 18.3515M15.2 11.9995C15.2 13.7668 13.7673 15.1995 12 15.1995C10.2327 15.1995 8.80001 13.7668 8.80001 11.9995C8.80001 10.2322 10.2327 8.7995 12 8.7995C13.7673 8.7995 15.2 10.2322 15.2 11.9995Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default AtIcon;
