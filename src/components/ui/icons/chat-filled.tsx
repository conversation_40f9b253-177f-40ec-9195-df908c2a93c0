import React from 'react';

interface ChatFilledIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  fill?: string;
}

const ChatFilledIcon: React.FC<ChatFilledIconProps> = ({
  color = '#7257FF',
  fill = '#7257FF',
  ...props
}) => (
  <svg width={25} height={24} viewBox="0 0 25 24" fill="none" {...props}>
    <path
      d="M16.7998 11.75C17.062 11.75 17.3384 11.7749 17.6494 11.8291V11.8301C17.9883 11.8996 18.3355 12.0218 18.6914 12.1943V12.1953C19.3237 12.5194 19.8552 12.9722 20.2529 13.5225L20.415 13.7637C20.8281 14.4481 21.0498 15.2173 21.0498 16C21.0498 16.5349 20.9422 17.0828 20.7441 17.5869H20.7451C20.6768 17.7413 20.654 17.9024 20.6729 18.0547V18.0557L20.7432 18.6758V18.6807C20.7835 19.01 20.6632 19.3592 20.4062 19.6162C20.1594 19.863 19.8142 19.9756 19.4707 19.9336L19.4531 19.9316L18.873 19.8818H18.8721C18.7072 19.8579 18.5457 19.8759 18.3867 19.9463V19.9443C17.8826 20.1424 17.3347 20.25 16.7998 20.25C16.0149 20.25 15.2449 20.0274 14.5713 19.6143C13.9036 19.2047 13.3654 18.6147 12.9951 17.9023C12.8693 17.6426 12.7727 17.3775 12.7021 17.1143L12.6396 16.8496L12.6387 16.8467L12.5986 16.627C12.5648 16.4103 12.5498 16.2 12.5498 16V15.7676C12.6074 14.71 13.0435 13.734 13.7812 12.9854C14.533 12.2427 15.5131 11.8064 16.5332 11.75H16.7998ZM11.2998 3.75H11.665C13.2615 3.84004 14.7225 4.45882 15.8623 5.49902L16.0859 5.71387C17.0641 6.69205 17.7207 7.97059 17.957 9.3457C17.5592 9.2855 17.1822 9.25 16.7998 9.25H16.4268L16.4131 9.25098C14.8708 9.33226 13.408 9.94645 12.2383 11.0068L12.0078 11.2246L12.0049 11.2285C10.8255 12.4187 10.1367 13.9961 10.0508 15.7354L10.0498 15.748V16C10.0498 16.3822 10.083 16.7674 10.1523 17.1494C9.68456 17.0722 9.22392 16.941 8.78125 16.7578H8.78027C8.59501 16.6795 8.28021 16.5923 7.94531 16.6436V16.6426L6.48535 16.8027C6.07383 16.8465 5.67499 16.7072 5.38379 16.416C5.12961 16.1618 4.99216 15.8267 4.99609 15.4668L5.00586 15.3105L5.00684 15.2998L5.14746 13.8994C5.20186 13.5598 5.1469 13.2485 5.03125 13.0088C4.71487 12.1961 4.5498 11.3605 4.5498 10.5C4.5498 9.25208 4.89486 8.02351 5.55566 6.95312L5.55469 6.95215C6.1844 5.93805 7.05052 5.12342 8.06641 4.57422L8.27148 4.46777L8.27539 4.4668C9.21471 3.99249 10.2372 3.75 11.2998 3.75Z"
      fill={fill}
      stroke={color}
    />
  </svg>
);

export default ChatFilledIcon;
