import React from 'react';

interface ChevronLeftProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const ChevronLeft: React.FC<ChevronLeftProps> = ({
  color = '#7257FF',
  ...props
}) => (
  <svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
    <path
      d="M20.5 24L12.9195 16.9428C12.3602 16.4221 12.3602 15.5779 12.9195 15.0572L20.5 8"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
    />
  </svg>
);

export default ChevronLeft;
