import React from 'react';

interface DarkEmptyIconProps extends React.SVGProps<SVGSVGElement> {
  strokeColor?: string;
}

const DarkEmptyIcon: React.FC<DarkEmptyIconProps> = ({
  strokeColor = '#53575A',
  ...props
}) => (
  <svg width="153" height="121" viewBox="0 0 153 121" fill="none" {...props}>
    <path
      d="M22.3037 23.0879C27.8493 22.7039 31.845 25.0495 36.2129 28.2617L37.0918 28.915C44.1174 34.2143 51.8499 38.7959 60.5732 41.3467L61.4199 41.5869C70.476 44.0793 80.1393 44.515 89.6055 44.459L89.6045 44.458C91.2206 44.4524 92.8452 44.4308 94.4531 44.4014L99.2031 44.3018C101.361 44.2535 103.687 43.9866 106.056 43.7031C108.433 43.4185 110.858 43.1175 113.25 42.9922C118.039 42.7414 122.586 43.2035 126.21 45.8223C129.025 48.0668 130.918 51.2501 131.536 54.7764L131.537 54.7861L131.539 54.7959C132.841 60.916 132.765 68.5299 131.028 75.6045C129.345 82.4633 126.119 88.7488 121.149 92.6943L120.663 93.0684L120.662 93.0693C114.921 97.3636 107.765 99.1659 100.322 99.6318C93.345 100.069 86.1563 99.329 79.7158 98.3975L78.4375 98.208C63.5133 95.9711 48.6522 92.1249 35.0508 85.6035L33.7383 84.9639C27.6948 81.9713 21.135 78.1892 15.5088 73.5146C10.0563 68.9844 5.50851 63.6395 3.1377 57.3926L2.91504 56.7861C1.37581 52.4299 0.320347 47.1712 0.605469 42.1846C0.89051 37.2001 2.5104 32.5394 6.26367 29.2812L6.2627 29.2803C10.6552 25.6811 16.0678 23.5314 21.7441 23.1221L22.2949 23.0879H22.3037Z"
      fill="#2F3133"
      stroke={strokeColor}
      strokeWidth={1.11826}
    />
    <path
      d="M43.5098 0.5V5.3085"
      stroke={strokeColor}
      strokeWidth={0.950518}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M41.084 2.9043H45.9348"
      stroke={strokeColor}
      strokeWidth={0.950518}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M135.538 96.3457V101.154"
      stroke={strokeColor}
      strokeWidth={0.950518}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M133.116 98.75H137.967"
      stroke={strokeColor}
      strokeWidth={0.950518}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.85547 73.9473C7.16182 73.9473 7.40224 74.1916 7.40234 74.4834C7.40234 74.7752 7.16187 75.0205 6.85547 75.0205C6.5491 75.0205 6.30957 74.7752 6.30957 74.4834C6.30967 74.1917 6.54916 73.9473 6.85547 73.9473Z"
      fill="#53575A"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M100.985 3.83301C101.291 3.83301 101.532 4.07739 101.532 4.36914C101.532 4.66097 101.291 4.90625 100.985 4.90625C100.678 4.90621 100.439 4.66096 100.439 4.36914C100.439 4.07741 100.679 3.83304 100.985 3.83301Z"
      fill="#53575A"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M70.3467 115.96C81.7717 115.96 92.1104 116.245 99.5879 116.705C103.329 116.935 106.342 117.209 108.414 117.511C109.401 117.654 110.154 117.804 110.665 117.95C110.154 118.096 109.401 118.245 108.414 118.389C106.342 118.69 103.329 118.964 99.5879 119.194C92.1104 119.655 81.7717 119.94 70.3467 119.94C58.9217 119.94 48.583 119.655 41.1055 119.194C37.3647 118.964 34.3513 118.69 32.2793 118.389C31.2919 118.245 30.5385 118.096 30.0273 117.95C30.5386 117.804 31.2918 117.655 32.2793 117.511C34.3513 117.209 37.3647 116.935 41.1055 116.705C48.583 116.245 58.9217 115.96 70.3467 115.96Z"
      fill="#2F3133"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M38.4529 13.5605H98.5808C100.077 13.5605 101.511 14.1496 102.569 15.1982C103.627 16.2468 104.221 17.6689 104.221 19.1518V90.0605C104.221 91.5434 103.627 92.9655 102.569 94.0141C101.511 95.0627 100.077 95.6517 98.5808 95.6517H30.7479C29.252 95.6517 27.8173 95.0627 26.7595 94.0141C25.7017 92.9655 25.1074 91.5434 25.1074 90.0605V26.9237L38.4529 13.5605Z"
      fill="#1F2224"
      stroke={strokeColor}
      strokeWidth="1.11826"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M103.391 48.6748V95.7754H42.5088V48.6748H103.391Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M25.1074 26.9237H36.0387C36.6801 26.9208 37.2941 26.6661 37.7465 26.2156C38.1989 25.765 38.4529 25.1552 38.4529 24.5195V13.5605L25.1074 26.9237Z"
      fill="#1F2224"
      stroke={strokeColor}
      strokeWidth="1.11826"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M103.646 32.7168H42.3226C39.9364 32.7168 38.002 34.6343 38.002 36.9997V50.005C38.002 52.3704 39.9364 54.288 42.3226 54.288H103.646C106.033 54.288 107.967 52.3704 107.967 50.005V36.9997C107.967 34.6343 106.033 32.7168 103.646 32.7168Z"
      fill="#1F2224"
      stroke={strokeColor}
      strokeWidth="1.11826"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M49.9141 40.3555C51.6908 40.3555 53.123 41.7819 53.123 43.5312C53.1228 45.2804 51.6907 46.707 49.9141 46.707C48.1376 46.7069 46.7063 45.2803 46.7061 43.5312C46.7061 41.782 48.1374 40.3556 49.9141 40.3555Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M61.7622 40.3555C63.539 40.3555 64.9712 41.7819 64.9712 43.5312C64.971 45.2804 63.5389 46.707 61.7622 46.707C59.9857 46.7069 58.5544 45.2803 58.5542 43.5312C58.5542 41.782 59.9856 40.3556 61.7622 40.3555Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M73.5957 40.3555C75.3725 40.3555 76.8047 41.7819 76.8047 43.5312C76.8045 45.2804 75.3723 46.707 73.5957 46.707C71.8192 46.7069 70.3879 45.2803 70.3877 43.5312C70.3877 41.782 71.8191 40.3556 73.5957 40.3555Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M103.646 56.625H42.3226C39.9364 56.625 38.002 58.5425 38.002 60.9079V73.9132C38.002 76.2786 39.9364 78.1962 42.3226 78.1962H103.646C106.033 78.1962 107.967 76.2786 107.967 73.9132V60.9079C107.967 58.5425 106.033 56.625 103.646 56.625Z"
      fill="#1F2224"
      stroke={strokeColor}
      strokeWidth="1.11826"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M49.9141 64.2744C51.6908 64.2744 53.123 65.7008 53.123 67.4502C53.1228 69.1994 51.6907 70.626 49.9141 70.626C48.1376 70.6258 46.7063 69.1993 46.7061 67.4502C46.7061 65.7009 48.1374 64.2746 49.9141 64.2744Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M61.7622 64.2744C63.539 64.2744 64.9712 65.7008 64.9712 67.4502C64.971 69.1994 63.5388 70.626 61.7622 70.626C59.9857 70.6258 58.5544 69.1993 58.5542 67.4502C58.5542 65.7009 59.9856 64.2746 61.7622 64.2744Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M73.5957 64.2744C75.3725 64.2744 76.8047 65.7008 76.8047 67.4502C76.8045 69.1994 75.3723 70.626 73.5957 70.626C71.8192 70.6258 70.3879 69.1993 70.3877 67.4502C70.3877 65.7009 71.8191 64.2746 73.5957 64.2744Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M103.646 80.5449H42.3226C39.9364 80.5449 38.002 82.4625 38.002 84.8278V97.8332C38.002 100.199 39.9364 102.116 42.3226 102.116H103.646C106.033 102.116 107.967 100.199 107.967 97.8332V84.8278C107.967 82.4625 106.033 80.5449 103.646 80.5449Z"
      fill="#1F2224"
      stroke={strokeColor}
      strokeWidth="1.11826"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M49.9141 88.1826C51.6908 88.1826 53.123 89.609 53.123 91.3584C53.1228 93.1076 51.6907 94.5342 49.9141 94.5342C48.1376 94.534 46.7063 93.1075 46.7061 91.3584C46.7061 89.6091 48.1374 88.1828 49.9141 88.1826Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M61.7622 88.1826C63.539 88.1826 64.9712 89.609 64.9712 91.3584C64.971 93.1076 63.5389 94.5342 61.7622 94.5342C59.9857 94.534 58.5544 93.1075 58.5542 91.3584C58.5542 89.6091 59.9856 88.1828 61.7622 88.1826Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M73.5957 88.1826C75.3725 88.1826 76.8047 89.609 76.8047 91.3584C76.8045 93.1076 75.3723 94.5342 73.5957 94.5342C71.8192 94.534 70.3879 93.1075 70.3877 91.3584C70.3877 89.6091 71.8191 88.1828 73.5957 88.1826Z"
      fill="#6E7375"
      stroke={strokeColor}
      strokeWidth="1.11826"
    />
    <path
      d="M109.202 59.9576C122.118 59.9576 132.588 49.5789 132.588 36.7762C132.588 23.9734 122.118 13.5947 109.202 13.5947C96.2865 13.5947 85.8164 23.9734 85.8164 36.7762C85.8164 49.5789 96.2865 59.9576 109.202 59.9576Z"
      fill="#1F2224"
      stroke={strokeColor}
      strokeWidth="1.11826"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M125.805 53.5947L132.032 59.7675"
      stroke={strokeColor}
      strokeWidth="1.11826"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M131.332 56.7707L129.399 58.7754C128.529 59.6772 128.562 61.107 129.471 61.9691L146.27 77.8872C147.179 78.7493 148.622 78.7171 149.491 77.8153L151.425 75.8107C152.294 74.9089 152.262 73.479 151.352 72.617L134.554 56.6988C133.644 55.8368 132.202 55.8689 131.332 56.7707Z"
      fill="#1F2224"
      stroke={strokeColor}
      strokeWidth="1.11826"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default DarkEmptyIcon;
