import React from 'react';

interface DeleteIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const DeleteIcon: React.FC<DeleteIconProps> = ({
  color = '#DB340B',
  ...props
}) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <path
      d="M10.0016 11.5L10.446 16.1667M14.0016 11.5L13.5571 16.1667M4.35156 7.92453H6.16283M6.16283 7.92453C10.8786 7.92453 19.6353 7.92453 19.6353 7.92453M6.16283 7.92453L6.80765 17.2079C6.91686 18.7803 8.22426 20 9.80044 20H14.2249C15.7919 20 17.095 18.7939 17.2159 17.2316L17.9365 7.92453C17.9365 7.92453 10.2891 7.92453 6.16283 7.92453ZM8.87986 7.92453V6C8.87986 4.89543 9.77529 4 10.8799 4H13.2195C14.3241 4 15.2195 4.89543 15.2195 6V7.92453"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default DeleteIcon;
