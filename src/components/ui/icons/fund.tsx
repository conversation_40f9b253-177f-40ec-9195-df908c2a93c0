import React from 'react';

interface FundIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const FundIcon: React.FC<FundIconProps> = ({ color = '#B4A6FF', ...props }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" {...props}>
    <path
      d="M10.0003 8.33414C10.0003 8.51823 9.85109 8.66747 9.66699 8.66747C9.4829 8.66747 9.33366 8.51823 9.33366 8.33414C9.33366 8.15004 9.4829 8.00081 9.66699 8.00081C9.85109 8.00081 10.0003 8.15004 10.0003 8.33414Z"
      fill="white"
    />
    <path
      d="M10.3337 4.33414H3.66699C2.56242 4.33414 1.66699 5.22957 1.66699 6.33414M10.3337 4.33414C11.4382 4.33414 12.3337 5.22957 12.3337 6.33414V10.3341C12.3337 11.4387 11.4382 12.3341 10.3337 12.3341H3.66699C2.56242 12.3341 1.66699 11.4387 1.66699 10.3341V6.33414M10.3337 4.33414V3.61048C10.3337 2.29444 9.08451 1.33712 7.81371 1.67925L4.56384 2.55422C2.85471 3.01437 1.66699 4.56415 1.66699 6.33414M10.0003 8.33414C10.0003 8.51823 9.85109 8.66747 9.66699 8.66747C9.4829 8.66747 9.33366 8.51823 9.33366 8.33414C9.33366 8.15004 9.4829 8.00081 9.66699 8.00081C9.85109 8.00081 10.0003 8.15004 10.0003 8.33414Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinejoin="round"
    />
  </svg>
);

export default FundIcon;
