import React from 'react';

interface GraphChartProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const GraphChart: React.FC<GraphChartProps> = ({
  color = '#B4A6FF',
  ...props
}) => (
  <svg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
    <path
      d="M2.66797 2.66602V10.666C2.66797 12.1388 3.86188 13.3327 5.33464 13.3327H13.3346M5.33464 10.666V8.66602M10.668 10.666V6.66602M8.0013 10.666V4.66602"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default GraphChart;
