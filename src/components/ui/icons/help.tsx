import React from 'react';

interface HelpIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const HelpIcon: React.FC<HelpIconProps> = ({ color = 'white', ...props }) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <path
      d="M12 13.7374C12 12.873 12.484 12.1252 13.1812 11.6141C13.6776 11.2502 14 10.6628 14 10C14 8.89543 13.1046 8 12 8C10.8954 8 10 8.89543 10 10M12 4C7.58173 4 4 7.58173 4 12C4 14.15 4.8526 16.0976 6.23248 17.5351L5 20H12C16.4183 20 20 16.4183 20 12C20 7.58173 16.4183 4 12 4Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 16.5918C12.2761 16.5918 12.5 16.3679 12.5 16.0918C12.5 15.8157 12.2761 15.5918 12 15.5918C11.7239 15.5918 11.5 15.8157 11.5 16.0918C11.5 16.3679 11.7239 16.5918 12 16.5918Z"
      fill={color}
      stroke={color}
      strokeWidth={0.5}
      strokeMiterlimit={10}
    />
  </svg>
);

export default HelpIcon;
