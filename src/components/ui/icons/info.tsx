import React from 'react';

interface InfoIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const InfoIcon: React.FC<InfoIconProps> = ({ color = 'white', ...props }) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <path
      d="M12 15.2V11.7542M12 8.5C12 8.5 12.0049 8.5 12.008 8.5M20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default InfoIcon;
