import React from 'react';

interface LocationIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  strokeColor?: string;
  width?: number | string;
  height?: number | string;
}

const LocationIcon: React.FC<LocationIconProps> = ({
  color = '#7257FF',
  strokeColor = '#7257FF',
  width = 20,
  height = 21,
  ...props
}) => (
  <svg
    width={width}
    height={height}
    viewBox={`0 0 ${width} ${height}`}
    fill="none"
    {...props}
  >
    <path
      d="M10.0002 3.73047C11.5503 3.73047 13.0067 4.33571 14.105 5.43402C15.2026 6.53163 15.8002 7.97934 15.8002 9.53047C15.8002 11.0342 15.1511 12.482 14.2623 13.7534C13.3764 15.0205 12.2803 16.072 11.453 16.7731C11.0245 17.1322 10.513 17.3138 10.0002 17.3138C9.48758 17.3138 8.97632 17.1323 8.54796 16.7736C7.71999 16.0678 6.62395 15.0142 5.7381 13.7471C4.84904 12.4754 4.2002 11.0298 4.2002 9.53047C4.2002 7.97934 4.7978 6.53163 5.89542 5.43402C6.99373 4.33571 8.45006 3.73047 10.0002 3.73047ZM6.98353 9.53047C6.98353 11.1961 8.33024 12.5555 10.0002 12.5555C11.6712 12.5555 13.0169 11.1867 13.0169 9.53047C13.0169 7.87207 11.6691 6.5138 10.0002 6.5138C8.33239 6.5138 6.98353 7.86266 6.98353 9.53047ZM10.7669 9.53047C10.7669 9.9586 10.4198 10.3055 10.0002 10.3055C9.58059 10.3055 9.23353 9.9586 9.23353 9.53047C9.23353 9.10661 9.57634 8.7638 10.0002 8.7638C10.4219 8.7638 10.7669 9.1128 10.7669 9.53047Z"
      fill={color}
      stroke={strokeColor}
    />
  </svg>
);

export default LocationIcon;
