import React from 'react';

interface MapIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  fill?: string;
}

const MapIcon: React.FC<MapIconProps> = ({
  color = '#B4A6FF',
  fill = 'white',
  style,
  ...props
}) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      {...props}
      style={{
        ...style,
      }}
    >
      <path
        d="M11.3332 10.3327C11.3332 10.5168 11.1839 10.666 10.9998 10.666C10.8157 10.666 10.6665 10.5168 10.6665 10.3327C10.6665 10.1486 10.8157 9.99935 10.9998 9.99935C11.1839 9.99935 11.3332 10.1486 11.3332 10.3327Z"
        fill={fill}
      />
      <path
        d="M10.9998 13.3327L10.6277 13.9839C10.8583 14.1156 11.1414 14.1156 11.3719 13.9839L10.9998 13.3327ZM7.99984 14.0827C8.41405 14.0827 8.74984 13.7469 8.74984 13.3327C8.74984 12.9185 8.41405 12.5827 7.99984 12.5827V14.0827ZM12.5832 6.66602C12.5832 7.08023 12.919 7.41602 13.3332 7.41602C13.7474 7.41602 14.0832 7.08023 14.0832 6.66602H12.5832ZM12.1968 3.52968C12.4897 3.23679 12.4897 2.76191 12.1968 2.46902C11.9039 2.17613 11.4291 2.17613 11.1362 2.46902L12.1968 3.52968ZM4.80284 5.86301C5.09574 6.15591 5.57061 6.15591 5.8635 5.86301C6.1564 5.57012 6.1564 5.09525 5.8635 4.80235L4.80284 5.86301ZM13.3332 10.3327H12.5832C12.5832 10.833 12.2116 11.3924 11.6562 11.9081C11.398 12.1478 11.1365 12.3442 10.9383 12.4811C10.8399 12.5491 10.7588 12.6012 10.7038 12.6354C10.6763 12.6525 10.6555 12.6651 10.6424 12.6728C10.6359 12.6767 10.6313 12.6794 10.6288 12.6809C10.6276 12.6816 10.6268 12.682 10.6267 12.6821C10.6266 12.6822 10.6266 12.6821 10.6268 12.682C10.6269 12.682 10.627 12.6819 10.6272 12.6818C10.6272 12.6818 10.6274 12.6817 10.6274 12.6817C10.6276 12.6816 10.6277 12.6815 10.9998 13.3327C11.3719 13.9839 11.3721 13.9838 11.3723 13.9837C11.3724 13.9836 11.3726 13.9835 11.3727 13.9834C11.373 13.9832 11.3734 13.983 11.3737 13.9828C11.3745 13.9824 11.3754 13.9819 11.3764 13.9813C11.3785 13.9801 11.3811 13.9786 11.3842 13.9767C11.3906 13.9731 11.3991 13.9681 11.4097 13.9618C11.4308 13.9492 11.4601 13.9315 11.4964 13.9089C11.569 13.8637 11.6702 13.7986 11.7906 13.7155C12.0298 13.5503 12.3517 13.3092 12.6768 13.0073C13.2881 12.4397 14.0832 11.4991 14.0832 10.3327H13.3332ZM10.9998 13.3327C11.3719 12.6815 11.3721 12.6816 11.3723 12.6817C11.3723 12.6817 11.3724 12.6818 11.3725 12.6818C11.3727 12.6819 11.3728 12.682 11.3729 12.682C11.3731 12.6821 11.3731 12.6822 11.373 12.6821C11.3728 12.682 11.3721 12.6816 11.3709 12.6809C11.3684 12.6794 11.3638 12.6767 11.3572 12.6728C11.3442 12.6651 11.3234 12.6525 11.2959 12.6354C11.2409 12.6012 11.1598 12.5491 11.0614 12.4811C10.8631 12.3442 10.6017 12.1478 10.3435 11.9081C9.78812 11.3924 9.41651 10.833 9.41651 10.3327H8.66651H7.91651C7.91651 11.4991 8.71155 12.4397 9.32283 13.0073C9.64798 13.3092 9.96988 13.5503 10.2091 13.7155C10.3294 13.7986 10.4306 13.8637 10.5032 13.9089C10.5396 13.9315 10.5689 13.9492 10.59 13.9618C10.6006 13.9681 10.6091 13.9731 10.6154 13.9767C10.6186 13.9786 10.6212 13.9801 10.6233 13.9813C10.6243 13.9819 10.6252 13.9824 10.6259 13.9828C10.6263 13.983 10.6266 13.9832 10.6269 13.9834C10.6271 13.9835 10.6273 13.9836 10.6274 13.9837C10.6276 13.9838 10.6277 13.9839 10.9998 13.3327ZM8.66651 10.3327H9.41651C9.41651 9.45823 10.1254 8.74935 10.9998 8.74935V7.99935V7.24935C9.29696 7.24935 7.91651 8.6298 7.91651 10.3327H8.66651ZM10.9998 7.99935V8.74935C11.8743 8.74935 12.5832 9.45823 12.5832 10.3327H13.3332H14.0832C14.0832 8.6298 12.7027 7.24935 10.9998 7.24935V7.99935ZM4.66651 2.66602V3.41602H11.3332V2.66602V1.91602H4.66651V2.66602ZM2.66651 11.3327H3.41651V4.66602H2.66651H1.91651V11.3327H2.66651ZM7.99984 13.3327V12.5827H4.66651V13.3327V14.0827H7.99984V13.3327ZM13.3332 4.66602H12.5832V6.66602H13.3332H14.0832V4.66602H13.3332ZM2.66651 11.3327H1.91651C1.91651 12.8515 3.14772 14.0827 4.66651 14.0827V13.3327V12.5827C3.97615 12.5827 3.41651 12.023 3.41651 11.3327H2.66651ZM11.3332 2.66602V3.41602C12.0235 3.41602 12.5832 3.97566 12.5832 4.66602H13.3332H14.0832C14.0832 3.14723 12.852 1.91602 11.3332 1.91602V2.66602ZM4.66651 2.66602V1.91602C3.14772 1.91602 1.91651 3.14723 1.91651 4.66602H2.66651H3.41651C3.41651 3.97566 3.97615 3.41602 4.66651 3.41602V2.66602ZM11.6665 2.99935L11.1362 2.46902L2.13617 11.469L2.6665 11.9993L3.19683 12.5297L12.1968 3.52968L11.6665 2.99935ZM5.33317 5.33268L5.8635 4.80235L3.8635 2.80235L3.33317 3.33268L2.80284 3.86301L4.80284 5.86301L5.33317 5.33268ZM11.3332 10.3327H10.5832C10.5832 10.1026 10.7697 9.91602 10.9998 9.91602V10.666V11.416C11.5981 11.416 12.0832 10.931 12.0832 10.3327H11.3332ZM10.9998 10.666V9.91602C11.23 9.91602 11.4165 10.1026 11.4165 10.3327H10.6665H9.91651C9.91651 10.931 10.4015 11.416 10.9998 11.416V10.666ZM10.6665 10.3327H11.4165C11.4165 10.5628 11.23 10.7493 10.9998 10.7493V9.99935V9.24935C10.4015 9.24935 9.91651 9.73437 9.91651 10.3327H10.6665ZM10.9998 9.99935V10.7493C10.7697 10.7493 10.5832 10.5628 10.5832 10.3327H11.3332H12.0832C12.0832 9.73437 11.5981 9.24935 10.9998 9.24935V9.99935ZM8.66651 2.66602L8.13618 2.13569L2.13618 8.13569L2.66651 8.66602L3.19684 9.19635L9.19684 3.19635L8.66651 2.66602Z"
        fill={color}
      />
      {/* Keep adding the rest of the <path> elements similarly */}
    </svg>
  );
};

export default MapIcon;
