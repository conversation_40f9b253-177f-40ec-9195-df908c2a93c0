import React from 'react';

interface MessageDotCircleProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  width?: number | string;
  height?: number | string;
}

const MessageDotCircle: React.FC<MessageDotCircleProps> = ({
  color = '#131214',
  width = 24,
  height = 24,
  ...props
}) => (
  <svg
    width={width}
    height={height}
    viewBox={`0 0 ${width} ${height}`}
    fill="none"
    {...props}
  >
    <path
      d="M18.2 5.79C16.68 4.27 14.66 3.37 12.47 3.25H11.98C10.63 3.25 9.29 3.57 8.09 4.18C6.63 4.9 5.41 6.02 4.56 7.4C3.7 8.78 3.25 10.37 3.25 12C3.25 13.12 3.46 14.22 3.88 15.26C4 15.56 4.04 15.85 3.99 16.15L3.77 18.19C3.71 18.75 3.91 19.3 4.3 19.69C4.7 20.09 5.25 20.28 5.81 20.23L7.9 20C8.15 19.96 8.44 20 8.74 20.12C9.77 20.54 10.86 20.75 11.98 20.75H12C13.63 20.75 15.22 20.3 16.6 19.44C17.98 18.59 19.1 17.37 19.82 15.91C20.43 14.71 20.75 13.35 20.75 12V11.49C20.63 9.34 19.73 7.32 18.2 5.79ZM9.01 12.95C8.59 12.95 8.25 12.61 8.25 12.2C8.25 11.79 8.59 11.45 9 11.45H9.01C9.42 11.45 9.76 11.79 9.76 12.2C9.76 12.61 9.42 12.95 9.01 12.95ZM12.02 12.95C11.6 12.95 11.26 12.61 11.26 12.2C11.26 11.79 11.59 11.45 12.01 11.45H12.02C12.43 11.45 12.77 11.79 12.77 12.2C12.77 12.61 12.43 12.95 12.02 12.95ZM15.02 12.95C14.61 12.95 14.27 12.61 14.27 12.2C14.27 11.79 14.6 11.45 15.02 11.45C15.44 11.45 15.77 11.79 15.77 12.2C15.77 12.61 15.44 12.95 15.02 12.95Z"
      fill={color}
    />
  </svg>
);

export default MessageDotCircle;
