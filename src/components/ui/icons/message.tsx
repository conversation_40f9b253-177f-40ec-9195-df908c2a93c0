import React from 'react';

interface MessageIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  width?: number | string;
  height?: number | string;
}

const MessageIcon: React.FC<MessageIconProps> = ({
  color = '#898D8F',
  width = 24,
  height = 24,
  ...props
}) => (
  <svg
    width={width}
    height={height}
    viewBox={`0 0 ${width} ${height}`}
    fill="none"
    {...props}
  >
    <path
      d="M4.5827 8C4.5827 8 9.99999 12 12 12C14 12 19.4173 8 19.4173 8M4 15C4 16.6569 5.34314 18 7 18H17C18.6569 18 20 16.6569 20 15V9C20 7.34315 18.6569 6 17 6H7.00001C5.34315 6 4.00001 7.34315 4.00001 9L4 15Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

export default MessageIcon;
