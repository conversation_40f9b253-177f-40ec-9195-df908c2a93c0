import React from 'react';

interface MoreHorizontalProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  fill?: string;
  width?: number | string;
  height?: number | string;
}

const MoreHorizontal: React.FC<MoreHorizontalProps> = ({
  color = '#B4A6FF',
  fill = 'white',
  width = 16,
  height = 16,
  ...props
}) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" {...props}>
    <circle
      cx="3.6667"
      cy="7.9993"
      r="0.3333"
      fill={fill}
      stroke={color}
      strokeWidth="1.5"
    />
    <circle
      cx="7.6667"
      cy="7.9993"
      r="0.3333"
      fill={fill}
      stroke={color}
      strokeWidth="1.5"
    />
    <circle
      cx="11.6667"
      cy="7.9993"
      r="0.3333"
      fill={fill}
      stroke={color}
      strokeWidth="1.5"
    />
  </svg>
);

export default MoreHorizontal;
