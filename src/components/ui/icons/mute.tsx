import * as React from 'react';

interface MuteIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const MuteIcon: React.FC<MuteIconProps> = ({ color = '#FFF', ...props }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" {...props}>
    <path
      d="M17.4984 10.5L17.1803 12C17.1803 12 16.8628 14.4262 15.6892 15.5998C14.7344 16.5545 13.4395 17.0909 12.0893 17.0909M12.0893 17.0909C10.7392 17.0909 9.44426 16.5545 8.48953 15.5998C7.07153 14.1818 6.89844 10.5455 6.89844 10.5455M12.0893 17.0909V20M9.18026 20H14.9984M12.1893 4C10.5984 4 9.59844 5 9.59844 6.18182C9.59844 6.18182 9.59844 9.5 10.0075 12C10.1128 12.6436 10.2374 13.1336 10.6466 13.5428C11.0557 13.9519 11.6107 14.1818 12.1893 14.1818C12.768 14.1818 13.323 13.9519 13.7321 13.5428C14.1413 13.1336 14.3712 12 14.3712 12C14.8317 9 14.8317 6.18182 14.8317 6.18182C14.8317 5.37658 14.0984 4 12.1893 4Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default MuteIcon;
