import * as React from 'react';

interface PasswordFilledIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const PasswordFilledIcon: React.FC<PasswordFilledIconProps> = ({
  color = '#7257FF',
  isRTL = false,
  ...props
}) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <rect x={5} y={11} width={14} height={9} rx={3} fill={color} />
    <path
      d="M8.2 11.2V8C8.2 6.93913 8.62145 5.92172 9.37159 5.17157C10.1217 4.42143 11.1392 4 12.2 4C13.2609 4 14.2783 4.42143 15.0284 5.17157C15.7786 5.92172 16.2 6.93913 16.2 8V11.2M8 20H16.4C18.0569 20 19.4 18.6569 19.4 17V14.2C19.4 12.5431 18.0569 11.2 16.4 11.2H8C6.34315 11.2 5 12.5431 5 14.2V17C5 18.6568 6.34315 20 8 20Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default PasswordFilledIcon;
