import * as React from 'react';

interface PlayIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const PlayIcon: React.FC<PlayIconProps> = ({
  color = '#070707',
  isRTL = false,
  ...props
}) => (
  <svg
    width={29}
    height={28}
    viewBox="0 0 29 28"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M14.4993 23.3327C19.654 23.3327 23.8327 19.154 23.8327 13.9993C23.8327 8.84469 19.654 4.66602 14.4993 4.66602C9.34469 4.66602 5.16602 8.84469 5.16602 13.9993C5.16602 19.154 9.34469 23.3327 14.4993 23.3327Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.746 14.746L13.4727 18.001C12.901 18.3627 12.1543 17.9427 12.1543 17.2543V10.7443C12.1543 10.056 12.8893 9.63602 13.4727 9.99768L18.746 13.2527C19.2944 13.591 19.2944 14.4077 18.746 14.746Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default PlayIcon;
