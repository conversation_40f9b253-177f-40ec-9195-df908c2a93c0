import * as React from 'react';

interface ProfileIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const ProfileIcon: React.FC<ProfileIconProps> = ({
  color = '#7257FF',
  isRTL = false,
  ...props
}) => (
  <svg
    width={16}
    height={18}
    viewBox="0 0 16 18"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M15.2603 16.5083C14.8303 17.2783 14.0203 17.7483 13.1403 17.7483H2.8703C1.9903 17.7483 1.1803 17.2683 0.750301 16.5083C0.500301 16.0583 0.400301 15.5783 0.460301 15.1283C0.510301 14.6783 0.700301 14.2483 1.0303 13.9183C2.0103 12.9283 4.2103 11.2583 7.9103 11.2583H8.1203C11.8203 11.2583 14.0203 12.9283 15.0003 13.9183C15.3303 14.2483 15.5203 14.6783 15.5703 15.1383C15.6203 15.5983 15.5303 16.0783 15.2803 16.5183L15.2603 16.5083ZM8.0103 8.85828C10.3803 8.85828 12.3203 6.92828 12.3203 4.54828C12.3203 2.16828 10.3903 0.238281 8.0103 0.238281C5.6303 0.238281 3.7003 2.16828 3.7003 4.54828C3.7003 6.92828 5.6303 8.85828 8.0103 8.85828Z"
      fill={color}
    />
  </svg>
);

export default ProfileIcon;
