import * as React from 'react';

interface SendIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const SendIcon: React.FC<SendIconProps> = ({
  color = '#ffffff',
  isRTL = false,
  ...props
}) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M13.1528 1.43477C14.0512 1.02014 14.9797 1.94866 14.5651 2.84702L9.39541 14.0479C8.97916 14.9498 7.66376 14.8403 7.40241 13.882L6.43001 10.3165C6.33086 9.95299 6.04685 9.66898 5.6833 9.56982L2.11785 8.59743C1.15956 8.33608 1.05003 7.02067 1.9519 6.60442L13.1528 1.43477Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default SendIcon;
