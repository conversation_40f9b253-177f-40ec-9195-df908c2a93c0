import * as React from 'react';

interface TicketIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const TicketIcon: React.FC<TicketIconProps> = ({
  color = '#B4A6FF',
  isRTL = false,
  ...props
}) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M7.57041 5.93899C7.69268 5.69125 8.04596 5.69125 8.16824 5.939L8.57548 6.76416C8.62403 6.86254 8.71789 6.93073 8.82646 6.94651L9.73709 7.07883C10.0105 7.11856 10.1197 7.45455 9.92182 7.6474L9.26289 8.2897C9.18433 8.36628 9.14848 8.47661 9.16702 8.58474L9.32258 9.49169C9.36928 9.76399 9.08347 9.97165 8.83893 9.84308L8.02444 9.41488C7.92733 9.36383 7.81132 9.36383 7.71421 9.41488L6.89972 9.84308C6.65518 9.97165 6.36937 9.76399 6.41607 9.49169L6.57162 8.58474C6.59017 8.47661 6.55432 8.36628 6.47576 8.2897L5.81682 7.6474C5.61898 7.45455 5.72815 7.11856 6.00156 7.07883L6.91219 6.94651C7.02076 6.93073 7.11461 6.86254 7.16317 6.76416L7.57041 5.93899Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
    <path
      d="M3.99984 4C3.26346 4 2.6665 4.59695 2.6665 5.33333V10.6667C2.6665 11.403 3.26346 12 3.99984 12H11.9998C12.7362 12 13.3332 11.403 13.3332 10.6667V9.33333C12.5968 9.33333 11.9998 8.73638 11.9998 8C11.9998 7.26362 12.5968 6.66667 13.3332 6.66667V5.33333C13.3332 4.59695 12.7362 4 11.9998 4H3.99984Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
  </svg>
);

export default TicketIcon;
