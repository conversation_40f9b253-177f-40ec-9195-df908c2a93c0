import * as React from 'react';

interface UnmuteIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const UnmuteIcon: React.FC<UnmuteIconProps> = ({
  color = '#FFF',
  isRTL = false,
  ...props
}) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M9 20H15M12 20V17M12 17C12.12 17 12.25 17 12.37 16.99C13.5 16.92 14.55 16.53 15.43 15.92M12 17C9.91 17 8.07 15.93 7 14.31M9.82 4.95C10.36 4.36 11.14 4 12 4C12.86 4 13.58 4.34 14.12 4.88C14.66 5.42 15 6.17 15 7V10.87M9 8.57V11C9 12.66 10.34 14 12 14C12.52 14 13.01 13.87 13.43 13.64M5 4L19 20"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default UnmuteIcon;
