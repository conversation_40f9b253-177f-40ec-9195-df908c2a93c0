import * as React from 'react';

interface UserFilledIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const UserFilledIcon: React.FC<UserFilledIconProps> = ({
  color = '#7257FF',
  isRTL = false,
  ...props
}) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M11.9082 14.7598H12.1182C15.43 14.7598 17.4645 16.1573 18.458 17.0918L18.6426 17.2725L18.6445 17.2734C18.8865 17.5154 19.0324 17.8367 19.0713 18.1943C19.1001 18.4597 19.0682 18.7341 18.9697 19L18.8223 19.2666C18.4816 19.8767 17.8393 20.2499 17.1387 20.25H6.86816C6.21425 20.2499 5.60645 19.9154 5.25098 19.375L5.18359 19.2637C5.00605 18.9427 4.93031 18.6187 4.94336 18.3223L4.9541 18.1963L4.95508 18.1855C4.98886 17.8815 5.10471 17.5972 5.2959 17.3682L5.38184 17.2734L5.38379 17.2725C6.29664 16.3503 8.37555 14.7598 11.9082 14.7598ZM12.0088 3.74023C14.1123 3.74047 15.8181 5.44629 15.8184 7.5498C15.8184 9.65276 14.1032 11.3601 12.0088 11.3604C9.90493 11.3604 8.19824 9.65366 8.19824 7.5498C8.19847 5.44614 9.90507 3.74023 12.0088 3.74023Z"
      fill={color}
      stroke={color}
    />
  </svg>
);

export default UserFilledIcon;
