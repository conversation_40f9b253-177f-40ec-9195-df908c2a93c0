import * as React from 'react';

interface UserIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const UserIcon: React.FC<UserIconProps> = ({
  color = '#7257FF',
  isRTL = false,
  ...props
}) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M13.2357 17C13.8452 17 14.4073 16.6713 14.7061 16.1401C15.022 15.5784 14.9874 14.8849 14.5195 14.4417C13.513 13.488 11.3979 12 8.00014 12C4.60848 12 2.59722 13.4827 1.65424 14.4365C1.20959 14.8863 1.19278 15.5647 1.50285 16.1159"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.11125 8.11111C10.0749 8.11111 11.6668 6.51924 11.6668 4.55556C11.6668 2.59188 10.0749 1 8.11125 1C6.14757 1 4.55569 2.59188 4.55569 4.55556C4.55569 6.51924 6.14757 8.11111 8.11125 8.11111Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default UserIcon;
