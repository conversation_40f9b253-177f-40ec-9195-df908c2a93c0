import * as React from 'react';

interface WarningTriangleProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const WarningTriangle: React.FC<WarningTriangleProps> = ({
  color = '#FFD84D',
  isRTL = false,
  ...props
}) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M11.9155 9.37017V13.0001M11.9155 16.7612H11.9235M17.7418 20H6.08931C4.54351 20 3.53434 18.3779 4.21746 16.9912L10.0437 5.16453C10.8086 3.61183 13.0225 3.61182 13.7874 5.16452L19.6136 16.9912C20.2967 18.3779 19.2876 20 17.7418 20Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default WarningTriangle;
