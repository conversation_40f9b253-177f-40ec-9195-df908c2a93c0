import NextImage, { type ImageProps as NextImageProps } from 'next/image';
import * as React from 'react';

export type ImgProps = NextImageProps & {
  className?: string;
  placeholder?: string;
  alt?: string;
};

export const Image = ({
  className,
  placeholder,
  alt = '',
  ...props
}: ImgProps) => {
  return (
    <NextImage
      className={className}
      alt={alt || ''}
      {...props}
      placeholder={placeholder ? 'blur' : undefined}
      blurDataURL={placeholder}
      priority={false}
    />
  );
};

export const preloadImages = (sources: string[]) => {
  sources.forEach((src) => {
    const img = new window.Image();
    img.src = src;
  });
};
