'use client';
import React, { useState, useEffect, useMemo } from 'react';
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { useController, useFormContext } from 'react-hook-form';
import { tv } from 'tailwind-variants';
import { FiEye, FiEyeOff } from 'react-icons/fi';

const inputTv = tv({
  slots: {
    container: 'relative',
    label: `
    absolute left-3 transition-all duration-300 pointer-events-none
    text-fg-muted-light dark:text-fg-muted-dark

    peer-placeholder-shown:top-1/2
    peer-placeholder-shown:-translate-y-1/2
    peer-placeholder-shown:text-base
    peer-placeholder-shown:font-medium

    peer-focus:top-1 peer-[&:not(:placeholder-shown)]:top-1
    peer-focus:text-xs peer-[&:not(:placeholder-shown)]:text-xs
    peer-focus:font-semibold peer-[&:not(:placeholder-shown)]:font-semibold

    peer-focus:text-accent-moderate`,
    input: `w-full 
rounded-md 
border border-border-subtle-light dark:border-border-subtle-dark
bg-transparent 
font-aeonik-regular 
text-base leading-[100%] font-medium 
shadow-none 
dark:text-white
focus:border-accent-moderate dark:focus:border-accent-moderate
focus:outline-hidden focus:ring-0
focus:shadow-[0_0_0_4px_rgba(114,87,255,0.3)] peer`,
    iconContainer:
      'absolute left-3 top-3.5 text-accent-moderate dark:text-accent-moderate',
  },
  variants: {
    focused: {
      true: { input: 'border-accent-moderate dark:border-accent-moderate' },
    },
    filled: { true: {} },
    error: {
      true: {
        input: 'border-2 border-red-30 dark:border-red-30',
        label: 'text-red-40 dark:text-red-40',
      },
    },
    disabled: { true: { input: 'opacity-50 cursor-not-allowed' } },
    labelLayout: {
      floating: { input: 'pt-5 pb-2 px-3' },
      centered: { input: 'py-3 px-3', label: 'hidden' },
    },
    withIcon: { true: { input: 'pl-10' } },
  },
  defaultVariants: {
    focused: false,
    filled: false,
    error: false,
    disabled: false,
    labelLayout: 'floating',
  },
});

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement> {
  label?: string;
  error?: string;
  isPassword?: boolean;
  icon?: React.ReactNode;
  iconClassName?: string;
  containerClassName?: string;
  inputClassName?: string;
  focusedInputClassName?: string;
  handleFieldBlur?: () => void;
  handleFieldUnBlur?: () => void;
  hideErrorMessage?: boolean;
  multiline?: boolean;
  rows?: number;
}

type TRule<T extends FieldValues> =
  | Omit<
      RegisterOptions<T>,
      'disabled' | 'valueAsNumber' | 'valueAsDate' | 'setValueAs'
    >
  | undefined;

export type RuleType<T extends FieldValues> = { [name in keyof T]: TRule<T> };
export type InputControllerType<T extends FieldValues> = {
  name: Path<T>;
  control?: Control<T>;
  rules?: TRule<T>;
};

interface ControlledInputProps<T extends FieldValues>
  extends Omit<InputProps, 'name'>,
    InputControllerType<T> {}

export const Input = React.forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  InputProps
>(
  (
    {
      label,
      error,
      isPassword,
      icon,
      hideErrorMessage,
      containerClassName = '',
      inputClassName = '',
      iconClassName = '',
      focusedInputClassName = '',
      value,
      onBlur,
      onChange,
      handleFieldBlur,
      handleFieldUnBlur,
      multiline,
      rows,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(!isPassword);
    const [isFocused, setIsFocused] = useState(false);
    const [isFilled, setIsFilled] = useState(!!value);

    useEffect(() => {
      setIsFilled(!!value);
    }, [value]);

    const handleValueChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      setIsFilled(!!e.target.value);
      onChange?.(e);
    };

    const handleFocus = (
      e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      setIsFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (
      e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      setIsFocused(false);
      onBlur?.(e);
      handleFieldBlur?.();
      handleFieldUnBlur?.();
    };

    const styles = useMemo(
      () =>
        inputTv({
          focused: isFocused,
          filled: isFilled,
          error: !!error,
          disabled: props.disabled,
          labelLayout: label ? 'floating' : 'centered',
          withIcon: !!icon,
        }),
      [isFocused, isFilled, error, props.disabled, label, icon]
    );

    const Element = multiline ? 'textarea' : 'input';

    return (
      <div className={`${styles.container()} ${containerClassName}`}>
        <div className="relative">
          {icon && (
            <div className={`${styles.iconContainer()} ${iconClassName}`}>
              {icon}
            </div>
          )}
          <Element
            {...props}
            ref={ref as any}
            type={
              isPassword
                ? showPassword
                  ? 'text'
                  : 'password'
                : (props.type ?? 'text')
            }
            className={`${styles.input()} ${inputClassName} ${
              isFocused ? focusedInputClassName : ''
            } ${multiline ? 'resize-y' : 'h-[52px]'}`}
            placeholder=" "
            value={value}
            onChange={handleValueChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            rows={multiline ? rows : undefined}
          />
          {label && (
            <label htmlFor={props.id} className={styles.label()}>
              {label}
            </label>
          )}
          {isPassword && (
            <button
              type="button"
              aria-label={showPassword ? 'Hide password' : 'Show password'}
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-3.5 text-gray-500 dark:text-gray-400"
            >
              {showPassword ? <FiEye /> : <FiEyeOff />}
            </button>
          )}
        </div>
        {error && !hideErrorMessage && (
          <p className="text-red-400 dark:text-red-400 text-sm mt-1">{error}</p>
        )}
      </div>
    );
  }
);

export function ControlledInput<T extends FieldValues>(
  props: ControlledInputProps<T>
) {
  const { name, rules, control, ...rest } = props;

  const formContext = useFormContext<T>();
  const usedControl = control || formContext?.control;

  if (!usedControl) {
    throw new Error(
      `ControlledInput for "${name}" must be used inside a FormProvider or passed a 'control' prop.`
    );
  }

  const { field, fieldState } = useController({
    control: usedControl,
    name,
    rules,
  });

  return (
    <Input
      {...rest}
      ref={field.ref}
      value={field.value ?? ''}
      onChange={field.onChange}
      onBlur={field.onBlur}
      error={rest.hideErrorMessage ? undefined : fieldState.error?.message}
    />
  );
}

Input.displayName = 'Input';
