'use client';

import { semanticColors } from '@/components/ui';
import React, { useCallback, useMemo, useState } from 'react';
import GooglePlacesAutocomplete, {
  geocodeByPlaceId,
  getLatLng,
} from 'react-google-places-autocomplete';
import { LocationIcon } from '@/components/ui/icons';
import { type LocationType } from '@/types';
import { useTheme } from 'next-themes';
import { BsCursorFill } from 'react-icons/bs';
import { Point } from '@/api';

interface PlaceValue {
  label: string;
  value: {
    description: string;
    place_id?: string;
  };
}

export type LocationInputType = {
  city: string;
  state: string;
  street: string;
  country: string;
  address: string;
  landmark?: string;
};

interface LocationInputProps {
  onSelectLocation: (location: LocationInputType, coordinates: Point) => void;
  defaultValue?: LocationType;
  className?: string;
  shouldDisplayCityAsLocationName?: boolean;
  hasCurrentLocationIcon?: boolean;
  textInputHeight?: number;
  onBlur?: () => void;
  onFocus?: () => void;
  borderRadius?: number;
  hasBorder?: boolean;
}

const geocode = (
  geocoder: google.maps.Geocoder,
  latLng: { lat: number; lng: number }
) => {
  return new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
    geocoder.geocode({ location: latLng }, (results, status) => {
      if (status === 'OK' && results) {
        resolve(results);
      } else {
        reject(new Error(status));
      }
    });
  });
};

const parseAddressComponents = (
  addressComponents: google.maps.GeocoderAddressComponent[]
) => {
  let city = '';
  let state = '';
  let street = '';
  let country = '';

  addressComponents.forEach((comp) => {
    if (comp.types.includes('locality')) city = comp.long_name;
    else if (comp.types.includes('administrative_area_level_1'))
      state = comp.long_name;
    else if (comp.types.includes('route')) street = comp.long_name;
    else if (comp.types.includes('country')) country = comp.long_name;
  });

  return { city, state, street, country };
};

export const LocationInput: React.FC<LocationInputProps> = ({
  onSelectLocation,
  defaultValue,
  className,
  shouldDisplayCityAsLocationName,
  hasCurrentLocationIcon = true,
  borderRadius = 8,
  textInputHeight = 48,
  hasBorder = true,
}) => {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const [loadingLocation, setLoadingLocation] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initialValue = useMemo<PlaceValue | null>(() => {
    if (!defaultValue?.address && !defaultValue?.city) return null;

    const label = shouldDisplayCityAsLocationName
      ? defaultValue.city
      : (defaultValue.landmark ?? defaultValue.address);

    return {
      label,
      value: {
        description: label,
        place_id: defaultValue.coordinates ? 'initial-place-id' : undefined,
      },
    };
  }, [defaultValue, shouldDisplayCityAsLocationName]);

  const [placeValue, setPlaceValue] = useState<PlaceValue | null>(initialValue);

  const handleSelect = useCallback(
    async (place: PlaceValue | null) => {
      if (!place?.value?.place_id) {
        setPlaceValue(place);
        const emptyLocation: LocationInputType = {
          city: '',
          state: '',
          street: '',
          country: '',
          address: '',
          landmark: '',
        };
        onSelectLocation(emptyLocation, {
          lat: 0,
          lng: 0,
          latitude: 0,
          longitude: 0,
        });
        return;
      }

      try {
        const results = await geocodeByPlaceId(place.value.place_id);
        if (!results?.length) throw new Error('No geocoding results found');

        const latLng = await getLatLng(results[0]);
        const { city, state, street, country } = parseAddressComponents(
          results[0].address_components
        );
        const landmark = results[0].formatted_address || '';

        const locationObj: LocationInputType = {
          city,
          state,
          street,
          country,
          address: landmark,
          landmark,
        };

        setPlaceValue(place);
        onSelectLocation(
          { ...locationObj },
          { ...latLng, latitude: latLng.lat, longitude: latLng.lng }
        );
      } catch (err) {
        console.error(err);
        setError('Failed to get location details. Please try again.');
        onSelectLocation(
          {
            city: '',
            state: '',
            street: '',
            country: '',
            address: '',
            landmark: '',
          },
          { lat: 0, lng: 0, latitude: 0, longitude: 0 }
        );
      }
    },
    [onSelectLocation]
  );

  const handleUseCurrentLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser.');
      return;
    }
    setLoadingLocation(true);
    navigator.geolocation.getCurrentPosition(
      async ({ coords: { latitude: lat, longitude: lng } }) => {
        try {
          const geocoder = new google.maps.Geocoder();
          const results = await geocode(geocoder, { lat, lng });
          if (!results.length) throw new Error('No reverse geocoding results');

          const { city, state, street, country } = parseAddressComponents(
            results[0].address_components
          );
          const landmark = results[0].formatted_address || '';
          const locationObj: LocationInputType = {
            city,
            state,
            street,
            country,
            address: landmark,
            landmark,
          };

          setPlaceValue({
            label: landmark,
            value: { description: landmark, place_id: results[0].place_id },
          });

          onSelectLocation(locationObj, {
            lat,
            lng,
            latitude: lat,
            longitude: lng,
          });
        } catch (err) {
          console.error(err);
          setError(
            'Failed to get address from your location. Please try again.'
          );
        } finally {
          setLoadingLocation(false);
        }
      },
      (geoError: GeolocationPositionError) => {
        setLoadingLocation(false);

        let errorMessage = 'Unable to retrieve your location.';

        if (geoError && typeof geoError.code === 'number') {
          switch (geoError.code) {
            case geoError.PERMISSION_DENIED:
              errorMessage =
                'Location access denied. Please enable it in your browser settings.';
              break;
            case geoError.POSITION_UNAVAILABLE:
              errorMessage = 'Location information is unavailable.';
              break;
            case geoError.TIMEOUT:
              errorMessage = 'The request to get your location timed out.';
              break;
            default:
              errorMessage = 'Unable to retrieve your location.';
          }
          console.error(
            'Geolocation error:',
            `code=${geoError.code}, message=${geoError.message}`
          );
        } else {
          console.error(
            'Geolocation callback received unknown error:',
            geoError
          );
        }

        setError(errorMessage);
      }
    );
  }, [onSelectLocation]);

  const customStyles = useMemo(
    () => ({
      container: (provided: any) => ({
        ...provided,
        width: '100%',
      }),
      control: (provided: any) => ({
        ...provided,
        height: textInputHeight,
        minHeight: textInputHeight,
        borderRadius: `${borderRadius}px`,
        borderWidth: hasBorder ? '1px' : '0',
        borderColor: isDark
          ? semanticColors.border.subtle.dark
          : semanticColors.border.subtle.light,
        backgroundColor: 'transparent',
        boxShadow: 'none',
        '&:hover': {
          borderColor: semanticColors.border.subtle.light,
        },
        '&:focus-within': {
          borderColor: semanticColors.accent.moderate,
          boxShadow: 'none',
        },
      }),
      input: (provided: any) => ({
        ...provided,
        color: 'inherit',
        fontFamily: '"Aeonik", sans-serif',
        fontSize: '1rem',
        fontWeight: 500,
        margin: 0,
        padding: 0,
        paddingLeft: '2.5rem',
      }),
      placeholder: (provided: any) => ({
        ...provided,
        color: semanticColors.fg.muted.dark,
        fontFamily: '"Aeonik", sans-serif',
        fontSize: '1rem',
        fontWeight: 500,
        paddingLeft: '2.5rem',
      }),
      singleValue: (provided: any) => ({
        ...provided,
        color: 'inherit',
        fontFamily: '"Aeonik", sans-serif',
        fontSize: '1rem',
        fontWeight: 500,
        paddingLeft: '2.5rem',
      }),
      valueContainer: (provided: any) => ({
        ...provided,
        padding: '0',
      }),
      indicatorSeparator: () => ({
        display: 'none',
      }),
      dropdownIndicator: () => ({
        display: 'none',
      }),
      menu: (provided: any) => ({
        ...provided,
        backgroundColor: semanticColors.bg.surface.dark,
        border: `1px solid ${semanticColors.border.subtle.dark}`,
        borderRadius: '0.375rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      }),
      option: (provided: any, state: any) => ({
        ...provided,
        backgroundColor: state.isSelected
          ? semanticColors.accent.moderate
          : state.isFocused
            ? semanticColors.bg.muted.dark
            : 'transparent',
        color: state.isSelected
          ? semanticColors.accent['on-accent']
          : 'inherit',
        fontSize: '0.875rem',
        padding: '0.5rem 0.75rem',
      }),
    }),
    [isDark, textInputHeight, borderRadius, hasBorder]
  );

  return (
    <div className={className}>
      <div className="relative">
        <div className="absolute left-3 top-1/2 -translate-y-1/2 z-10">
          <LocationIcon />
        </div>
        <GooglePlacesAutocomplete
          apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY || ''}
          selectProps={{
            value: placeValue,
            onChange: (place) => {
              setError(null);
              setPlaceValue(place);
              handleSelect(place);
            },
            placeholder: 'Search location',
            isClearable: true,
            styles: customStyles,
          }}
          autocompletionRequest={{
            types: [],
          }}
        />
        {hasCurrentLocationIcon && !initialValue && (
          <button
            type="button"
            onClick={handleUseCurrentLocation}
            aria-label="Use current location"
            className="absolute inset-y-0 right-0 flex items-center pr-3"
            disabled={loadingLocation}
          >
            {loadingLocation ? (
              '...'
            ) : (
              <BsCursorFill color={semanticColors.fg.muted.light} />
            )}
          </button>
        )}
      </div>
      {error && (
        <p className="mt-2 text-sm text-fg-danger-light dark:text-fg-danger-dark">
          {error}
        </p>
      )}
    </div>
  );
};
