'use client';

import React, { useEffect } from 'react';
import { X } from 'lucide-react';

type ModalProps = {
  isOpen: boolean;
  title?: string;
  onClose: () => void;
  children: React.ReactNode;
};

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  title,
  onClose,
  children,
}) => {
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      <div
        className="fixed inset-0 bg-black/80 z-40 animate-fadeIn"
        onClick={onClose}
      />

      <div
        className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 
                   bg-white dark:bg-bg-canvas-dark rounded-lg shadow-xl z-50
                   w-[90vw] max-w-lg max-h-[90vh] overflow-y-auto p-4
                   animate-scaleIn"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-4">
          {title ? (
            <h2
              id="modal-title"
              className="text-xl font-semibold text-grey-90 dark:text-grey-10"
            >
              {title}
            </h2>
          ) : (
            <span />
          )}

          <button
            aria-label="Close modal"
            className="text-grey-50 hover:text-grey-70 dark:text-grey-40 dark:hover:text-grey-20"
            onClick={onClose}
          >
            <X size={24} />
          </button>
        </div>

        <div className="text-grey-70 dark:text-grey-30">{children}</div>
      </div>

      <style>
        {`
        @keyframes scaleIn {
          from {
            opacity: 0;
            transform: scale(0.95); /* only scale */
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
        .animate-scaleIn { animation: scaleIn 0.2s ease forwards; }
      `}
      </style>
    </>
  );
};
