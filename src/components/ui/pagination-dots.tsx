import React from 'react';
import { cn } from '@/lib/utils';

interface PaginationDotsProps {
  total: number;
  activeIndex: number;
  onDotClick?: (index: number) => void;
  className?: string;
  dotClassName?: string;
  renderDot?: (index: number, isActive: boolean) => React.ReactNode;
}

export const PaginationDots: React.FC<PaginationDotsProps> = ({
  total,
  activeIndex,
  onDotClick,
  className,
  dotClassName,
  renderDot,
}) => {
  return (
    <div
      className={cn(
        'flex flex-row justify-center mt-4 items-center gap-2',
        className
      )}
    >
      {Array.from({ length: total }).map((_, i) => {
        const isActive = i === activeIndex;

        if (renderDot) return renderDot(i, isActive);

        return (
          <button
            key={i}
            onClick={() => onDotClick?.(i)}
            className={cn(
              'size-2 bg-bg-interactive-primary-light dark:bg-bg-interactive-primary-dark p-0 rounded-full',
              isActive && 'bg-accent-moderate dark:bg-accent-moderate',
              dotClassName
            )}
            aria-label={`Go to page ${i + 1}`}
            disabled={!onDotClick}
          />
        );
      })}
    </div>
  );
};
