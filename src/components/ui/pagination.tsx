'use client';

import React from 'react';
import { MdBold<PERSON>abel, SmBoldLabel } from '@/components/ui/typography';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
}) => {
  if (totalPages <= 1) return null;

  const pages = [...Array(totalPages)].map((_, idx) => idx + 1);

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <MdBoldLabel className="text-fg-link dark:text-fg-link">
          Prev
        </MdBoldLabel>
      </button>

      <div className="flex flex-row items-center gap-2">
        {pages.map((pageNumber) => {
          if (
            pageNumber === 1 ||
            pageNumber === totalPages ||
            (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
          ) {
            return (
              <button
                key={pageNumber}
                onClick={() => onPageChange(pageNumber)}
                className={`size-8 rounded-full ${
                  currentPage === pageNumber
                    ? 'text-accent-bold-light dark:text-accent-bold-dark bg-accent-subtle-light dark:bg-accent-subtle-dark hover:bg-accent-subtle-light'
                    : 'text-accent-bold-light dark:text-accent-bold-dark hover:text-white hover:bg-accent-subtle-light border border-border-subtle-light dark:border-border-subtle-dark'
                }`}
              >
                <SmBoldLabel className="text-accent-bold-light dark:text-accent-bold-dark">
                  {pageNumber}
                </SmBoldLabel>
              </button>
            );
          }

          if (
            pageNumber === currentPage - 2 ||
            pageNumber === currentPage + 2
          ) {
            return (
              <span key={pageNumber} className="text-gray-400 px-2">
                ...
              </span>
            );
          }

          return null;
        })}
      </div>

      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <MdBoldLabel className="text-fg-link dark:text-fg-link">
          Next
        </MdBoldLabel>
      </button>
    </div>
  );
};
