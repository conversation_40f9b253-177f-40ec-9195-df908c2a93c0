'use client';

import * as React from 'react';
import { EyeIcon, EyeOffIcon } from 'lucide-react';

import { cn } from '@/lib/utils';
import {
  FloatingInput,
  FloatingInputProps,
} from '@/components/ui/floating-input';

export const PasswordInput = React.forwardRef<
  HTMLInputElement,
  FloatingInputProps
>(({ className, label, ...props }, ref) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const disabled =
    props.value === '' || props.value === undefined || props.disabled;

  return (
    <div>
      <FloatingInput
        type={showPassword ? 'text' : 'password'}
        className={cn('hide-password-toggle', className)}
        ref={ref}
        label={label}
        {...props}
        postIcon={
          <button
            type="button"
            onClick={() => setShowPassword((prev) => !prev)}
            disabled={disabled}
          >
            {showPassword && !disabled ? (
              <EyeIcon className="size-6" aria-hidden="true" />
            ) : (
              <EyeOffIcon className="size-6" aria-hidden="true" />
            )}
            <span className="sr-only">
              {showPassword ? 'Hide password' : 'Show password'}
            </span>
          </button>
        }
      />

      <style>{`
					.hide-password-toggle::-ms-reveal,
					.hide-password-toggle::-ms-clear {
						visibility: hidden;
						pointer-events: none;
						display: none;
					}
				`}</style>
    </div>
  );
});
PasswordInput.displayName = 'PasswordInput';
