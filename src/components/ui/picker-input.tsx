'use client';

import { colors } from '@/components/ui/colors';
import React from 'react';
import { IoChevronDownOutline } from 'react-icons/io5';

interface DateButtonProps {
  value?: Date | string | null;
  label: string;
  disabled?: boolean;
  onClick?: () => void;
  withChevron?: boolean;
}

export const InputPickerButton: React.FC<DateButtonProps> = ({
  value,
  label,
  disabled,
  onClick,
  withChevron = false,
}) => {
  return (
    <button
      type="button"
      className="h-[52px] w-full flex flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark cursor-pointer"
      disabled={disabled}
      onClick={onClick}
    >
      <p
        className={
          value
            ? 'dark:text-neutral-100'
            : 'text-neutral-400 dark:text-neutral-500'
        }
      >
        {label}
      </p>
      {withChevron && (
        <IoChevronDownOutline size={20} color={colors.grey[70]} />
      )}
    </button>
  );
};
