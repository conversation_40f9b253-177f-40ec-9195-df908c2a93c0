import React from 'react';

import {
  MdRegularLabel,
  RadioGroupItem,
  SmRegularLabel,
} from '@/components/ui';

type RadioGroupOptionProps = {
  value: string;
  currentValue: string;
  title: string;
  description?: string;
  customContent?: React.ReactNode;
  onPress?: () => void;
  labelClassName?: string;
};

export const RadioGroupOption: React.FC<RadioGroupOptionProps> = ({
  value,
  currentValue,
  title,
  description,
  customContent,
  onPress,
  labelClassName,
}) => {
  const isSelected = currentValue === value;

  return (
    <button
      className="flex flex-row items-center gap-4 rounded-md bg-bg-subtle-light px-4 py-3 dark:bg-bg-subtle-dark cursor-pointer"
      role="radio"
      aria-checked={isSelected}
      onClick={onPress}
    >
      <div className="flex-1 flex gap-2.5">
        <MdRegularLabel className={labelClassName}>{title}</MdRegularLabel>
        {description && !customContent && (
          <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark">
            {description}
          </SmRegularLabel>
        )}
        {customContent && !description ? customContent : null}
        {description && customContent ? customContent : null}
      </div>

      <div className="flex items-center justify-center">
        <RadioGroupItem
          value={value}
          aria-selected={isSelected}
          aria-labelledby={`label-for-${value}`}
          onClick={onPress}
          checked={isSelected}
        />
      </div>
    </button>
  );
};
