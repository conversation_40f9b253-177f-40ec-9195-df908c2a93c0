import React from 'react';
import { cn } from '@/lib';

interface RadioGroupProps
  extends Omit<React.FieldsetHTMLAttributes<HTMLFieldSetElement>, 'onChange'> {
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
  children: React.ReactNode;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  className,
  value,
  onChange,
  children,
  ...props
}) => {
  const cloneRecursively = (child: React.ReactNode): React.ReactNode => {
    if (!React.isValidElement(child)) return child;

    const el = child as React.ReactElement<any>;
    const childValue = (el.props as any).value;
    if (typeof childValue !== 'undefined') {
      return React.cloneElement(el, {
        checked: childValue === value,
        onChange: () => onChange?.(childValue),
        name: (props as any).id || 'radio-group',
      } as any);
    }

    const childChildren = (el.props as any).children;
    if (childChildren) {
      return React.cloneElement(el, {
        children: React.Children.map(childChildren, cloneRecursively),
      } as any);
    }

    return el;
  };

  return (
    <fieldset
      role="radiogroup"
      className={cn('grid gap-2', className)}
      {...props}
    >
      {React.Children.map(children, cloneRecursively)}
    </fieldset>
  );
};

interface RadioGroupItemProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
  value: string;
  label?: string;
  disabled?: boolean;
}

export const RadioGroupItem: React.FC<RadioGroupItemProps> = ({
  className,
  value,
  label,
  disabled,
  checked,
  onChange,
  name,
  ...props
}) => {
  return (
    <label
      className={cn(
        'inline-flex items-center cursor-pointer gap-2',
        disabled && 'cursor-not-allowed opacity-50',
        className
      )}
      aria-checked={checked}
      role="radio"
    >
      <input
        type="radio"
        value={value}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        name={name}
        className="sr-only"
        {...props}
      />
      <span
        className={cn(
          'h-6 w-6 rounded-full border border-grey-30 dark:border-grey-70 flex items-center justify-center',
          checked
            ? 'border-4 border-brand-60 dark:border-brand-40'
            : 'border-grey-30 dark:border-grey-70'
        )}
      >
        {checked && <span className="size-2 rounded-full bg-white" />}
      </span>
      {label && <span>{label}</span>}
    </label>
  );
};
