'use client';
import React, { useMemo } from 'react';
import 'react-quill-new/dist/quill.snow.css';
import dynamic from 'next/dynamic';

const ReactQuill = dynamic(() => import('react-quill-new'), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
});
import { SmRegularLabel } from '@/components/ui';
import { useTheme } from 'next-themes';

const defaultModules = {
  toolbar: [
    [{ header: [1, 2, false] }],
    ['bold', 'italic', 'underline'],
    ['link'],
  ],
};

type RichTextInputProps = {
  value: string;
  onChange: (val: string) => void;
  label?: string;
  placeholder?: string;
  maxLength?: number;
  modules?: any;
};

const getPlainTextLength = (html: string): number => {
  if (typeof document === 'undefined') return 0;
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  return tempDiv.textContent?.length || 0;
};

export const RichTextInput: React.FC<RichTextInputProps> = ({
  value,
  onChange,
  label = 'Description',
  placeholder = 'Type here...',
  maxLength = 500,
  modules = defaultModules,
}) => {
  const plainTextLength = useMemo(
    () => getPlainTextLength(value || ''),
    [value]
  );
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  // Debug: Log modules to see if they're being passed correctly
  React.useEffect(() => {
    console.log('RichTextInput modules:', modules);
  }, [modules]);

  const handleChange = (newValue: string) => {
    const length = getPlainTextLength(newValue);
    if (length <= maxLength) {
      onChange(newValue);
    }
  };

  React.useEffect(() => {
    const quillStyles = `
      .quill-editor .ql-container {
        border: 1px solid var(--quill-border);
        border-top: none;
        border-radius: 0 0 6px 6px;
        background: var(--quill-bg);
        color: var(--quill-text);
        font-family: var(--font-aeonik);
        font-size: 16px;
        min-height: 120px;
      }

      .quill-editor .ql-toolbar {
        border: 1px solid var(--quill-border);
        border-bottom: none;
        border-radius: 6px 6px 0 0;
        background: var(--quill-bg);
      }

      .quill-editor .ql-toolbar .ql-stroke {
        stroke: var(--quill-text);
      }

      .quill-editor .ql-toolbar .ql-fill {
        fill: var(--quill-text);
      }

      .quill-editor .ql-toolbar .ql-picker-label {
        color: var(--quill-text);
      }

      .quill-editor .ql-editor {
        color: var(--quill-text);
        padding: 12px 16px;
      }

      .quill-editor .ql-editor.ql-blank::before {
        color: var(--quill-placeholder);
        font-style: normal;
        font-weight: 400;
      }

      .quill-editor .ql-editor:focus {
        outline: none;
      }

      .quill-editor:focus-within .ql-container,
      .quill-editor:focus-within .ql-toolbar {
        border-color: #7257ff;
        box-shadow: 0 0 0 4px rgba(114, 87, 255, 0.3);
      }

      .quill-editor .ql-toolbar button:hover {
        background: rgba(114, 87, 255, 0.1);
      }

      .quill-editor .ql-toolbar button.ql-active {
        background: rgba(114, 87, 255, 0.2);
      }
    `;

    const styleElement = document.getElementById('quill-custom-styles');
    if (!styleElement) {
      const style = document.createElement('style');
      style.id = 'quill-custom-styles';
      style.textContent = quillStyles;
      document.head.appendChild(style);
    }
  }, []);

  return (
    <div className="space-y-2">
      {label && (
        <div>
          <span className="text-fg-muted-light dark:text-fg-muted-dark">
            {label}
          </span>
        </div>
      )}

      <div className="relative">
        <ReactQuill
          value={value || ''}
          placeholder={placeholder}
          onChange={handleChange}
          modules={modules}
          theme="snow"
          className="quill-editor"
          style={
            {
              '--quill-bg': 'transparent',
              '--quill-border': isDark ? '#53575a' : '#e8ebeb',
              '--quill-text': isDark ? '#fff' : '#000',
              '--quill-placeholder': isDark ? '#898d8f' : '#6e7375',
            } as React.CSSProperties
          }
        />
      </div>

      <div className="flex justify-end items-center">
        <SmRegularLabel
          className={`${
            plainTextLength > maxLength * 0.9
              ? 'text-fg-danger-light dark:text-fg-danger-dark'
              : 'text-fg-muted-light dark:text-fg-muted-dark'
          }`}
        >
          {plainTextLength}/{maxLength}
        </SmRegularLabel>
      </div>
    </div>
  );
};
