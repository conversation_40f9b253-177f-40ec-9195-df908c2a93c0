import React from 'react';
import { IoSearch } from 'react-icons/io5';
import { cn } from '@/lib';

export interface SearchInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    'value' | 'onChange'
  > {
  placeholder?: string;
  className?: string;
  value: string;
  onChangeText?: (text: string) => void;
  iconClassName?: string;
  inputClassName?: string;
  placeholderClassName?: string;
  focusedInputClassName?: string;
  onSubmit?: () => void;
  onClear?: () => void;
  showClearButton?: boolean;
}

export const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  (
    {
      placeholder = 'Explore',
      value,
      onChangeText,
      iconClassName,
      inputClassName,
      className,
      focusedInputClassName,
      showClearButton = true,
      onClear,
      onSubmit,
      ...rest
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = React.useState(false);

    return (
      <div className={cn('relative w-full', className)}>
        <IoSearch
          size={18}
          className={cn(
            'absolute left-4 top-1/2 -translate-y-1/2 text-fg-base-light dark:text-fg-base-dark',
            iconClassName
          )}
        />
        <input
          ref={ref}
          type="text"
          value={value}
          placeholder={placeholder}
          onChange={(e) => onChangeText?.(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') onSubmit?.();
          }}
          className={cn(
            'w-full rounded-full py-3 pl-10 pr-4 bg-bg-subtle-light dark:bg-bg-subtle-dark text-dark dark:text-white placeholder-fg-subtle-light dark:placeholder-fg-subtle-dark text-base border-none focus:outline-none focus:ring-0', // Unified dark background, no border, and no focus ring
            inputClassName,
            isFocused && focusedInputClassName
          )}
          {...rest}
        />
        {showClearButton && value && (
          <button
            type="button"
            onClick={() => {
              onClear?.();
              onChangeText?.('');
            }}
            className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-200"
          >
            ×
          </button>
        )}
      </div>
    );
  }
);

SearchInput.displayName = 'SearchInput';
