import React from 'react';
import { cn, formatEnumLabel } from '@/lib';

interface StatusPillProps {
  status?: 'PENDING' | 'FAILED' | 'SUCCESS' | string;
}

export const StatusPill: React.FC<StatusPillProps> = ({ status }) => {
  const getStatusClasses = () => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-500 dark:bg-yellow-600';
      case 'FAILED':
        return 'bg-red-400 dark:bg-red-600';
      case 'SUCCESS':
        return 'bg-green-400 dark:bg-green-600';
      default:
        return 'bg-gray-300 dark:bg-gray-700';
    }
  };

  return (
    <div
      className={cn(
        'h-[30px] rounded-full px-4 flex items-center justify-center',
        getStatusClasses()
      )}
    >
      <span className="text-white font-bold text-sm">
        {formatEnumLabel(status || '')}
      </span>
    </div>
  );
};
