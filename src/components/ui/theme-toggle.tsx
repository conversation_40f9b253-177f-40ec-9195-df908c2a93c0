'use client';

import * as React from 'react';
import { useTheme } from 'next-themes';
import { Image } from '@/components/ui';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown';

export const ModeToggle = ({ className }: { className?: string }) => {
  const { setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className={className}>
          <Image
            src={'/svgs/sun.svg'}
            width={24}
            height={24}
            alt="Light Mode"
            className="size-6 rotate-0 scale-100 transition-transform duration-500 dark:-rotate-90 dark:scale-0"
          />

          <Image
            src={'/svgs/moon.svg'}
            width={24}
            height={24}
            alt="Dark Mode"
            className="absolute size-6 rotate-90 scale-0 transition-transform duration-500 dark:rotate-0 dark:scale-100"
          />

          <span className="sr-only">Toggle theme</span>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme('light')}>
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('system')}>
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

import { cn } from '@/lib/utils';
import { Small } from '@/components/ui/typography';
interface ThemeToggleSwitchProps {
  className?: string;
  label?: string;
  showLabel?: boolean;
  showIcon?: boolean;
}

export function ThemeToggleSwitch({
  className,
  showLabel = false,
  showIcon = true,
}: ThemeToggleSwitchProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();

  const isDarkMode =
    theme === 'dark' || (theme === 'system' && resolvedTheme === 'dark');

  const handleToggle = (checked: boolean) => {
    setTheme(checked ? 'dark' : 'light');
  };

  return (
    <button
      className={cn('flex items-center gap-2 cursor-pointer', className)}
      onClick={() => handleToggle(!isDarkMode)}
    >
      {showIcon &&
        (isDarkMode ? (
          <Image
            src={'/svgs/moon.svg'}
            width={24}
            height={24}
            alt="Dark Mode"
          />
        ) : (
          <Image
            src={'/svgs/sun.svg'}
            width={24}
            height={24}
            alt="Light Mode"
          />
        ))}
      {showLabel && <Small themed>{isDarkMode ? 'Dark' : 'Light'}</Small>}
    </button>
  );
}
