import React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Typography variants using CVA
export const typographyVariants = cva('font-aeonik', {
  variants: {
    variant: {
      // Display variants
      d1: 'text-3xl/[1.2] md:text-4xl/[1.2] lg:text-[64px]/[1.2] font-bold',
      d2: 'text-3xl/[1.2] font-bold',

      // Heading variants
      h1: 'text-3xl/[1.2] font-bold',
      h2: 'text-2xl/[1.2] font-bold',
      h3: 'text-lg/[1.2] font-bold',
      h4: 'text-base/[1.2] font-bold',
      h5: 'text-sm/[1.2] font-bold',
      h6: 'text-xs/[1.2] font-bold',

      // Body text variants
      p: 'text-base/normal',
      large: 'text-lg/normal',
      medium: 'text-base/normal',
      small: 'text-sm/normal',
      tiny: 'text-xs/normal',

      // Label variants
      'lg-bold': 'text-lg leading-[18px] font-bold',
      'lg-regular': 'text-lg leading-[18px]',
      'md-bold': 'text-base leading-[16px] font-bold',
      'md-regular': 'text-base leading-[16px]',
      'sm-bold': 'text-sm leading-[14px] font-bold',
      'sm-regular': 'text-sm leading-[14px]',
      'xs-bold': 'text-xs leading-[12px] font-bold',
      'xs-regular': 'text-xs leading-[12px]',
    },
    weight: {
      light: 'font-light',
      regular: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
    },
    themed: {
      false: 'text-white',
      true: 'text-fg-base-light dark:text-fg-base-dark',
    },
  },
  defaultVariants: {
    variant: 'p',
    weight: 'medium',
    themed: true,
  },
});

export interface TypographyProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof typographyVariants> {
  asChild?: boolean;
  as?: React.ElementType;
}

export const Typography = React.forwardRef<
  HTMLElement,
  TypographyProps & { [key: string]: any }
>(
  (
    {
      className,
      variant,
      weight,
      themed = true,
      asChild = false,
      as,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : as || getDefaultElement(variant);

    return (
      <Comp
        className={cn(
          typographyVariants({ themed, variant, weight, className })
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Typography.displayName = 'Typography';

// Helper function to get default HTML element based on variant
function getDefaultElement(
  variant: string | null | undefined
): React.ElementType {
  switch (variant) {
    case 'd1':
    case 'd2':
    case 'h1':
      return 'h1';
    case 'h2':
      return 'h2';
    case 'h3':
      return 'h3';
    case 'h4':
      return 'h4';
    case 'h5':
      return 'h5';
    case 'h6':
      return 'h6';
    case 'p':
    case 'large':
    case 'medium':
    case 'small':
    case 'tiny':
      return 'p';
    case 'lg-bold':
    case 'lg-regular':
    case 'md-bold':
    case 'md-regular':
    case 'sm-bold':
    case 'sm-regular':
    case 'xs-bold':
    case 'xs-regular':
      return 'span';
    default:
      return 'p';
  }
}

// Convenience components for better developer experience
export const D1 = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="d1" as="h1" {...props} />);
D1.displayName = 'D1';

export const D2 = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="d2" as="h1" {...props} />);
D2.displayName = 'D2';

export const H1 = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="h1" as="h1" {...props} />);
H1.displayName = 'H1';

export const H2 = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="h2" as="h2" {...props} />);
H2.displayName = 'H2';

export const H3 = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="h3" as="h3" {...props} />);
H3.displayName = 'H3';

export const H4 = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="h4" as="h4" {...props} />);
H4.displayName = 'H4';

export const H5 = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="h5" as="h5" {...props} />);
H5.displayName = 'H5';

export const H6 = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="h6" as="h6" {...props} />);
H6.displayName = 'H6';

export const P = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="p" as="p" {...props} />);
P.displayName = 'P';

export const Large = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="large" as="p" {...props} />);
Large.displayName = 'Large';

export const Medium = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="medium" as="p" {...props} />);
Medium.displayName = 'Medium';

export const Small = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="small" as="p" {...props} />);
Small.displayName = 'Small';

export const Tiny = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => <Typography ref={ref} variant="tiny" as="p" {...props} />);
Tiny.displayName = 'Tiny';

export const LgBoldLabel = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => (
  <Typography ref={ref} variant="lg-bold" as="span" {...props} />
));
LgBoldLabel.displayName = 'LgBoldLabel';

export const LgRegularLabel = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => (
  <Typography ref={ref} variant="lg-regular" as="span" {...props} />
));
LgRegularLabel.displayName = 'LgRegularLabel';

export const MdBoldLabel = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => (
  <Typography ref={ref} variant="md-bold" as="span" {...props} />
));
MdBoldLabel.displayName = 'MdBoldLabel';

export const MdRegularLabel = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => (
  <Typography ref={ref} variant="md-regular" as="span" {...props} />
));
MdRegularLabel.displayName = 'MdRegularLabel';

export const SmBoldLabel = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => (
  <Typography ref={ref} variant="sm-bold" as="span" {...props} />
));
SmBoldLabel.displayName = 'SmBoldLabel';

export const SmRegularLabel = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => (
  <Typography ref={ref} variant="sm-regular" as="span" {...props} />
));
SmRegularLabel.displayName = 'SmRegularLabel';

export const XsBoldLabel = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => (
  <Typography ref={ref} variant="xs-bold" as="span" {...props} />
));
XsBoldLabel.displayName = 'XsBoldLabel';

export const XsRegularLabel = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, 'variant'>
>((props, ref) => (
  <Typography ref={ref} variant="xs-regular" as="span" {...props} />
));
XsRegularLabel.displayName = 'XsRegularLabel';
