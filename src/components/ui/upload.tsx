'use client';
import { Image } from '@/components/ui';
import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';

type UploadComponentProps = {
  onFileUpload: (files: File[]) => void;
  type?: 'video' | 'image';
  value?: File | any;
  className?: string;
};

export const UploadComponent: React.FC<UploadComponentProps> = ({
  onFileUpload,
  type,
  value,
  className,
}) => {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      onFileUpload(acceptedFiles);
    },
    [onFileUpload]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept:
      type === 'video'
        ? {
            'video/mp4': ['.mp4', '.MP4'],
          }
        : { 'image/png': ['.png'], 'image/jpeg': ['.jpg', '.jpeg'] },
    multiple: false,
  });

  return (
    <div className={`flex flex-col items-center md:items-start ${className}`}>
      <div
        {...getRootProps()}
        className="relative flex flex-col items-center justify-center border-2 w-[210px] h-[280px] border-gray-200 p-6 rounded-lg cursor-pointer bg-[#FFFFFF] transition duration-300"
      >
        <input {...getInputProps()} />
        {value && (
          <>
            {value?.type === 'video/mp4' ? (
              <video
                src={
                  typeof value !== 'string' && value?.type === 'video/mp4'
                    ? URL?.createObjectURL(value)
                    : value
                }
                controls
                className="w-full max-w-md"
              />
            ) : (
              <Image
                src={
                  typeof value !== 'string'
                    ? URL.createObjectURL(value)
                    : `${value}?${new Date().getTime()}`
                }
                alt=""
                className="w-full h-full object-cover"
                width={200}
                height={200}
              />
            )}
          </>
        )}
        <p className="text-primary text-3xl">+</p>

        <div className="absolute bottom-4">
          {type === 'image' ? (
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 24H24V0H0V24ZM22.2222 17.119L16.0349 10.9316L18.6238 8.34272L22.2222 11.9408V17.119ZM1.77778 1.77778H22.2222V9.42667L18.6239 5.82833L14.7778 9.67456L9.22222 4.119L1.77778 11.5634V1.77778ZM1.77778 14.0778L9.22222 6.63333L22.2222 19.6333V22.2222H1.77778V14.0778Z"
                fill="#71717A"
              />
            </svg>
          ) : (
            <svg
              width="30"
              height="30"
              viewBox="0 0 30 30"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15 30C23.2843 30 30 23.2843 30 15C30 6.71573 23.2843 0 15 0C6.71573 0 0 6.71573 0 15C0 23.2843 6.71573 30 15 30ZM21.0947 15.7869C21.7197 15.4376 21.7197 14.5642 21.0947 14.2149L12.6572 9.49887C12.0322 9.14953 11.251 9.5862 11.251 10.2849V19.717C11.251 20.4156 12.0322 20.8523 12.6572 20.503L21.0947 15.7869Z"
                fill="#71717A"
              />
            </svg>
          )}
        </div>

        {/* <p className='mt-2 text-gray-400'>Maximum file size: 5MB</p> */}
      </div>
    </div>
  );
};
