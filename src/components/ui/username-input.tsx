'use client';

import { FloatingInput } from '@/components/ui/floating-input';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Spinner } from '@/components/ui/spinner';
import { P } from '@/components/ui/typography';
import { AtSign } from 'lucide-react';
import React from 'react';
import type { Control, FieldPath, FieldValues } from 'react-hook-form';

import { useWatch } from 'react-hook-form';

interface UsernameFieldProps<T extends FieldValues> {
  control: Control<T>;
  name: FieldPath<T>;
  isValidating?: boolean;
  onValidationStart?: () => void;
  onUsernameValidationInvalidate?: () => void;
  label?: string;
  className?: string;
}

export const UsernameField = <T extends FieldValues>({
  control,
  name,
  isValidating = false,
  onValidationStart,
  onUsernameValidationInvalidate,
  label = 'Username',
}: UsernameFieldProps<T>) => {
  const username = useWatch({ control, name });

  const UsernameStatusIndicator = () => {
    if (!username) return null;

    return (
      <div className="flex flex-row items-center">
        {isValidating ? (
          <Spinner size="small" className="text-brand-60" />
        ) : (
          <P>{/* You can replace this with actual validity logic */}</P>
        )}
        <P className="ml-1 text-grey-50 dark:text-grey-60">
          {isValidating ? 'Checking username' : 'Username status not validated'}
        </P>
      </div>
    );
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <FloatingInput
              type="text"
              className="pl-12"
              labelClassName="start-12"
              label={label}
              prefixIcon={
                <AtSign className="peer-focus:text-primary dark:peer-focus:text-primary" />
              }
              {...field}
              onChange={(e) => {
                onValidationStart?.();
                onUsernameValidationInvalidate?.();
                field.onChange(e);
              }}
            />
          </FormControl>
          <UsernameStatusIndicator />
          {!username && <FormMessage />}
        </FormItem>
      )}
    />
  );
};
