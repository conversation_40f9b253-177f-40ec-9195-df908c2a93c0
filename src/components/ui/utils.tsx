import { AxiosError } from 'axios';
import toast from 'react-hot-toast';

/**
 * Show a formatted error message from an Axios error object.
 * @param error - Axios error object from a failed HTTP request
 */
export const showError = (error: AxiosError) => {
  console.error(error?.response?.data);
  const description = extractError(error?.response?.data).trimEnd();

  toast(`Error: ${description}`);
};

/**
 * Show a generic error message.
 * @param message - Optional custom error message
 */
export const showErrorMessage = (message = 'Something went wrong') => {
  toast(message);
};

/**
 * Recursively extract and format error messages from API response data.
 * Supports string, arrays, and objects with nested messages.
 * @param data - The error payload from the API response
 * @returns A formatted string containing all error messages
 */
export const extractError = (data: unknown): string => {
  if (typeof data === 'string') {
    return data;
  }
  if (Array.isArray(data)) {
    return data.map((item) => `  ${extractError(item)}`).join('');
  }
  if (typeof data === 'object' && data !== null) {
    return Object.entries(data)
      .map(([key, value]) => {
        const separator = Array.isArray(value) ? ':\n ' : ': ';
        return `- ${key}${separator}${extractError(value)}\n`;
      })
      .join('');
  }
  return 'Something went wrong';
};
