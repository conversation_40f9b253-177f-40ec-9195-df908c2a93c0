'use client';
import React from 'react';
import { format } from 'date-fns';
import Link from 'next/link';

import {
  colors,
  FundIcon,
  LiveIcon,
  MdRegularLabel,
  SmRegularLabel,
  TicketIcon,
} from '@/components/ui';
import { cn, formatAmount, formatEnumLabel, toAmountInMajor } from '@/lib';
import { type Transaction } from '@/types';
import { IoWalletOutline } from 'react-icons/io5';
import { useTheme } from 'next-themes';
import { FiArrowUpRight } from 'react-icons/fi';

interface TransactionHistoryItemProps {
  item: Transaction;
}

export const TransactionHistoryItem: React.FC<TransactionHistoryItemProps> = ({
  item,
}) => {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const IconComponent = () => {
    switch (item.category) {
      case 'FUND_WALLET':
        return (
          <IoWalletOutline
            size={16}
            color={isDark ? colors.brand[40] : colors.brand[70]}
          />
        );
      case 'SONG_REQUEST':
        return <LiveIcon height={16} color={colors.brand[70]} />;
      case 'WITHDRAW_WALLET':
        return (
          <FiArrowUpRight
            size={16}
            color={isDark ? colors.brand[40] : colors.brand[70]}
          />
        );
      case 'EVENT_TICKET_PURCHASE':
      case 'EVENT_TICKET_EARNINGS':
        return <TicketIcon color={colors.brand[70]} />;
      case 'DJ_SESSION_EARNINGS':
        return <LiveIcon height={16} color={colors.brand[70]} />;
      default:
        return <FundIcon color={colors.brand[70]} />;
    }
  };

  return (
    <Link href={`/wallet/${item.id}`} className="block">
      <button className="flex h-16 w-full flex-row items-center justify-between hover:bg-grey-10 dark:hover:bg-grey-90 rounded-lg px-2 transition cursor-pointer">
        <div className="flex flex-1 flex-row items-center gap-4">
          <div className="flex size-8 items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90">
            <IconComponent />
          </div>
          <div className="flex flex-col gap-1 items-start justify-start">
            <MdRegularLabel>
              {item.category === 'DJ_SESSION_EARNINGS'
                ? 'Live Session Earnings'
                : item.category === 'SONG_REQUEST'
                  ? 'Live Request'
                  : item.category === 'WITHDRAW_WALLET'
                    ? 'Wallet Withdrawal'
                    : item.category === 'FUND_WALLET'
                      ? 'Wallet Funding'
                      : formatEnumLabel(item.category)}
            </MdRegularLabel>
            <SmRegularLabel className="text-grey-60 dark:text-grey-50">
              {format(item.createdAt || '', 'yyyy-MM-dd HH:mm')}
            </SmRegularLabel>
          </div>
        </div>

        <div className="flex flex-col items-end gap-1">
          <SmRegularLabel>
            {item.type === 'CREDIT' ? '+' : '-'}{' '}
            {formatAmount(
              toAmountInMajor(item.amount),
              undefined,
              item.currency
            )}
          </SmRegularLabel>
          <SmRegularLabel
            className={cn(
              item.status === 'SUCCESS'
                ? 'text-green-60 dark:text-green-40'
                : 'text-red-60 dark:text-red-40'
            )}
          >
            {formatEnumLabel(item.status)}
          </SmRegularLabel>
        </div>
      </button>
    </Link>
  );
};
