import { USER_ROLE_VALUES } from '@/api';

export const SUPPORTED_COUNTRIES = [
  {
    label: 'Nigeria',
    value: 'NIGERIA',
    code: 'ng',
    coordinates: { latitude: 9.5132431, longitude: 8.0894561 },
  },
  {
    label: 'United Kingdom',
    value: 'UNITED KINGDOM',
    code: 'gb',
    coordinates: { latitude: 55.3781, longitude: -3.436 },
  },
];

export const USER_ROLE_CARDS = [
  {
    id: USER_ROLE_VALUES[0],
    icon: '/svgs/happy-people.svg',
    title: "I'm Here to Explore",
    description:
      'Just here for the vibes and to explore countless sounds and activities anywhere, anytime',
  },
  {
    id: USER_ROLE_VALUES[1],
    icon: '/svgs/headphone.svg',
    title: "I'm a Creator",
    description:
      'Specially made for Disc Jockeys, Bands, Artistes and other creatives',
  },
];

export const GENRES = [
  {
    id: 'afrobeats',
    name: 'Afrobeats',
    image: '/images/genres/afrobeats.png',
  },
  {
    id: 'hiphop',
    name: 'HipHop',
    image: '/images/genres/hiphop.png',
  },
  {
    id: 'rave',
    name: 'Rave',
    image: '/images/genres/rave.png',
  },
  {
    id: 'concerts',
    name: 'Concerts',
    image: '/images/genres/concerts.png',
  },
  {
    id: 'pop',
    name: 'Pop',
    image: '/images/genres/pop.png',
  },
  {
    id: 'gospel',
    name: 'Gospel',
    image: '/images/genres/gospel.png',
  },
  {
    id: 'festival',
    name: 'Festival',
    image: '/images/genres/festival.png',
  },
  {
    id: 'podcast',
    name: 'Podcast',
    image: '/images/genres/podcast.png',
  },
];

export const accountTypes = [
  {
    id: 'quester',
    type: 'Quester',
    title: 'Join as a User',
    desc: 'Explore countless sounds and activities anywhere, anytime',
    image: '/images/account/quester.png',
  },
  {
    id: 'dj',
    type: 'CREATOR',
    title: 'Join as a Creator',
    desc: 'Go Live, accept song request, create events and more...',
    image: '/images/account/dj.png',
  },
];

export const NAV_SECTIONS: SidebarSection[] = [
  {
    items: [
      // { title: 'Home', href: '/home', Icon: '/svgs/tabs/home.svg' },
      { title: 'Events', href: '/events', Icon: '/svgs/tabs/event.svg' },
      // { title: 'Chats', href: '/chats', Icon: '/svgs/tabs/chat.svg' },
      {
        title: 'Favourites',
        href: '/favourites',
        Icon: '/svgs/tabs/favourite.svg',
      },
      { title: 'Tickets', href: '/tickets', Icon: '/svgs/tabs/ticket.svg' },
      { title: 'Wallet', href: '/wallet', Icon: '/svgs/tabs/wallet.svg' },
      { title: 'Profile', href: '/profile', Icon: '/svgs/tabs/profile.svg' },
    ],
  },
];

export const FOLLOWING_USERS: FollowingUser[] = [
  {
    id: '1',
    avatar: '/images/users/dj-aisha.png',
    displayName: 'Fox Alexis',
    username: 'djfoxxy',
  },
  {
    id: '2',
    avatar: '/images/users/dj-aisha.png',
    displayName: 'Ed Trey',
    username: 'djed',
  },
  {
    id: '3',
    avatar: '/images/users/dj-aisha.png',
    displayName: 'Dave',
    username: 'djdave',
  },
  {
    id: '4',
    avatar: '/images/users/dj-aisha.png',
    displayName: 'Janet',
    username: 'djjezzy',
  },
  {
    id: '5',
    avatar: '/images/users/dj-aisha.png',
    displayName: 'Oxygen',
    username: 'djoxygen',
  },
  {
    id: '6',
    avatar: '/images/users/dj-aisha.png',
    displayName: 'Dope Czar',
    username: 'djczar',
  },
  {
    id: '7',
    avatar: '/images/users/dj-aisha.png',
    displayName: 'Slim Boi',
    username: 'slimboi',
  },
];

import type { ReportType } from '@/types';

export const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

export const defaultLocationObject = {
  coordinates: {
    lat: 0,
    lng: 0,
    latitude: 0,
    longitude: 0,
  },
  city: 'N/A',
  state: 'N/A',
  street: 'N/A',
  country: 'N/A',
  address: 'N/A',
  landmark: 'N/A',
};

export const DEFAULT_COORDINATES = SUPPORTED_COUNTRIES.reduce<
  Record<string, { latitude: number; longitude: number }>
>((acc, country) => {
  if (country.coordinates) {
    acc[country.value] = country.coordinates;
  }
  return acc;
}, {});

export const CREATOR_COMPLETION_CARDS = [
  // {
  //   id: 'preferences',
  //   imageSource: '/icons/onboarding/add.png',
  //   title: 'Tailor your vibe',
  //   description:
  //     'Tell us what interests you so we can customise your experience.',
  // },
  {
    id: 'category',
    imageSource: '/icons/onboarding/type.png',
    title: 'Select creator type',
    description: 'Let everyone know the type of creator you are.',
  },
  {
    id: 'bio',
    imageSource: '/icons/onboarding/bio.png',
    title: 'Add a bio',
    description: 'Write a short bio about yourself.',
  },
  {
    id: 'avatar',
    imageSource: '/icons/onboarding/avatar.png',
    title: 'Set a profile picture',
    description: 'Let everyone know it is you.',
  },
  {
    id: 'inspirations',
    imageSource: '/icons/onboarding/add.png',
    title: 'Favourite popular accounts',
    description: 'Get inspired by favouriting popular accounts.',
  },
];

export const QUESTER_COMPLETION_CARDS = [
  // {
  //   id: 'preferences',
  //   imageSource: '/icons/onboarding/add.png'),
  //   title: 'Tailor your vibe',
  //   description:
  //     'Tell us what interests you so we can customise your experience.',
  // },
  {
    id: 'inspirations',
    imageSource: '/icons/onboarding/add.png',
    title: 'Favourite popular accounts',
    description: 'Get inspired by favouriting popular accounts.',
  },
  {
    id: 'bio',
    imageSource: '/icons/onboarding/bio.png',
    title: 'Add a bio',
    description: 'Write a short bio about yourself.',
  },
  {
    id: 'avatar',
    imageSource: '/icons/onboarding/avatar.png',
    title: 'Set a profile picture',
    description: 'Let everyone know it is you.',
  },
];

export const EVENT_TYPE_CARDS = [
  {
    id: 'PUBLIC' as const,
    icon: '/icons/event/public.png',
    title: 'Public event',
    description: 'Anyone can find and join your event.',
  },
  {
    id: 'PRIVATE' as const,
    icon: '/icons/event/private.png',
    title: 'Private event',
    description: 'Only people with a unique access code or link can join.',
  },
];

export const TICKET_TYPE_CARDS = [
  {
    id: 'PAID' as const,
    icon: '/icons/event/paid.png',
    title: 'Paid event',
    description: 'Set a price for tickets.',
  },
  {
    id: 'FREE' as const,
    icon: '/icons/event/free.png',
    title: 'Free event',
    description: 'No cost to attend.',
  },
];

export const EVENT_FORMAT_CARDS = [
  {
    id: 'IN_PERSON' as const,
    icon: '/icons/event/physical.png',
    title: 'In-Person',
    description: 'Hosted at a phyiscal location.',
  },
  {
    id: 'ONLINE' as const,
    icon: '/icons/event/online.png',
    title: 'Online',
    description: 'Hosted virtually.',
  },
  {
    id: 'HYBRID' as const,
    icon: '/icons/event/hybrid.png',
    title: 'Hybrid',
    description: 'Hosted in-person and virtually.',
  },
];

export const CREATOR_CATEGORY_VALUES = [
  'DJ',
  'Event Organizer',
  'Hype-person/MC',
  'Organization/Business',
  'Others',
] as const;

export const CREATOR_CATEGORY_CARDS = [
  {
    id: CREATOR_CATEGORY_VALUES[0],
    icon: '/images/categories/dj.png',
    title: 'DJ',
    description:
      'I mix tracks and perform live sets at events, clubs, or festivals.',
  },
  {
    id: CREATOR_CATEGORY_VALUES[1],
    icon: '/images/categories/organizer.png',
    title: 'Event Organizer',
    description: 'I organize live events and shows.',
  },
  {
    id: CREATOR_CATEGORY_VALUES[2],
    icon: '/images/categories/artiste.png',
    title: 'Hype-person/MC',
    description: 'I anchor events, clubs or festivals.',
  },
  {
    id: CREATOR_CATEGORY_VALUES[3],
    icon: '/images/categories/org.png',
    title: 'Organization/Business',
    description: 'I represent a corporate body.',
  },
  {
    id: CREATOR_CATEGORY_VALUES[4],
    icon: '/images/categories/others.png',
    title: 'Others',
  },
];

export const NOTIFICATION_TYPES = ['Email', 'Push'] as const;
export type NotificationOption = (typeof NOTIFICATION_TYPES)[number];

export const CHAT_OPTIONS = ['Online status', 'Last seen', 'Near me'] as const;
export type ChatOption = (typeof CHAT_OPTIONS)[number];

export const RECENT_SEARCHES_KEY = '@recent_searches';
export const MAX_RECENT_SEARCHES = 10;

export const CAROUSEL_INTERVAL = 3000; // Auto-play interval in ms

export const REPORT_OPTIONS: { label: string; value: ReportType }[] = [
  { label: 'Fake profile / spam', value: 'spam' },
  { label: 'Inappropriate content', value: 'inappropriate' },
  { label: 'Copyright infringement', value: 'copyright' },
  { label: 'Underage user', value: 'underage' },
];

export const mapCustomStyle = [
  { elementType: 'geometry', stylers: [{ color: '#242f3e' }] },
  { elementType: 'labels.text.fill', stylers: [{ color: '#746855' }] },
  { elementType: 'labels.text.stroke', stylers: [{ color: '#242f3e' }] },
  {
    featureType: 'administrative.locality',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }],
  },
  {
    featureType: 'poi',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }],
  },
  {
    featureType: 'poi.park',
    elementType: 'geometry',
    stylers: [{ color: '#263c3f' }],
  },
  {
    featureType: 'poi.park',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#6b9a76' }],
  },
  {
    featureType: 'road',
    elementType: 'geometry',
    stylers: [{ color: '#38414e' }],
  },
  {
    featureType: 'road',
    elementType: 'geometry.stroke',
    stylers: [{ color: '#212a37' }],
  },
  {
    featureType: 'road',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#9ca5b3' }],
  },
  {
    featureType: 'road.highway',
    elementType: 'geometry',
    stylers: [{ color: '#746855' }],
  },
  {
    featureType: 'road.highway',
    elementType: 'geometry.stroke',
    stylers: [{ color: '#1f2835' }],
  },
  {
    featureType: 'road.highway',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#f3d19c' }],
  },
  {
    featureType: 'transit',
    elementType: 'geometry',
    stylers: [{ color: '#2f3948' }],
  },
  {
    featureType: 'transit.station',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }],
  },
  {
    featureType: 'water',
    elementType: 'geometry',
    stylers: [{ color: '#17263c' }],
  },
  {
    featureType: 'water',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#515c6d' }],
  },
  {
    featureType: 'water',
    elementType: 'labels.text.stroke',
    stylers: [{ color: '#17263c' }],
  },
];
