import { FAQ_URL } from '@/lib/constants';
import { ProfileItem, ProfileSections } from '@/types/general.interface';

export const PROFILE_SECTIONS: ProfileSections = [
  {
    title: 'Account',
    items: [
      {
        icon: 'settings-outline',
        iconType: 'ionicons',
        label: 'Account settings',
        navigateTo: '/profile/account',
      },
      {
        icon: 'headset-outline',
        iconType: 'ionicons',
        label: 'Live session history',
        navigateTo: '/profile/session',
      },
    ],
  },
  {
    title: 'Finance',
    items: [
      {
        icon: 'wallet-outline',
        iconType: 'ionicons',
        label: 'Wallet',
        navigateTo: '/profile/wallet',
      },
    ],
  },
  {
    title: 'Preference',
    items: [
      {
        icon: 'notifications-outline',
        iconType: 'ionicons',
        label: 'Notification',
        navigateTo: '/profile/notification',
      },
      {
        icon: 'chatbubbles-outline',
        iconType: 'ionicons',
        label: 'Chat preferences',
        navigateTo: '/profile/chat',
      },
      // {
      //   icon: 'play-circle-outline',
      //   iconType: 'ionicons',
      //   label: 'Content preferences',
      //   navigateTo: '/profile/preferences',
      // },
      {
        icon: 'moon-outline',
        iconType: 'ionicons',
        label: 'Dark mode',
        navigateTo: '/profile',
        isToggle: true,
      },
    ],
  },
  {
    title: 'Support',
    items: [
      {
        icon: 'information-circle-outline',
        iconType: 'ionicons',
        label: 'About the app',
        navigateTo: '/profile/about',
      },
      {
        // use custom icon
        icon: 'Help',
        iconType: 'custom-svg',
        label: 'Help and support',
        navigateTo: '/profile',
        hasWebView: true,
        externalUrl: FAQ_URL,
      },
      {
        icon: 'document-text-outline',
        iconType: 'ionicons',
        label: 'Terms and conditions',
        navigateTo: '/profile/terms',
      },
      {
        icon: 'close-circle-outline',
        iconType: 'ionicons',
        label: 'Cancellation policy',
        navigateTo: '/profile/cancellation-policy',
      },
    ],
  },
  // {
  //   title: 'Stay in touch',
  //   items: [
  //     {
  //       // use custom icons
  //       icon: 'Instagram',
  //       iconType: 'custom-svg',
  //       label: 'Follow us on Instagram',
  //       navigateTo: '/profile',
  //       handleExternalLink: () =>
  //         openLinkInBrowser(IG_DEEP_LINK_URL, IG_SITE_URL),
  //     },
  //     {
  //       icon: 'X',
  //       iconType: 'custom-svg',
  //       label: 'Follow us on X',
  //       navigateTo: '/profile',
  //       handleExternalLink: () =>
  //         openLinkInBrowser(X_DEEP_LINK_URL, X_SITE_URL),
  //     },
  //     {
  //       icon: 'heart',
  //       iconType: 'ionicons',
  //       iconColor: '#FF5226',
  //       label: 'Rate this app',
  //       navigateTo: '/profile',
  //       handleExternalLink: () => openStoreLink(STORE_LINK),
  //     },
  //   ],
  // },
];

export type ColorSchemeType = 'light' | 'dark' | 'system';

export const LOGOUT_ITEM = (selectedTheme: ColorSchemeType): ProfileItem => ({
  icon: 'logout',
  iconType: 'material',
  iconColor: selectedTheme === 'dark' ? '#FF9175' : '#DB340B',
  label: 'Log out',
  navigateTo: '/profile/confirm-logout',
  textColor: 'text-red-60 dark:text-red-40',
  hideChevronForward: true,
});

export const DELETE_ACCOUNT_ITEM = (
  selectedTheme: ColorSchemeType
): ProfileItem => ({
  icon: 'trash',
  iconType: 'feather',
  iconColor: selectedTheme === 'dark' ? '#FF9175' : '#DB340B',
  label: 'Delete account',
  navigateTo: '/profile/account/confirm-delete-account',
  textColor: 'text-red-60 dark:text-red-40',
  hideChevronForward: true,
});
