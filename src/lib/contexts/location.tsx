'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';

import { type UserObjectData, type LocationObject } from '@/api/auth';
import { useUpdateProfile } from '@/api';
import { type LocationType } from '@/types';
import { useAuth } from '../hooks/use-auth';
import { DEFAULT_COORDINATES } from '../constants';
import { getLatLongFromCountry } from '../utils';

type LocationContextType = {
  location: LocationType | null;
  setLocation: (loc: LocationType | null) => void;
  getCurrentLocation: () => Promise<void>;
  locationAccessible: boolean;
};

const LocationContext = createContext<LocationContextType | undefined>(
  undefined
);

export const useLocation = () => {
  const ctx = useContext(LocationContext);
  if (!ctx) throw new Error('useLocation must be used within LocationProvider');
  return ctx;
};

type Props = {
  children: React.ReactNode;
  user?: UserObjectData;
};

export const LocationProvider: React.FC<Props> = ({ children, user }) => {
  const pathname = usePathname();
  const normalizedPathname =
    pathname === '/' ? '/' : pathname.replace(/\/$/, '');
  const enabledPaths = ['/', '/explore', '/explore/map-view'];
  const isEnabled = enabledPaths.includes(normalizedPathname);

  const queryClient = useQueryClient();
  const { status } = useAuth();
  const isAuthenticated = status === 'authenticated';

  const [location, setLocation] = useState<LocationType | null>(null);
  const [locationAccessible, setLocationAccessible] = useState(false);

  const { mutate: updateProfile } = useUpdateProfile({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getUser'] });
    },
  });

  const getCurrentLocation = async () => {
    if (!user || !isAuthenticated || !isEnabled) return;

    setLocationAccessible(false);

    if (!navigator.geolocation) {
      console.warn('Geolocation not supported by this browser');
      if (user.location) {
        const coords =
          DEFAULT_COORDINATES[user.location.country.toUpperCase()] ??
          (await getLatLongFromCountry(user.location.country));

        setLocation({
          ...user.location,
          coordinates: {
            lat: coords?.latitude || user.location.coordinates.lat,
            lng: coords?.longitude || user.location.coordinates.lng,
          },
        });
      }
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (pos) => {
        const { latitude, longitude } = pos.coords;

        const locationObj: LocationType = {
          coordinates: { lat: latitude, lng: longitude },
          landmark: '',
          city: '',
          state: '',
          street: '',
          country: '',
          address: '',
        };

        try {
          // OpenStreetMap reverse geocoding
          const res = await fetch(
            `https://nominatim.openstreetmap.org/reverse?lat=${latitude}&lon=${longitude}&format=json`
          );
          const data = await res.json();

          if (data) {
            locationObj.city = data.address.city || data.address.town || 'N/A';
            locationObj.state = data.address.state || 'N/A';
            locationObj.country = data.address.country || 'N/A';
            locationObj.street = data.address.road || 'N/A';
            locationObj.landmark = data.name || data.address.road || 'N/A';
            locationObj.address =
              data.display_name || data.address.road || 'N/A';

            updateProfile({
              userId: user.id,
              payload: { location: locationObj as LocationObject },
            });
          }
        } catch (err) {
          console.error('Reverse geocoding error:', err);
        } finally {
          setLocation(locationObj);
          setLocationAccessible(true);
        }
      },
      (err) => {
        console.error('Geolocation error:', err);
        if (user.location) setLocation(user.location);
      }
    );
  };

  // Get location on mount or when path changes
  useEffect(() => {
    if (isEnabled) {
      getCurrentLocation();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isEnabled]);

  // Refetch when tab regains focus
  useEffect(() => {
    if (!isEnabled) return;
    const handleFocus = () => getCurrentLocation();
    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isEnabled]);

  return (
    <LocationContext.Provider
      value={{ location, setLocation, getCurrentLocation, locationAccessible }}
    >
      {children}
    </LocationContext.Provider>
  );
};
