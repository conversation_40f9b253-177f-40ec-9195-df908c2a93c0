'use client';
import { createContext, useContext, useState } from 'react';

interface AppSessionContextType {
  hideTodo: boolean;
  setHideTodo: React.Dispatch<React.SetStateAction<boolean>>;
}

const AppSessionContext = createContext<AppSessionContextType | undefined>(
  undefined
);

import { ReactNode } from 'react';

export const AppSessionProvider = ({ children }: { children: ReactNode }) => {
  const [hideTodo, setHideTodo] = useState(false);

  return (
    <AppSessionContext.Provider value={{ hideTodo, setHideTodo }}>
      {children}
    </AppSessionContext.Provider>
  );
};

export const useAppSession = () => {
  const context = useContext(AppSessionContext);
  if (!context) {
    throw new Error('useAppSession must be used within an AppSessionProvider');
  }
  return context;
};
