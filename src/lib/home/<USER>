import { type UserObjectData } from '@/api/auth';
import {
  type LiveSessionResponse,
  type TrendingDjResponse,
} from '@/api/session';

import { removeDuplicatesById } from '../utils';

export const dummySessionDataForAddingLiveSession: (
  user: UserObjectData,
  activeSession?: LiveSessionResponse
) => LiveSessionResponse = (user, activeSession) => ({
  id: activeSession?.id || '',
  cost: 0,
  title: '',
  status: activeSession ? activeSession.status : 'ENDED',
  dj: {
    fullName: activeSession ? 'You' : 'Go Live',
    username: activeSession ? 'You' : 'Go Live',
    id: '',
    profileImageUrl: user.profileImageUrl,
  },
  bannerUrl: user.profileImageUrl ?? '',
  createdAt: '',
  distance: '',
  location: {
    city: '',
    state: '',
    street: '',
    address: '',
    country: '',
    coordinates: {
      lat: 0,
      lng: 0,
      latitude: 0,
      longitude: 0,
    },
    landmark: '',
  },
  sessionId: activeSession?.id,
});

export const getCombinedSessionAndDjSessionsList = ({
  user,
  trendingDJs,
  djSessions,
  activeSession,
}: {
  user: UserObjectData;
  trendingDJs: TrendingDjResponse[];
  djSessions: LiveSessionResponse[];
  activeSession: string;
  activeSessionCreator: string;
}): (
  | LiveSessionResponse
  | {
      id: string;
      name: string;
      avatar: string;
    }
)[] => {
  const modifiedTrendingDjs =
    user.role === 'CREATOR'
      ? [
          ...(removeDuplicatesById<TrendingDjResponse>(trendingDJs)
            .filter(
              (trendingDj: TrendingDjResponse) => trendingDj.id !== user.id
            )
            .map((trendingDj: TrendingDjResponse) => ({
              id: trendingDj.id,
              profileImageUrl: trendingDj.profileImageUrl,
              fullName: trendingDj.fullName,
              username: trendingDj.username,
              questerCount: 0,
            })) as TrendingDjResponse[]),
        ]
      : (removeDuplicatesById(trendingDJs).map(
          (trendingDj: TrendingDjResponse) => ({
            id: trendingDj.id,
            profileImageUrl: trendingDj.profileImageUrl,
            fullName: trendingDj.fullName,
            username: trendingDj.username,
            questerCount: 0,
          })
        ) as TrendingDjResponse[]);

  const transformedTrendingDjs = modifiedTrendingDjs.map(
    (item: TrendingDjResponse) => ({
      id: item.id,
      name: item.username,
      avatar: item.profileImageUrl,
    })
  );

  const maxSessions = 12;
  // const dummySessionToAdd = dummySessionDataForAddingLiveSession(
  //   user,
  //   djSessions.find(
  //     (session) =>
  //       session.id === activeSession && session.dj.username === user.username
  //   )
  // );
  const liveSessions = djSessions.filter(
    (session) =>
      session.dj.username !== user.username || session.id !== activeSession
  );
  const remainingSlots = maxSessions - 1 - liveSessions.length;
  const trendingDjsToFill = transformedTrendingDjs.slice(0, remainingSlots);

  const combinedFlatlistData = [
    // ...(user.role === 'CREATOR' &&
    // !!activeSession &&
    // activeSessionCreator === user.username
    //   ? [dummySessionToAdd]
    //   : []),
    ...liveSessions,
    ...trendingDjsToFill,
  ].slice(0, maxSessions);

  return combinedFlatlistData;
};
