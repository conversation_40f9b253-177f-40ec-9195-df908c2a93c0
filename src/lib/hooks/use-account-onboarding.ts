'use client';
import { apiVerifyUsername } from '@/api';
import {
  createFormStep,
  MultistepFormStep,
} from '@/lib/hooks/use-multi-step-form';
import {
  useRefinement,
  type RefinementCallback,
} from '@/lib/hooks/use-refinement';
import { USER_ROLE_VALUES } from '@/api';
import { NO_SPACE_REGEX, USERNAME_REGEX } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

export const useAccountOnboarding = () => {
  const [isValidatingUsername, setIsValidatingUsername] = useState(false);

  const checkUsernameToBeValid = (): RefinementCallback<string> => {
    return async (username: string, { signal }) => {
      if (!username) return false;

      setIsValidatingUsername(true);

      try {
        await apiVerifyUsername(username.trim().toLowerCase(), signal);

        return true;
      } catch (err: any) {
        if (err.name === 'AbortError') {
          // Request was aborted, don't treat as validation failure
          console.log('Username validation aborted');
          return true;
        }
        const error = err as AxiosError<CustomError>;
        console.log('Username validation error:', error.response?.data.message);
        return false;
      } finally {
        setIsValidatingUsername(false);
      }
    };
  };

  const usernameRefinement = useRefinement<string>(checkUsernameToBeValid(), {
    debounce: 500,
  });

  const onboardingSchema = z.object({
    firstName: z.string().min(2, 'First name must be at least 2 characters'),
    lastName: z.string().min(2, 'Last name must be at least 2 characters'),
    username: z
      .string()
      .regex(NO_SPACE_REGEX, 'Spaces are not allowed in the username')
      .regex(
        USERNAME_REGEX.noStartEndDotUnderscore,
        'Username cannot start or end with a dot or underscore'
      )
      .regex(
        USERNAME_REGEX.startWithAlphanumeric,
        'Username must start with an alphanumeric character'
      )
      .regex(
        USERNAME_REGEX.validCharacters,
        'Username can only contain alphanumeric characters, dots, or underscores'
      )
      .transform((value) => value.trim().toLowerCase())
      .refine(usernameRefinement, {
        message: 'Username is already taken',
        params: { asyncValidation: true },
      }),
    country: z.string().min(1, 'Please select a country'),
    genres: z.array(z.string()).optional(),
    role: z.enum(USER_ROLE_VALUES),
  });

  const personalInfoSchema = onboardingSchema.pick({
    firstName: true,
    lastName: true,
    country: true,
    username: true,
  });

  const genreSchema = onboardingSchema.pick({
    genres: true,
  });

  const onboardingSteps: MultistepFormStep<OnboardingFormValues>[] = [
    createFormStep({
      id: 0,
      title: 'Tell us about you',
      schema: personalInfoSchema,
      showSkip: false,
    }),
    createFormStep({
      id: 1,
      title: 'What’s your vibe?',
      subTitle:
        'Tell us what you love to discover events and artists tailored just for you.',
      schema: genreSchema,
      showSkip: true,
    }),
  ];

  type OnboardingFormValues = z.infer<typeof onboardingSchema>;

  const formMethods = useForm<OnboardingFormValues>({
    defaultValues: {
      firstName: '',
      lastName: '',
      country: '',
    },
    resolver: zodResolver(onboardingSchema),
    mode: 'onChange',
  });

  return {
    formMethods,
    usernameRefinement,
    onboardingSchema,
    personalInfoSchema,
    genreSchema,
    onboardingSteps,
    isValidatingUsername,
    setIsValidatingUsername,
  };
};

export type OnboardingFormValues = z.infer<
  ReturnType<typeof useAccountOnboarding>['onboardingSchema']
>;
export type PersonalInfoOnboardingFormValues = z.infer<
  ReturnType<typeof useAccountOnboarding>['personalInfoSchema']
>;
export type GenreOnboardingFormValues = z.infer<
  ReturnType<typeof useAccountOnboarding>['genreSchema']
>;
