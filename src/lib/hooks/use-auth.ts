'use client';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useMutation } from '@tanstack/react-query';
import type { LoginPayload } from '@/api';
import { useRouter } from 'next/navigation';
import { getQueryClient } from '@/api';

export const useAuth = () => {
  const { data: session, status } = useSession();
  const queryClient = getQueryClient();
  const router = useRouter();

  const handleAuthError = (result: any) => {
    if (result?.error) throw new Error(result.error);
    return result;
  };

  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginPayload) =>
      handleAuthError(
        await signIn('credentials', { ...credentials, redirect: false })
      ),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['getUser'] }),
  });

  const socialLoginMutation = useMutation({
    mutationFn: async ({ type }: { type: 'google' | 'apple' }) =>
      handleAuthError(await signIn(type, { redirect: false })),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['getUser'] }),
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      await signOut({ redirect: false });
    },
    onSuccess: () => {
      queryClient.clear();
      router.push('/login');
    },
  });

  return {
    session,
    status,
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated',
    user: session?.user,
    accessToken: session?.accessToken,
    // login
    login: loginMutation.mutate,
    loginAsync: loginMutation.mutateAsync,
    isLoggingIn: loginMutation.isPending,
    loginError: loginMutation.error,
    // social login
    socialLogin: socialLoginMutation.mutate,
    socialLoginAsync: socialLoginMutation.mutateAsync,
    isSocialLoggingIn: socialLoginMutation.isPending,
    socialLoginError: socialLoginMutation.error,
    // logout
    logout: logoutMutation.mutate,
    logoutAsync: logoutMutation.mutateAsync,
    isLoggingOut: logoutMutation.isPending,
  };
};
