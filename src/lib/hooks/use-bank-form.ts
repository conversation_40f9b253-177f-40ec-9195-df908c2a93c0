'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useAuth } from './use-auth';
export const schema = z.object({
  bankName: z.string(),
  accountName: z.string(),
  bankCode: z.string(),
  accountNumber: z.string().regex(/^\d{10}$/, {
    message: 'Account number must be exactly 10 digits',
  }),
});

export function useBankSetupForm() {
  const { user: currentUser } = useAuth();
  const bankDetails = currentUser?.idVerificationData?.bank;
  const formMethods = useForm<z.infer<typeof schema>>({
    defaultValues: {
      bankName: bankDetails?.bankName || '',
      accountNumber: bankDetails?.accountNumber || '',
      bankCode: bankDetails ? '058' : '',
      ...(bankDetails && {
        accountName: bankDetails.fullName,
      }),
    },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  return {
    formMethods,
    schema,
    hasExistingBankDetails: !!bankDetails,
  };
}

export type BankFormType = z.infer<
  ReturnType<typeof useBankSetupForm>['schema']
>;
