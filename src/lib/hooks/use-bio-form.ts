'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

export function useBioForm() {
  const schema = z.object({
    bio: z.string(),
    gender: z.enum(['male', 'female', 'non-binary'], {
      message: 'Please select a gender',
    }),
  });

  const formMethods = useForm<z.infer<typeof schema>>({
    defaultValues: {
      bio: '',
    },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  return {
    formMethods,
    schema,
  };
}

export type BioFormType = z.infer<ReturnType<typeof useBioForm>['schema']>;
