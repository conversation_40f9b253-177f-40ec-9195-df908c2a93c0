'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

export function useCreateDiscountsForm() {
  const baseDiscountSchema = z.object({
    isEnabled: z.boolean(),
    code: z.string(),
    type: z.enum(['fixed', 'percentage']),
    value: z.coerce.number().min(0, 'Value must be a positive number'),
    isForAll: z.boolean(),
    startDatetime: z.date(),
    endDatetime: z.date(),
    limit: z.coerce.number().int().min(1, 'Limit must be at least 1'),
  });

  const discountWithTicketsSchema = baseDiscountSchema.extend({
    tickets: z.array(z.string()).min(1, 'At least one ticket is required'),
  });

  const discountWithoutTicketsSchema = baseDiscountSchema.extend({});

  const schema = z.discriminatedUnion('isForAll', [
    discountWithTicketsSchema.extend({ isForAll: z.literal(false) }),
    discountWithoutTicketsSchema.extend({ isForAll: z.literal(true) }),
  ]);

  const defaultDiscount = {
    isEnabled: false,
    code: '',
    type: 'fixed',
    value: '' as unknown as number,
    isForAll: false,
    tickets: [],
    startDatetime: new Date(),
    endDatetime: new Date(),
    limit: 0,
  };

  const formMethods = useForm<z.infer<typeof schema>>({
    // @ts-expect-error - abc
    resolver: zodResolver(schema),
    mode: 'onChange',
    defaultValues: {
      code: '',
      type: 'fixed',
      isForAll: true,
    },
  });

  return {
    formMethods,
    schema,
    defaultDiscount,
  };
}

export type CreateDiscountsFormType = z.infer<
  ReturnType<typeof useCreateDiscountsForm>['schema']
>;
