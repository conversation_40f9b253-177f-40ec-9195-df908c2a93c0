'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { type z } from 'zod';

import { eventCreateSchema, type ticketSchema } from '@/lib';

export function useCreateEventForm() {
  const defaultTicket: z.infer<typeof ticketSchema> = {
    id: Math.random()
      .toString(36)
      .substring(2, 15)
      .concat(Date.now().toString(36)),
    name: '',
    price: '' as unknown as number,
    quantity: '' as unknown as number,
    description: '',
    hasPresale: false,
    hasTimeline: false,
    hasPurchaseLimit: false,
    purchaseLimit: 0,
    startDatetime: undefined,
    endDatetime: undefined,
  };

  const formMethods = useForm<z.infer<typeof eventCreateSchema>>({
    defaultValues: {
      tickets: [defaultTicket],
      registrationFields: [],
      collaborators: [],
      onlineUrl: 'https://',
      timezone: 'W. Central Africa Standard Time',
    },
    // @ts-expect-error - abc
    resolver: zod<PERSON>esolver(eventCreateSchema),
    mode: 'onChange',
  });

  return {
    formMethods,
    schema: eventCreateSchema,
    defaultTicket,
  };
}

export type CreateEventFormType = z.infer<typeof eventCreateSchema>;
