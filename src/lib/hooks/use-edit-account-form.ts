'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { z } from 'zod';

import { apiVerifyUsername, useUpdateProfile } from '@/api';
import { useLoggedInUser } from '@/lib';

import {
  filterEmptyProperties,
  getNameParts,
  NO_SPACE_REGEX,
  USERNAME_REGEX,
} from '../utils';
import { useRefinement, type RefinementCallback } from './use-refinement';

export function useEditAccountForm() {
  const queryClient = useQueryClient();
  const { data: user } = useLoggedInUser();

  const router = useRouter();

  const [isValidatingUsername, setIsValidatingUsername] = useState(false);

  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(
    null
  );

  const checkUsernameToBeValid = (): RefinementCallback<string> => {
    return async (username: string, { signal }) => {
      if (!username) return false;

      setIsValidatingUsername(true);
      try {
        const res = await apiVerifyUsername(
          username.trim().toLowerCase(),
          signal
        );

        return res.data.data.username === username.trim().toLowerCase();
      } catch (err: any) {
        if (err.name === 'AbortError') return true;
        return false;
      } finally {
        setIsValidatingUsername(false);
      }
    };
  };

  const usernameRefinement = useRefinement<string>(checkUsernameToBeValid(), {
    debounce: 500,
  });

  const emptyOrUrl = (regex: RegExp, message: string, placeholder?: string) =>
    z
      .string()
      .refine((val) => !val || val === placeholder || regex.test(val), {
        message,
      })
      .optional();

  const schema = z.object({
    firstName: z.string(),
    lastName: z.string(),
    email: z.email('Invalid email format').nonempty('Email is required'),
    username: z
      .string()
      .min(3, 'Username must be at least 3 characters long')
      .regex(NO_SPACE_REGEX, 'Spaces are not allowed in the username')
      .regex(
        USERNAME_REGEX.noStartEndDotUnderscore,
        'Username cannot start or end with a dot or underscore'
      )
      .regex(
        USERNAME_REGEX.startWithAlphanumeric,
        'Username must start with an alphanumeric character'
      )
      .regex(
        USERNAME_REGEX.validCharacters,
        'Username can only contain alphanumeric characters, dots, or underscores'
      )
      .transform((value) => value.trim().toLowerCase())
      .refine(
        async (val) => {
          if (!val) return false;
          try {
            const res = await apiVerifyUsername(val.trim().toLowerCase());
            const available =
              res.data.data.username === val.trim().toLowerCase();
            setUsernameAvailable(available);
            return available;
          } catch {
            setUsernameAvailable(false);
            return false;
          }
        },
        { message: 'Username is already taken' }
      ),
    socials: z.object({
      instagram: emptyOrUrl(
        /^https?:\/\/(www\.)?instagram\.com\/[A-Za-z0-9_.]+(\?.*)?$/,
        'Must be a valid Instagram profile URL',
        'https://instagram.com/'
      ).optional(),

      x: emptyOrUrl(
        /^https?:\/\/(www\.)?x\.com\/[A-Za-z0-9_]+(\?.*)?$/,
        'Must be a valid X/Twitter profile URL',
        'https://x.com/'
      ).optional(),

      snapchat: emptyOrUrl(
        /^https?:\/\/(www\.)?snapchat\.com\/add\/[A-Za-z0-9_-]+(\?.*)?$/,
        'Must be a valid Snapchat profile URL',
        'https://snapchat.com/add/'
      ).optional(),

      facebook: emptyOrUrl(
        /^https?:\/\/(www\.)?facebook\.com\/[A-Za-z0-9.]+$/,
        'Must be a valid Facebook profile URL',
        'https://facebook.com/'
      ).optional(),

      tiktok: emptyOrUrl(
        /^https?:\/\/(www\.)?tiktok\.com\/@[\w.-]+(\?.*)?$/,
        'Must be a valid TikTok profile URL',
        'https://tiktok.com/@'
      ).optional(),

      youtube: emptyOrUrl(
        /^(https?:\/\/)?(www\.)?(youtube\.com\/(@|c\/|channel\/)?[A-Za-z0-9_-]+|youtu\.be\/[A-Za-z0-9_-]+)(\?.*)?$/,
        'Must be a valid YouTube channel URL',
        'https://youtube.com/'
      ).optional(),
    }),
  });

  type EditAccountSchema = z.infer<typeof schema>;

  const formMethods = useForm<z.infer<typeof schema>>({
    defaultValues: {
      firstName: getNameParts(user?.fullName).firstName,
      lastName: getNameParts(user?.fullName).lastName,
      email: user?.email,
      username: user?.username,
      socials: {
        instagram: user?.socials?.instagram || 'https://instagram.com/',
        x: user?.socials?.x || 'https://x.com/',
        snapchat: user?.socials?.snapchat || 'https://snapchat.com/add/',
        facebook: user?.socials?.facebook || 'https://facebook.com/',
        tiktok: user?.socials?.tiktok || 'https://tiktok.com/@',
        youtube: user?.socials?.youtube || 'https://youtube.com/',
      },
    },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  const { mutate: updateProfile, isPending: accountUpdating } =
    useUpdateProfile();
  const onSubmit: SubmitHandler<EditAccountSchema> = (data) => {
    const cleanSocials = Object.entries(data.socials).reduce(
      (acc, [key, value]) => {
        if (
          !value ||
          (key === 'instagram' && value === 'https://instagram.com/') ||
          (key === 'x' && value === 'https://x.com/') ||
          (key === 'snapchat' && value === 'https://snapchat.com/add/') ||
          (key === 'facebook' && value === 'https://facebook.com/') ||
          (key === 'tiktok' && value === 'https://tiktok.com/@') ||
          (key === 'youtube' && value === 'https://youtube.com/')
        ) {
          return acc;
        }
        acc[key] = value;
        return acc;
      },
      {} as Record<string, string>
    );
    const payload = filterEmptyProperties<Partial<EditAccountSchema>>({
      socials: cleanSocials,
      ...(data.username &&
        data.username !== user?.username && { username: data.username }),
    });

    console.log('edit profile', payload);
    updateProfile(
      { userId: user?.id || '', payload },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          toast.success('Profile updated successfully');
          router.back();
          queryClient.invalidateQueries({
            queryKey: ['getUser', { userCurrency: 'NGN' }],
          });
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  return {
    formMethods,
    usernameRefinement,
    schema,
    isValidatingUsername,
    setIsValidatingUsername,
    onSubmit,
    accountUpdating,
    usernameAvailable,
  };
}

export type EditAccountFormType = z.infer<
  ReturnType<typeof useEditAccountForm>['schema']
>;
