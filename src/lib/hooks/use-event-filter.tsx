'use client';
import { useState, useMemo, useCallback } from 'react';
import { AllEventsQueryParams, Point } from '@/api';
import { LocationType } from '@/types';
import dayjs from 'dayjs';
import { useAuth } from '@/lib';

const PAGE_SIZE = 12;

interface EventFilterState {
  categoryId: string;
  categoryName: string;
  currentPage: number;
  eventDate: string | null;
  location: Pick<AllEventsQueryParams, 'city' | 'country'> | null;
  coordinates: Pick<LocationType, 'coordinates'>['coordinates'] | null;
}

export const useEventFilters = () => {
  const { user } = useAuth();
  const [state, setState] = useState<EventFilterState>({
    categoryId: 'all',
    categoryName: 'All',
    currentPage: 1,
    eventDate: null,
    location: null,
    coordinates: null,
  });

  const setCategory = useCallback((id: string, name: string) => {
    setState((prevState) => ({
      ...prevState,
      categoryId: id,
      categoryName: name,
      currentPage: 1,
    }));
  }, []);

  const setPage = useCallback((page: number) => {
    setState((prevState) => ({
      ...prevState,
      currentPage: page,
    }));
  }, []);

  const setDate = useCallback((date: string | null) => {
    setState((prevState) => ({
      ...prevState,
      eventDate: date,
    }));
  }, []);

  const setLocation = useCallback(
    (
      { city, country }: Omit<LocationType, 'coordinates'>,
      { lat, lng }: Point
    ) => {
      setState((prevState) => ({
        ...prevState,
        location: { city, country },
        coordinates: lat !== 0 || lng !== 0 ? { lat, lng } : null,
      }));
    },
    []
  );

  const variables = useMemo(() => {
    const vars: any = {
      take: PAGE_SIZE,
      skip: (state.currentPage - 1) * PAGE_SIZE,
      userId: user?.id,
    };

    if (state.eventDate) {
      vars.eventDate = state.eventDate;
    }
    if (!state.eventDate) {
      vars.minEventEndDate = dayjs().format('YYYY-MM-DD');
    }
    if (state.categoryId !== 'all') {
      vars.categoryId = state.categoryId;
    }
    if (state.location?.city) {
      vars.city = state.location.city;
    }
    if (state.location?.country) {
      vars.country = state.location.country;
    }
    if (state.coordinates) {
      vars.coordinates = JSON.stringify(state.coordinates);
    }

    return vars;
  }, [state, user]);

  return {
    ...state,
    setCategory,
    setPage,
    setDate,
    setLocation,
    variables,
    pageSize: PAGE_SIZE,
  };
};
