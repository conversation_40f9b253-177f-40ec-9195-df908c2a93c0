import { type SpotifyAccessData, useInitiateSpotifyToken } from '@/api';
import { getItem, removeItem, setItem } from '../utils/storage';
import { env } from '@/env.mjs';
import dayjs from 'dayjs';
import React from 'react';
import { toast } from 'react-hot-toast';

const SPOTIFY_TOKEN = 'spotify-token';

export const getSpotifyToken = () => getItem<SpotifyAccessData>(SPOTIFY_TOKEN);
export const removeSpotifyToken = () => removeItem(SPOTIFY_TOKEN);
export const setSpotifyToken = (value: SpotifyAccessData) =>
  setItem<SpotifyAccessData>(SPOTIFY_TOKEN, value);

export const useInitializeSpotify = () => {
  // const setSpotifyData = useSession.use.setSpotifyData();

  const { mutate } = useInitiateSpotifyToken();

  React.useEffect(() => {
    const savedAccessToken = getSpotifyToken();

    const fetchSpotifyData = () => {
      mutate(
        {
          clientId: env.NEXT_PUBLIC_SPOTIFY_CLIENT_ID,
          clientSecret: env.NEXT_PUBLIC_SPOTIFY_CLIENT_SECRET,
        },
        {
          onSuccess: (data) => {
            console.log('🚀 ~ fetchSpotifyData ~ data:', data);
            const tokenObj: SpotifyAccessData = {
              ...data,
              expirationDate: dayjs().add(data.expires_in, 'second'),
            };
            setSpotifyToken(tokenObj); // Save the token to localStorage
          },
          onError: (error) => toast.error(error.message),
        }
      );
    };

    if (!savedAccessToken) {
      console.log('🚀 ~ Fresh ~ fetchSpotifyData');
      fetchSpotifyData();
    } else {
      if (dayjs(savedAccessToken.expirationDate).diff(dayjs()) > 0) {
      } else {
        console.log('🚀 ~ Expired ~ fetchSpotifyData');
        removeSpotifyToken();
        fetchSpotifyData();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};
