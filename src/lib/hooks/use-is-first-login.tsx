'use client';
import { useState, useCallback } from 'react';

const IS_FIRST_LOGIN = 'IS_FIRST_LOGIN';

export const useIsFirstLogin = () => {
  const [isFirstLogin, setIsFirstLoginState] = useState<boolean>(() => {
    const storedValue = localStorage.getItem(IS_FIRST_LOGIN);
    return storedValue === null ? true : storedValue === 'true';
  });

  const setIsFirstLogin = useCallback((value: boolean) => {
    localStorage.setItem(IS_FIRST_LOGIN, String(value));
    setIsFirstLoginState(value);
  }, []);

  return [isFirstLogin, setIsFirstLogin] as const;
};
