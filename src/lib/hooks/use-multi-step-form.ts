'use client';
import { getZodSchemaKeys } from '@/lib/utils';
import { useState, useCallback, useMemo } from 'react';
import type { UseFormReturn, FieldValues } from 'react-hook-form';
import { z } from 'zod';

export interface MultistepFormStep<T extends FieldValues = FieldValues> {
  id: number;
  title: string;
  subTitle?: string;
  showSkip?: boolean;
  schema: z.ZodSchema<Partial<T>>;
  skipValidation?: boolean;
}

interface UseMultistepFormProps<T extends FieldValues> {
  steps: MultistepFormStep<T>[];
  form: UseFormReturn<T>;
  onOpenAccountTypeDialog: () => void;
  onComplete?: (data: T) => void;
}

interface UseMultistepFormReturn<T extends FieldValues> {
  currentStep: number;
  currentStepData: MultistepFormStep<T>;
  isFirstStep: boolean;
  isLastStep: boolean;
  progress: number;
  goToNext: () => Promise<boolean>;
  goToPrevious: () => void;
  goToStep: (step: number) => void;
  skip: () => void;
  canContinue: boolean;
}

export function useMultistepForm<T extends FieldValues>({
  steps,
  form,
  onComplete,
  onOpenAccountTypeDialog,
}: UseMultistepFormProps<T>): UseMultistepFormReturn<T> {
  const [currentStep, setCurrentStep] = useState(0);

  const currentStepData = steps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;
  const progress = ((currentStep + 1) / steps.length) * 100;

  const canContinue = useMemo(() => {
    if (currentStepData.skipValidation) return true;

    const stepKeys = getZodSchemaKeys<T>(currentStepData.schema);
    return stepKeys.every((key) => {
      const { error } = form.getFieldState(key);
      return error === undefined;
    });
  }, [form, currentStepData]);

  const goToNext = useCallback(async (): Promise<boolean> => {
    if (!currentStepData.skipValidation) {
      const isValid = await form.trigger(
        getZodSchemaKeys(currentStepData.schema)
      );
      if (!isValid) return false;
    }

    if (isFirstStep) {
      onOpenAccountTypeDialog();
      return true;
    }
    if (isLastStep) {
      const formData = form.getValues();
      onComplete?.(formData);
      return true;
    } else {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
      form.clearErrors();
      return true;
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [
    currentStepData.skipValidation,
    form,
    isLastStep,
    onComplete,
    steps.length,
  ]);

  const goToPrevious = useCallback(() => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
    form.clearErrors();
  }, [form]);

  const goToStep = useCallback(
    (step: number) => {
      if (step >= 0 && step < steps.length) {
        setCurrentStep(step);
        form.clearErrors();
      }
    },
    [form, steps.length]
  );

  const skip = useCallback(() => {
    if (isLastStep) {
      const formData = form.getValues();
      onComplete?.(formData);
    } else {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
      form.clearErrors();
    }
  }, [form, isLastStep, onComplete, steps.length]);

  return {
    currentStep,
    currentStepData,
    isFirstStep,
    isLastStep,
    progress,
    goToNext,
    goToPrevious,
    goToStep,
    skip,
    canContinue: Boolean(canContinue),
  };
}

export function createFormStep<T extends FieldValues>(
  step: Omit<MultistepFormStep<T>, 'id'> & { id?: number }
): MultistepFormStep<T> {
  return {
    id: step.id ?? 0,
    ...step,
  };
}
