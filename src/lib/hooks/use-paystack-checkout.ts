'use client';
import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

import { getQueryClient } from '@/api';

import { useAuth } from '../';
import { usePurchaseTicketContext } from '../contexts/';
import { type SelectedTicket } from './use-purchase-ticket';
import { type PurchaseTicketFormType } from '@/lib';
import { useFormContext } from 'react-hook-form';

export const usePaystackCheckout = ({
  amount,
  discountCode,
}: {
  amount: number;
  discountCode?: string;
}) => {
  const { watch } = useFormContext<PurchaseTicketFormType>();
  const router = useRouter();
  const queryClient = getQueryClient();
  const { user, isAuthenticated } = useAuth();
  const email = (isAuthenticated ? user?.email : watch('email')) ?? '';
  const fullName = (isAuthenticated ? user?.fullName : watch('fullName')) ?? '';
  const userId = (isAuthenticated ? user?.id : '') ?? '';
  const { event, getSelectedTicketsArray } = usePurchaseTicketContext();
  const amountInKobo = amount * 100;
  const selectedTickets = getSelectedTicketsArray();

  const [PaystackPop, setPaystackPop] = useState<any>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // @ts-expect-error - abc
      import('@paystack/inline-js').then((mod) => {
        setPaystackPop(() => mod.default);
      });
    }
  }, []);

  const paystack = useMemo(() => {
    return PaystackPop ? new PaystackPop() : null;
  }, [PaystackPop]);

  const formatMetadata = (metadata: {
    purpose: string;
    user: { fullName: string; email: string };
    eventId: string;
    userId: string;
    breakdown: Omit<SelectedTicket, 'cost'>[];
    discountCode?: string;
  }): {
    custom_fields: {
      display_name: string;
      variable_name: string;
      value: string | number;
    }[];
  } => {
    return {
      custom_fields: [
        {
          display_name: 'Purpose',
          variable_name: 'purpose',
          value: metadata.purpose,
        },
        {
          display_name: 'User Details',
          variable_name: 'user',
          value: JSON.stringify(metadata.user),
        },
        {
          display_name: 'Event ID',
          variable_name: 'event_id',
          value: metadata.eventId,
        },
        {
          display_name: 'User ID',
          variable_name: 'userId',
          value: metadata.userId,
        },
        {
          display_name: 'Ticket Breakdown',
          variable_name: 'breakdown',
          value: JSON.stringify(metadata.breakdown),
        },
        ...(metadata.discountCode
          ? [
              {
                display_name: 'Discount Code',
                variable_name: 'discountCode',
                value: metadata.discountCode,
              },
            ]
          : []),
      ],
    };
  };

  const checkout = () => {
    const metadata = formatMetadata({
      purpose: 'EVENT_TICKET_PURCHASE',
      user: { email, fullName },
      eventId: event?.id || '',
      breakdown: selectedTickets.map(({ category, quantity, isPresale }) => ({
        category,
        quantity,
        isPresale,
      })),
      userId,
      discountCode,
    });

    paystack.newTransaction({
      key: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,
      email,
      amount: amountInKobo,
      onSuccess: () => {
        toast.success('Payment successful');
        queryClient.invalidateQueries({
          queryKey: ['getUserTickets', userId],
        });
        queryClient.invalidateQueries({
          queryKey: ['getEvent'],
        });
        queryClient.invalidateQueries({
          queryKey: ['getEventWithSlug'],
        });
        router.push(`/tickets`);
      },
      onCancel: () => {
        toast.error('Payment cancelled.');
      },
      metadata,
    });
  };

  return { checkout };
};
