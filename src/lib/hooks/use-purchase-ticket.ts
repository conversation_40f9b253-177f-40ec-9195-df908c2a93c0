'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams, useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  DiscountType,
  type EventDiscount,
  useGetEventCategories,
  useGetEventWithSlug,
  useValidateEventAccessCode,
} from '@/api/events';

import { useAuth } from './use-auth';
import { ticketSchema } from '../schemas';
import { isPresaleActive, toAmountInMajor } from '../utils';
import toast from 'react-hot-toast';
import { getQueryClient } from '@/api';

export interface SelectedTicket {
  category: string;
  quantity: number;
  cost: number;
  isPresale?: boolean;
}

export interface AdditionalFees {
  fee: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountAmount?: number;
}

export interface CostBreakdown {
  ticketSubtotal: number;
  totalFees: number;
  fees: AdditionalFees;
  total: number;
}

export const createDefaultTicket = (): z.infer<typeof ticketSchema> => ({
  id: Math.random().toString(36).substring(2, 15) + Date.now().toString(36),
  name: '',
  price: 0,
  quantity: 0,
  description: '',
  hasPresale: false,
  hasTimeline: false,
  hasPurchaseLimit: false,
  purchaseLimit: 0,
});

export function usePurchaseEventTicket() {
  const { user } = useAuth();
  const queryClient = getQueryClient();
  const router = useRouter();

  const params = useParams<{ slug: string }>();

  const slug = params.slug;

  const {
    data: slugEvent,
    isFetching: isEventFetching,
    error,
  } = useGetEventWithSlug({
    variables: {
      slug: slug || '',
      targetCurrency: 'NGN',
      userId: user?.id,
    },
    enabled: !!slug,
  });

  const event = slugEvent;

  const eventId = event?.id || '';

  const [accessCodeVerified, setAccessCodeVerified] = useState<boolean>(
    !isEventFetching &&
      error &&
      (error.code === 'FORBIDDEN' || error.response?.status === 403)
      ? false
      : true
  );

  const { mutate: validateAccessCode, isPending: isValidatingAccessCode } =
    useValidateEventAccessCode({
      onSuccess: ({ slug, accessGranted, token }) => {
        if (!accessGranted) toast.success('Access code invalid');
        queryClient.setQueryData(['event-access-token', slug], token);
        setAccessCodeVerified(true);
        toast.success('Access code valid');
        formMethods.reset();
        toast.success('Access code valid');

        router.push(`/events/${slug}`);
      },
      onError: (error) => {
        setAccessCodeVerified(false);
        toast.error(error.message);
      },
    });

  const [editType, setEditType] = React.useState<
    'desc' | 'date' | 'location'
  >();
  const [activeDiscount, setActiveDiscount] = React.useState<EventDiscount>();

  const { data: categories, isFetching: isEventCategoriesFetching } =
    useGetEventCategories();

  const [selectedTickets, setSelectedTickets] = React.useState<
    Record<string, number>
  >({});

  const getSelectedTicketsArray = (
    tickets = selectedTickets
  ): SelectedTicket[] => {
    if (!event?.ticketCategories) return [];

    return Object.entries(tickets)
      .filter(([, quantity]) => quantity > 0)
      .map(([category, quantity]) => {
        const presaleTicket = event?.presaleConfig?.find(
          (presale) =>
            presale.ticketCategoryId === event.ticketCategories[category].id
        );
        const shouldUsePresale =
          presaleTicket && isPresaleActive(presaleTicket);

        const activeTicket = shouldUsePresale
          ? {
              ...event.ticketCategories[category],
              cost: presaleTicket.price,
              quantity: presaleTicket.quantity,
              purchaseLimit:
                presaleTicket.purchaseLimit ||
                event.ticketCategories[category].purchaseLimit,
              description:
                presaleTicket.description ||
                event.ticketCategories[category].description,
            }
          : event.ticketCategories[category];

        const { convertedCost, cost } = activeTicket;

        return {
          category,
          quantity,
          cost: convertedCost || cost,
          ...(shouldUsePresale && { isPresale: shouldUsePresale }),
        };
      });
  };

  const selectedTicketSchema = z.object({
    category: z.string(),
    quantity: z.coerce.number().min(1, 'Quantity must be at least 1'),
    isPresaleTicket: z.boolean().optional(),
  });
  const schema = z.object({
    discountCode: z.string().optional(),
    paymentMethod: z.enum(['wallet', 'online']),
    selectedTickets: z
      .array(selectedTicketSchema)
      .min(1, 'At least one ticket must be selected'),
    tickets: z.array(ticketSchema).min(1, 'At least one ticket is required'),
    accessCode: z.string().optional(),
    accessCodeVerified: z.boolean().optional(),
    fullName: z.string('Full name is required').min(1, 'Full name is required'),
    email: z.email('Email is required').min(1, 'Email is required'),
  });

  const formMethods = useForm<z.infer<typeof schema>>({
    defaultValues: {
      paymentMethod: 'wallet',
      selectedTickets: [],
      tickets: [createDefaultTicket()],
      accessCodeVerified: accessCodeVerified,
    },
    // @ts-expect-error - abc
    resolver: zodResolver(schema),
    mode: 'onBlur',
  });

  const handleTicketQuantityChange = (category: string, quantity: number) => {
    const updatedSelectedTickets = {
      ...selectedTickets,
      [category]: quantity,
    };
    setSelectedTickets(updatedSelectedTickets);

    const selectedArray = getSelectedTicketsArray(updatedSelectedTickets);

    formMethods.setValue(
      'selectedTickets',
      selectedArray.map(({ quantity, category, isPresale }) => ({
        quantity,
        category,
        isPresaleTicket: !!isPresale,
      })),
      {
        shouldValidate: true,
        shouldDirty: true,
      }
    );
  };

  const calculateTotalCost = React.useCallback(
    (additionalFees: AdditionalFees = { fee: 0 }): CostBreakdown => {
      if (!event?.ticketCategories) {
        return {
          ticketSubtotal: 0,
          totalFees: 0,
          fees: additionalFees,
          total: 0,
        };
      }

      const ticketSubtotal = Object.entries(selectedTickets).reduce(
        (total, [category, quantity]) => {
          const presaleTicket = event?.presaleConfig?.find(
            (presale) =>
              presale.ticketCategoryId === event.ticketCategories[category].id
          );
          const shouldUsePresale =
            presaleTicket && isPresaleActive(presaleTicket);

          const activeTicket = shouldUsePresale
            ? {
                ...event.ticketCategories[category],
                cost: presaleTicket.price,
                quantity: presaleTicket.quantity,
                purchaseLimit:
                  presaleTicket.purchaseLimit ||
                  event.ticketCategories[category].purchaseLimit,
                description:
                  presaleTicket.description ||
                  event.ticketCategories[category].description,
              }
            : event.ticketCategories[category];

          if (activeTicket && quantity > 0) {
            const cost = activeTicket.convertedCost || activeTicket.cost;
            return total + toAmountInMajor(cost) * quantity;
          }
          return total;
        },
        0
      );

      let discountAmount = 0;
      if (additionalFees?.discountType === DiscountType.AMOUNT) {
        discountAmount = toAmountInMajor(additionalFees.discountValue || 0);
      } else if (additionalFees?.discountType === DiscountType.PERCENTAGE) {
        discountAmount = Math.floor(
          (ticketSubtotal * (additionalFees.discountValue || 0)) / 100
        );
      }

      const totalFees = ticketSubtotal * (additionalFees?.fee || 0);

      const maxDiscount = Math.min(discountAmount, ticketSubtotal + totalFees);

      const total = ticketSubtotal + totalFees - maxDiscount;

      return {
        ticketSubtotal,
        totalFees,
        fees: {
          ...additionalFees,
          discountAmount: maxDiscount,
        },
        total,
      };
    },
    [selectedTickets, event?.ticketCategories, event?.presaleConfig]
  );

  const getTotalQuantity = React.useCallback(() => {
    return Object.values(selectedTickets).reduce(
      (total, quantity) => total + quantity,
      0
    );
  }, [selectedTickets]);

  const resetForm = React.useCallback(() => {
    setSelectedTickets({});
    formMethods.reset();
  }, [formMethods]);

  const hasSelectedTickets = React.useMemo(() => {
    return Object.values(selectedTickets).some((quantity) => quantity > 0);
  }, [selectedTickets]);

  return {
    event,
    categories,
    isEventLoading: isEventCategoriesFetching || isEventFetching,
    selectedTickets,
    handleTicketQuantityChange,
    getSelectedTicketsArray,
    hasSelectedTickets,
    schema,
    formMethods,
    resetForm,
    calculateTotalCost,
    getTotalQuantity,
    eventId,
    editType,
    setEditType,
    activeDiscount,
    setActiveDiscount,
    accessCodeVerified,
    isValidatingAccessCode,
    validateAccessCode,
  };
}

export type PurchaseTicketFormType = z.infer<
  ReturnType<typeof usePurchaseEventTicket>['schema']
>;
