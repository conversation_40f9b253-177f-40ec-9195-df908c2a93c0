'use client';
import React from 'react';

/**
 * Custom hook to manage countdown timer with completion callback
 * @param isActive Whether the timer should be active
 * @param countStart Countdown start time (in seconds)
 * @param onComplete Callback invoked when countdown finishes
 * @returns Remaining time, formatted time string, and reset function
 */
export const useRecordingTimer = (
  isActive: boolean,
  countStart: number,
  onComplete?: () => void
) => {
  const [remainingTime, setRemainingTime] = React.useState(countStart);
  const hasCompletedRef = React.useRef(false);
  const timerRef = React.useRef<number | null>(null);

  const resetTimer = React.useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setRemainingTime(countStart);
    hasCompletedRef.current = false;
  }, [countStart]);

  React.useEffect(() => {
    if (isActive) {
      setRemainingTime(countStart);
      hasCompletedRef.current = false;

      timerRef.current = window.setInterval(() => {
        setRemainingTime((prevTime) => {
          if (prevTime <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
            }
            if (!hasCompletedRef.current) {
              hasCompletedRef.current = true;
              onComplete?.();
            }
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive, countStart]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}`;
  };

  return {
    remainingTime,
    formattedTime: formatTime(remainingTime),
    resetTimer,
  };
};
