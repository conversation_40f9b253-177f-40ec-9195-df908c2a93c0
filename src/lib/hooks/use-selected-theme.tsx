'use client';
import { useTheme } from 'next-themes';
import { useEffect, useState, useCallback } from 'react';

import { ColorSchemeType } from '@/lib/constants';

export const useSelectedTheme = () => {
  const { theme, setTheme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const setSelectedTheme = useCallback(
    (t: ColorSchemeType) => {
      setTheme(t);
    },
    [setTheme]
  );

  const selectedTheme = (theme ?? 'system') as ColorSchemeType;
  const resolvedTheme =
    selectedTheme === 'system'
      ? (systemTheme as ColorSchemeType)
      : selectedTheme;
  const isDark = resolvedTheme === 'dark';

  return {
    selectedTheme,
    setSelectedTheme,
    isDark,
    mounted,
  } as const;
};
