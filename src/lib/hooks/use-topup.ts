'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

import { getQueryClient } from '@/api';
import {
  useInitializeTransaction,
  useVerifyPaystackTransaction,
} from '@/api/transactions';
import { useAuth } from '@/lib/hooks/use-auth';
import { useEffect, useState } from 'react';

export const useTopup = ({ amount }: { amount: number }) => {
  const router = useRouter();
  const queryClient = getQueryClient();
  const { email, fullName, id: userId } = useAuth().user!;
  const { mutate: initializeTransaction, isPending: initializing } =
    useInitializeTransaction();
  const { mutate: verifyPaystackTransaction } = useVerifyPaystackTransaction();

  const [PaystackPop, setPaystackPop] = useState<any>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // @ts-expect-error - abc
      import('@paystack/inline-js').then((mod) => {
        setPaystackPop(() => mod.default);
      });
    }
  }, []);

  const formatMetadata = (metadata: {
    purpose: string;
    user: { fullName: string; email: string };
    userId: string;
  }) => ({
    custom_fields: [
      {
        display_name: 'Purpose',
        variable_name: 'purpose',
        value: metadata.purpose,
      },
      {
        display_name: 'User Details',
        variable_name: 'user',
        value: JSON.stringify(metadata.user),
      },
      {
        display_name: 'User ID',
        variable_name: 'userId',
        value: metadata.userId,
      },
    ],
  });

  const topup = ({ reference }: { reference: string }) => {
    if (!PaystackPop) {
      toast.error('Payment module not ready. Please try again.');
      return;
    }

    const popup = new PaystackPop();
    popup.newTransaction({
      key: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,
      email,
      amount: amount * 100,
      reference,
      onSuccess: (res: any) => {
        verifyPaystackTransaction(
          { transactionRef: res.reference },
          {
            onSuccess: ({ status }) => {
              queryClient.invalidateQueries({ queryKey: ['getUser'] });
              queryClient.invalidateQueries({
                queryKey: ['getTransactionHistory'],
              });
              if (status === 'success') {
                toast.success('Transaction Successful');
              } else {
                toast.success('Transaction could not be verified');
              }
              router.back();
            },
            onError: (error) => toast.error(error.message),
          }
        );
      },
      onCancel: () => {
        verifyPaystackTransaction({ transactionRef: reference });
      },
      metadata: formatMetadata({
        purpose: 'WALLET_TOPUP',
        user: { email: email, fullName: fullName },
        userId,
      }),
    });
  };

  return { topup, initializeTransaction, initializing };
};
