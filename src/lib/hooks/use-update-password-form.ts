'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { getQueryClient } from '@/api';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { z } from 'zod';
import { useChangePassword } from '@/api';

export function useUpdatePasswordForm() {
  const schema = z
    .object({
      password: z.string().min(6, 'Current password is required'),
      newPassword: z
        .string()
        .min(6, 'New password must be at least 6 characters'),
      confirmPassword: z.string().min(6, 'Please confirm your new password'),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: "Passwords don't match",
      path: ['confirmPassword'],
    });

  type UpdatePasswordSchema = z.infer<typeof schema>;

  const {
    mutate: changePassword,
    isPending: passwordUpdating,
    error,
  } = useChangePassword();

  const formMethods = useForm<UpdatePasswordSchema>({
    defaultValues: {
      password: '',
      newPassword: '',
      confirmPassword: '',
    },
    resolver: zodResolver(schema),
    mode: 'onBlur',
  });

  const queryClient = getQueryClient();

  const onSubmit: SubmitHandler<UpdatePasswordSchema> = (data) => {
    const payload = {
      oldPassword: data.password,
      password: data.newPassword,
    };

    console.log('update password', payload);

    changePassword(
      { ...payload },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['getUser'] });
          toast.success('Password updated successfully');
          formMethods.reset();
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  return {
    formMethods,
    schema,
    onSubmit,
    passwordUpdating,
    error,
  };
}

export type UpdatePasswordFormType = z.infer<
  ReturnType<typeof useUpdatePasswordForm>['schema']
>;
