'use client';

import { useEffect, useState } from 'react';

type RuntimeEnvironment = 'server' | 'client';
type PlatformType =
  | 'android'
  | 'ios'
  | 'macos'
  | 'windows'
  | 'linux'
  | 'unknown';
type DeviceType = 'mobile' | 'desktop' | 'tablet' | 'unknown';

export interface PlatformInfo {
  runtime: RuntimeEnvironment;
  platform: PlatformType;
  device: DeviceType;
  userAgent: string | null;
  mounted: boolean;
}

export const usePlatform = (): PlatformInfo => {
  const [state, setState] = useState<PlatformInfo>({
    runtime: typeof window === 'undefined' ? 'server' : 'client',
    platform: 'unknown',
    device: 'unknown',
    userAgent: null,
    mounted: false,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const ua = navigator.userAgent.toLowerCase();

    const platform: PlatformType = /android/.test(ua)
      ? 'android'
      : /iphone|ipad|ipod/.test(ua)
        ? 'ios'
        : /mac/.test(ua)
          ? 'macos'
          : /win/.test(ua)
            ? 'windows'
            : /linux/.test(ua)
              ? 'linux'
              : 'unknown';

    const device: DeviceType = /mobile/.test(ua)
      ? 'mobile'
      : /tablet/.test(ua)
        ? 'tablet'
        : 'desktop';

    setState({
      runtime: 'client',
      platform,
      device,
      userAgent: navigator.userAgent,
      mounted: true,
    });
  }, []);

  return state;
};
