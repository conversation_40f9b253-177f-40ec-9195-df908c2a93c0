import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { create } from 'zustand';

import { type NotificationType } from '@/types';

import { createSelectors } from '../utils';
import {
  getLocalNotifications,
  removeLocalNotifications,
  setLocalNotifications,
} from './utils';

dayjs.extend(relativeTime);

export type NotificationData = {
  id: number | string;
  text: string;
  imgUrl?: string;
  date: Date;
  relativeTimeFromNow?: string;
  type?: NotificationType;
  eventId?: string;
  eventSlug?: string;
  djSessionId?: string;
  songId?: string;
  userId?: string;
  username?: string;
  userAvatar?: string;
  chatRoomId?: string;
};

interface NotificationState {
  notifications: NotificationData[];
  getNotificationList: () => void;
  updateNotificationList: (data: NotificationData) => void;
  clearNotifications: () => void;
}

const _useNotificationStore = create<NotificationState>((set) => ({
  notifications: [],
  getNotificationList: () => {
    const stored = getLocalNotifications();

    // Compute relative time
    const parsed =
      stored?.map((n) => ({
        ...n,
        date: new Date(n.date),
        relativeTimeFromNow: dayjs(n.date).fromNow(),
      })) || [];

    set({ notifications: parsed });
  },

  updateNotificationList: (newNotification) => {
    const prevNotifications = getLocalNotifications();

    const updatedNotification = [
      {
        ...newNotification,
        date: new Date(newNotification.date),
        relativeTimeFromNow: dayjs(newNotification.date).fromNow(),
      },
      ...(prevNotifications || []),
    ];

    setLocalNotifications(updatedNotification);
    set({ notifications: updatedNotification });
  },

  clearNotifications: () => {
    removeLocalNotifications();
    set({ notifications: [] });
  },
}));

// Export with selectors
export const useNotificationStore = createSelectors(_useNotificationStore);

export const getNotificationList = () =>
  _useNotificationStore.getState().getNotificationList();

export const updateNotificationList = (newNotification: NotificationData) =>
  _useNotificationStore.getState().updateNotificationList(newNotification);

export const clearNotifications = () =>
  _useNotificationStore.getState().clearNotifications();
