import { getItem, removeItem, setItem } from '@/lib/storage';

import { type NotificationData } from '.';

const POPLA_NOTIFICATIONS = '@popla:notifications';

export const getLocalNotifications = () =>
  getItem<NotificationData[]>(POPLA_NOTIFICATIONS);
export const removeLocalNotifications = () => removeItem(POPLA_NOTIFICATIONS);
export const setLocalNotifications = (value: NotificationData[]) =>
  setItem<NotificationData[]>(POPLA_NOTIFICATIONS, value);
