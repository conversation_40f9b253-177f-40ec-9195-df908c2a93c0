import * as z from 'zod';

import { Location } from '@/types';

const baseTicketSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Ticket name is required'),
  price: z
    .string()
    .min(1, 'Price is required')
    .refine((val) => {
      const numeric = Number(val.replace(/,/g, ''));
      return !isNaN(numeric) && numeric > 0;
    }, 'Price must be a valid number greater than zero')
    .transform((val) => Number(val.replace(/,/g, ''))),

  quantity: z.coerce.number().int().min(1, 'Quantity must be at least 1'),
  description: z.string().optional(),
  hasPresale: z.boolean(),
  hasTimeline: z.boolean(),
  startDatetime: z.date().optional(),
  endDatetime: z.date().optional(),
  hasPurchaseLimit: z.boolean(),
  purchaseLimit: z
    .number()
    .int()
    .min(1, 'Purchase limit must be at least 1')
    .optional(),
});

const withPresaleTicketSchema = baseTicketSchema.extend({
  presalePrice: z
    .string()
    .min(1, 'Price is required')
    .refine((val) => {
      const numeric = Number(val.replace(/,/g, ''));
      return !isNaN(numeric) && numeric > 0;
    }, 'Presale price must be greater than zero'),
  presalePurchaseLimit: z
    .number()
    .int()
    .min(1, 'Presale limit must be at least 1')
    .optional(),
  presaleQuantity: z.coerce
    .number()
    .int()
    .min(1, 'Presale quantity must be at least 1'),
  presaleDescription: z.string().optional(),
  presaleStartDatetime: z
    .date()
    .refine(
      (date) => date > new Date(),
      'Presale start date must be in the future'
    ),
  presaleEndDatetime: z
    .date()
    .refine(
      (date) => date > new Date(),
      'Presale end date must be in the future'
    ),
});

export const ticketSchema = z.discriminatedUnion('hasPresale', [
  baseTicketSchema.extend({ hasPresale: z.literal(false) }),
  withPresaleTicketSchema.extend({ hasPresale: z.literal(true) }),
]);

const registrationFieldSchema = z.object({
  name: z.string(),
  type: z.enum([
    'alpha-numeric',
    'numeric',
    'age-range-selector',
    'country-selector',
    'sex-selector',
  ]),
});

const bannerSchema = z.object({
  uri: z.string(),
  file: z.instanceof(File).optional(),
  type: z.string().optional(),
  name: z.string().nullable().optional(),
  size: z.number().optional(),
});

const baseSchema = z.object({
  eventType: z.enum(['PUBLIC', 'PRIVATE']),
  eventFormat: z.enum(['IN_PERSON', 'ONLINE', 'HYBRID']),
  ticketType: z.enum(['FREE', 'PAID']),
  title: z
    .string()
    .min(1, 'Event title is required')
    .max(100, 'Title is too long'),
  description: z.string().min(1, 'Event description is required'),
  startDatetime: z
    .date()
    .min(new Date(), 'Event start date must be in the future'),
  endDatetime: z.date().min(new Date(), 'Event end date must be in the future'),
  collaborators: z
    .array(z.object({ id: z.string(), name: z.string(), avatar: z.string() }))
    .default([]),
  bannerObj: bannerSchema.optional(),
  media: z.array(bannerSchema).optional(),
  timezone: z.string(),
  category: z.string(),
  tickets: z.array(ticketSchema).min(1, 'At least one ticket is required'),
  passcode: z.string().optional(),
  isRegistrationRequired: z.boolean().optional(),
  maxAttendees: z.coerce.number().int().min(1).optional(),
  registrationFields: z.array(registrationFieldSchema).optional(),
});

const onlineSchema = baseSchema.extend({
  onlineUrl: z.url('Invalid URL').optional(),
});

const physicalSchema = baseSchema.extend({
  location: Location,
});

const hybridSchema = baseSchema.extend({
  onlineUrl: z.url('Invalid URL').optional(),
  location: Location,
});

export const eventCreateSchema = z.discriminatedUnion('eventFormat', [
  onlineSchema.extend({ eventFormat: z.literal('ONLINE') }),
  physicalSchema.extend({ eventFormat: z.literal('IN_PERSON') }),
  hybridSchema.extend({ eventFormat: z.literal('HYBRID') }),
]);

export type EventCreatePayload = z.infer<typeof eventCreateSchema>;
