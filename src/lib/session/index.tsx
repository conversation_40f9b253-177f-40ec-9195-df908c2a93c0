import { create } from 'zustand';

import {
  type DJLiveSession,
  type DJSessionState,
  type IRequestH<PERSON>oryItem,
  type LiveSessionInterface,
  type LiveSessionResponse,
  type QuesterSessionInfo,
  type Queue,
  type SpotifyAccessData,
  type TrackItems,
  type UserLiveSession,
} from '@/api/session/types';

import { createSelectors } from '../utils';
import { djSessionState } from './state';
import {
  setLiveSession as setLocalLiveSession,
  setSpotifyToken,
} from './utils';

interface SessionState extends DJSessionState {
  joinedSessionId: string;
  setJoinedSessionId: (sessionId: string) => void;
  setLiveSession: (session: LiveSessionInterface) => void;
  resetDjSession: () => void;
  updateSessionInfo: (sessionInfo: QuesterSessionInfo) => void;
  updateQueue: (userLiveSession: UserLiveSession) => void;
  setDjQueue: (djLiveSession: DJLiveSession) => void;
  setTopLiveSessions: (payload: {
    djSessions: LiveSessionResponse[];
    combinedSessionAndTrendingList: (
      | LiveSessionResponse
      | {
          id: string;
          name: string;
          avatar: string;
        }
    )[];
  }) => void;
  removeEndedLiveSession: (sessionId: string) => void;
  changeLiveSessionStatusForCreator: (sessionId: string) => void;
  setRecentLiveSessions: (sessions: LiveSessionResponse[]) => void;
  setSpotifyData: (spotifyData: SpotifyAccessData) => Promise<void>;
  setTrackResults: (spotrackResultstifyData: TrackItems[]) => void;
  setRequestHistory: (history: IRequestHistoryItem[]) => void;
  resetDjSessionLoadingState: () => void;
}

const _useSession = create<SessionState>((set) => ({
  ...djSessionState,
  joinedSessionId: '',
  setJoinedSessionId: (joinedSessionId) => set({ joinedSessionId }),

  setLiveSession: (session) => {
    const { questersOnSession, djSession } = session;
    const {
      cost,
      createdAt,
      bannerUrl,
      id,
      title,
      isFree,
      dj,
      djId,
      location,
      status,
      updatedAt,
      currency,
      enableVideo,
      conversionRate,
      originalCost,
      convertedCost,
      shoutoutEnabled,
      songRequestEnabled,
      locationType,
      shoutoutCost,
      type,
    } = djSession;
    setLocalLiveSession(djSession);

    set((state) => ({
      sessionInfo: {
        ...state.sessionInfo,
        isFree,
        enableVideo,
        cost,
        id,
        bannerUrl,
        createdAt,
        title,
        currency,
        conversionRate,
        originalCost,
        convertedCost,
        shoutoutEnabled,
        songRequestEnabled,
        locationType,
        shoutoutCost,
        type,
        ...(dj && {
          dj: {
            fullName: dj.fullName,
            username: dj.username,
            profileImageUrl: dj.profileImageUrl,
          },
        }),
        ...(djId && { djId }),
        ...(location && { location }),
        ...(status && { status }),
        ...(updatedAt && { updatedAt }),
        ...(questersOnSession && { questersOnSession }),
      },
    }));
  },

  resetDjSession: () => {
    set((state) => ({
      ...djSessionState,
      topLiveSessions: state.topLiveSessions,
      combinedSessionAndTrendingList: state.combinedSessionAndTrendingList,
      joinedSessionId: '',
    }));
  },

  updateSessionInfo: (sessionInfo) => {
    const { questers } = sessionInfo;

    set((state) => ({
      sessionInfo: {
        ...state.sessionInfo,
        questersOnSession: questers.count,
        profileImageUrls: questers.profileImageUrls,
      },
    }));
  },

  updateQueue: (sessionQueue) => {
    const { queue } = sessionQueue;

    set((state) => {
      if (Array.isArray(queue)) {
        return {
          user: {
            ...state.user,
            queue,
          },
        };
      } else {
        const djQueue = queue as unknown as DJLiveSession;
        const newQueue = [
          ...djQueue?.playQueue,
          ...(djQueue?.playedSongs as Queue[]),
          ...djQueue?.requests,
        ];

        return {
          user: {
            ...state.user,
            queue: newQueue,
          },
        };
      }
    });
  },

  setDjQueue: (djLiveSession) => {
    set((state) => ({
      dj: {
        ...state.dj,
        playQueue: djLiveSession?.playQueue || state.dj.playQueue,
        requests: djLiveSession?.requests || state.dj.requests,
        playedSongs: djLiveSession?.playedSongs || state.dj.playedSongs || [],
      },
    }));
  },

  setTopLiveSessions: (payload) => {
    const { djSessions, combinedSessionAndTrendingList } = payload;

    set(() => ({
      topLiveSessions: djSessions,
      combinedSessionAndTrendingList: combinedSessionAndTrendingList,
    }));
  },

  removeEndedLiveSession: (sessionId) => {
    set((state) => ({
      topLiveSessions: state.topLiveSessions.filter(
        (item) => item.id !== sessionId
      ),
      combinedSessionAndTrendingList:
        state.combinedSessionAndTrendingList.filter(
          (item) =>
            !('dj' in item) ||
            (item as LiveSessionResponse).sessionId !== sessionId
        ),
    }));
  },

  changeLiveSessionStatusForCreator: (sessionId) => {
    set((state) => ({
      combinedSessionAndTrendingList: state.combinedSessionAndTrendingList.map(
        (item) => {
          if ('dj' in item && item.sessionId === sessionId) {
            return {
              ...item,
              status: 'ENDED',
            };
          }
          return item;
        }
      ),
    }));
  },

  setRecentLiveSessions: (sessions) => {
    set(() => ({
      recentLiveSessions: sessions,
    }));
  },

  setSpotifyData: async (spotifyData) => {
    await setSpotifyToken(spotifyData);
    set(() => ({
      spotifyData,
    }));
  },

  setTrackResults: (trackResults) => {
    set(() => ({
      trackResults,
    }));
  },

  setRequestHistory: (history) => {
    set(() => {
      const songRequestHistoryOptions = history.map((requestItem) => ({
        label: `${requestItem.djSession.title} - ${requestItem.song.title} by ${requestItem.song.artist}`,
        value: requestItem.id,
      }));

      return {
        requestHistory: history,
        songRequestHistoryOptions,
      };
    });
  },

  resetDjSessionLoadingState: () => {
    set(() => ({
      loading: false,
    }));
  },
}));

export const getJoinedSessionId = () => _useSession.getState().joinedSessionId;

export const useSession = createSelectors(_useSession);
