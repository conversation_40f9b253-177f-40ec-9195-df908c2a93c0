import { type DJSessionState } from '@/api/session/types';

import { getLiveSession, getSpotifyToken } from './utils';

export const djSessionState: DJSessionState = {
  sessionInfo: {
    id: '',
    djId: '',
    creatorId: '',
    bannerUrl: '',
    title: '',
    cost: 0,
    location: {
      address: '',
      city: '',
      country: '',
      state: '',
      street: '',
      coordinates: {
        lat: 0,
        lng: 0,
        latitude: 0,
        longitude: 0,
      },
      landmark: '',
    },
    createdAt: '',
    updatedAt: '',
    dj: {
      fullName: '',
      username: '',
      profileImageUrl: '',
    },
    questersOnSession: 0,
    profileImageUrls: [],
    status: 'ENDED',
    shoutoutEnabled: false,
    songRequestEnabled: true,
    endTime: null,
    ...getLiveSession(),
  },
  dj: {
    playQueue: [],
    playedSongs: [],
    requests: [],
    creatorId: '',
  },
  user: {
    queue: [],
  },
  topLiveSessions: [],
  recentLiveSessions: [],
  spotifyData: getSpotifyToken(),
  trackResults: [],
  requestHistory: [],
  loading: false,
  combinedSessionAndTrendingList: [],
  songRequestHistoryOptions: [],
};
