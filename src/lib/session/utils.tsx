import {
  type CreateLiveSessionResponse,
  type SpotifyAccessData,
} from '@/api/session';

import { getItem, removeItem, setItem } from '../storage';

const LIVE_SESSION = 'live-session';
const SPOTIFY_TOKEN = 'spotify-token';

export const getLiveSession = () =>
  getItem<CreateLiveSessionResponse>(LIVE_SESSION);
export const removeLiveSession = () => removeItem(LIVE_SESSION);
export const setLiveSession = (value: CreateLiveSessionResponse) =>
  setItem<CreateLiveSessionResponse>(LIVE_SESSION, value);

export const getSpotifyToken = () => getItem<SpotifyAccessData>(SPOTIFY_TOKEN);
export const removeSpotifyToken = () => removeItem(SPOTIFY_TOKEN);
export const setSpotifyToken = (value: SpotifyAccessData) =>
  setItem<SpotifyAccessData>(SPOTIFY_TOKEN, value);
