import { create } from 'zustand';

import { createSelectors } from '../utils';

// Define the store state type
interface TransactionState {
  transactionRef: string;
  setTransactionReference: (transactionRef: string) => void;
}

// Create the store
const _useTransactionStore = create<TransactionState>((set) => ({
  transactionRef: '',
  setTransactionReference: (transactionRef) => set({ transactionRef }),
}));

// Export with selectors
export const useTransactionStore = createSelectors(_useTransactionStore);
