import { create } from 'zustand';

import { createSelectors } from '../utils';

interface TransactionState {
  transactionRef: string;
  setTransactionReference: (transactionRef: string) => void;
}

const _useTransactionStore = create<TransactionState>((set) => ({
  transactionRef: '',
  setTransactionReference: (transactionRef) => set({ transactionRef }),
}));

export const useTransactionStore = createSelectors(_useTransactionStore);
