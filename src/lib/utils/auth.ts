import AppleProvider from 'next-auth/providers/apple';
import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';
import { NextAuthOptions, getServerSession } from 'next-auth';
import { apiLoginUser, apiSocialLoginUser } from '@/api';
import {
  ExtendedJWT,
  ExtendedSession,
  SocialLoginPayload,
  UserWithSession,
} from '@/api';
import _ from 'lodash';
import { useQuery } from '@tanstack/react-query';
import { getSession } from 'next-auth/react';
import axios from 'axios';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        country: { label: 'Country', type: 'text', optional: true },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password are required');
        }

        try {
          const { data } = await apiLoginUser(credentials);
          const { user, token } = data.data;
          return { ...user, accessToken: token } as UserWithSession;
        } catch (error) {
          console.error('Login error:', error);
          let message = 'Login failed';

          if (axios.isAxiosError<CustomError>(error)) {
            message = error.response?.data?.message ?? message;
          } else if (error instanceof Error) {
            message = error.message;
          }
          throw new Error(message);
        }
      },
    }),
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET
      ? [
          GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
          }),
        ]
      : []),
    ...(process.env.APPLE_CLIENT_ID && process.env.APPLE_CLIENT_SECRET
      ? [
          AppleProvider({
            clientId: process.env.APPLE_CLIENT_ID,
            clientSecret: process.env.APPLE_CLIENT_SECRET,
          }),
        ]
      : []),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google' || account?.provider === 'apple') {
        try {
          const socialPayload: SocialLoginPayload = {
            type: account.provider,
            code: account.access_token,
            idToken: account.id_token,
          };

          const {
            data: {
              data: { token, user: dbUser },
            },
          } = await apiSocialLoginUser(socialPayload);

          const mergedUser = _.merge({}, user, dbUser, {
            accessToken: token,
            id: dbUser.id,
            email: dbUser.email || user.email,
          });
          Object.assign(user, mergedUser);

          return true;
        } catch (error) {
          console.error('Social login error:', error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        token.accessToken = (user as UserWithSession).accessToken;
        token.user = user as UserWithSession;
      }

      return token as ExtendedJWT;
    },
    async session({ session, token }) {
      const extendedToken = token as ExtendedJWT;

      return {
        ...session,
        user: extendedToken.user,
        accessToken: extendedToken.accessToken,
      } as ExtendedSession;
    },
  },
  pages: {
    signIn: '/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60,
  },
  secret: process.env.NEXTAUTH_SECRET,
};

export function getAuthSession() {
  return getServerSession(authOptions);
}

export function useAuthSession() {
  return useQuery({
    queryKey: ['auth-session'],
    queryFn: async () => await getSession(),
    staleTime: 5 * 60 * 1000,
  });
}

import { type UserObjectData } from '@/api/auth/types';
import { getItem, removeItem, setItem } from '@/lib/utils/storage';

const AUTH_USER = 'auth-user';
const PROFILE_IMAGE_KEY = 'profile-image';

export const getUser = () => getItem<UserObjectData>(AUTH_USER);
export const removeUser = () => removeItem(AUTH_USER);
export const setUser = (value: UserObjectData) =>
  setItem<UserObjectData>(AUTH_USER, value);

export const getProfileImageKey = () => getItem<number>(PROFILE_IMAGE_KEY);
export const removeProfileImageKey = () => removeItem(PROFILE_IMAGE_KEY);
export const setProfileImageKey = (value: number) =>
  setItem<number>(PROFILE_IMAGE_KEY, value);
