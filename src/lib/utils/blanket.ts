import validator from 'validator';
import dayjs from 'dayjs';
import {
  EventSearchInterface,
  ISingleEvent,
  TransactionItemProps,
  UserSearchInterface,
} from '@/api';
import { countryToCurrency } from '@/lib';

import { aeonik, inter } from '../constants/fonts';

export { aeonik, inter };

import { type ClassValue, clsx } from 'clsx';
import type { Path } from 'react-hook-form';
import { twMerge } from 'tailwind-merge';
import { z } from 'zod';
import { format } from 'date-fns';
import { TicketCategories } from '@/api';

import { type PresaleConfig } from '@/api';

const { isDecimal, isFloat, isInt, toFloat, toInt } = validator;

type ResponseType = {
  meta: {
    requestStatus: string;
  };
};

export function getFromLocalStorage(key: string): string | null {
  return window.localStorage.getItem(key);
}

export function removeFromLocalStorage(key: string) {
  window.localStorage.removeItem(key);
}

export function getFromSessionStorage(key: string): string | null {
  return sessionStorage.getItem(key);
}

export const handleRejectResponse = (
  response: ResponseType,
  success: (data: any) => void,
  failed: (data: any) => void
) => {
  if (response?.meta?.requestStatus === 'fulfilled') {
    success(response);
  }
  if (response?.meta?.requestStatus === 'rejected') {
    failed(response);
  }
};

export function formatNumberWithCommas(value: string) {
  const numeric = value.replace(/\D/g, '');
  if (!numeric) return '';
  return Number(numeric).toLocaleString();
}

export function formatAmounttoNumber(val: string) {
  return Number(val.replace(/,/g, ''));
}

export const removeUnwantedBackendNotationFromString = (str: string) => {
  // Replace "-" with space
  let modifiedString = str.replace(/-/g, ' ');

  // Replace "_" with space
  modifiedString = modifiedString.replace(/_/g, ' ');

  // Replace "." with space
  modifiedString = modifiedString.replace(/\./g, ' ');
  return modifiedString;
};

export const handleErrorAlertMessage = (
  response: any,
  defaultMessage: string
): string => {
  return response.payload
    ? typeof response.payload === 'string'
      ? removeUnwantedBackendNotationFromString(response.payload)
      : Array.isArray(response.payload) && response.payload.length > 0
        ? removeUnwantedBackendNotationFromString(response.payload[0])
        : defaultMessage
    : defaultMessage;
};

export function truncateString(str: string, maxLength: number) {
  if (str.length > maxLength) {
    return str.slice(0, maxLength - 3) + '...';
  }
  return str;
}

function isNumber(value: any): boolean {
  return typeof value === 'number';
}

function isString(value: any): boolean {
  return typeof value === 'string';
}

export function toNumber(string: string): number {
  if (isFloat(string)) {
    return toFloat(string);
  }

  if (isInt(string)) {
    return toInt(string);
  }
  return +string;
}

export function isValidStringAmount(stringAmount: string): boolean {
  if (isString(stringAmount) && stringAmount?.endsWith('.')) {
    return false;
  }

  return isDecimal(stringAmount);
}

export function isValidDecimalMonetaryValue(
  amountValue: AmountValue | any
): boolean {
  if (!isNumber(amountValue) && !isString(amountValue)) {
    return false;
  }

  return isNumber(amountValue) || isValidStringAmount(amountValue);
}

export const getAmountValueInMinor = (amount: AmountValue): number => {
  if (isValidDecimalMonetaryValue(amount)) {
    return toAmountInMinor(amount);
  }
  return 0;
};

export const constructQueryStrings = (
  payload: Partial<UserSearchInterface & EventSearchInterface>
) => {
  let queryString = '';
  Object.entries(payload).forEach((item) => {
    if (Array.isArray(item[1])) {
      item[1].forEach((nestedItem) => {
        queryString += `${queryString ? '&' : ''}${item[0]}=${nestedItem}`;
      });
    } else {
      queryString += `${queryString ? '&' : ''}${item[0]}=${item[1]}`;
    }
  });
  return queryString;
};

export const renderDate = (date: string | undefined) => {
  const currentDate = dayjs().format('YYYY-MM-DD');
  const tomorrowDate = dayjs().add(1, 'day').format('YYYY-MM-DD');
  const inputDate = dayjs(date).format('YYYY-MM-DD');

  if (inputDate === currentDate) {
    return 'Today';
  } else if (inputDate === tomorrowDate) {
    return 'Tomorrow';
  } else {
    const formattedDate = dayjs(date).format('MMM');
    const day = dayjs(date).date();
    let suffix = '';

    if (day === 1 || day === 21 || day === 31) {
      suffix = 'st';
    } else if (day === 2 || day === 22) {
      suffix = 'nd';
    } else if (day === 3 || day === 23) {
      suffix = 'rd';
    } else {
      suffix = 'th';
    }

    return `${day}${suffix} ${formattedDate}`;
  }
};

export const getAmountValueInMajor = (amount: AmountValue): number => {
  if (isValidDecimalMonetaryValue(amount)) {
    return toAmountInMajor(amount);
  }
  return 0;
};

interface ITickets {
  name: string;
  cost: number;
  quantity: number;
}

export const convertObjectToArray = (
  ticketsObj: Record<string, { cost: number; quantity: number }>
): ITickets[] => {
  const ticketsArray: ITickets[] = Object.keys(ticketsObj).map((ticketName) => {
    const { cost, quantity } = ticketsObj[ticketName];
    return {
      name: ticketName,
      cost: getAmountValueInMajor(cost),
      quantity: quantity,
    };
  });
  return ticketsArray;
};

export const isDateInThePast = (dateString: string) => {
  const providedDate = new Date(dateString);
  const currentDate = new Date();
  return providedDate < currentDate;
};

export function formatCurrency(
  value: number | string | undefined | null
): string {
  if (value === undefined || value === null || value === '') return '₦0';

  let amount: number;

  if (typeof value === 'string') {
    const cleaned = value.replace(/,/g, '');
    amount = parseFloat(cleaned);
  } else {
    amount = value;
  }

  if (isNaN(amount)) return '₦0';

  return `₦${amount.toLocaleString('en-NG', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })}`;
}
export const groupTransactionsByDate = (transactions: TransactionItemProps[]) =>
  transactions.reduce<Record<string, TransactionItemProps[]>>(
    (acc, transaction) => {
      const date = new Date(transaction.createdAt).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(transaction);
      return acc;
    },
    {}
  );

export const toLocalISOString = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
};

export const truncateText = (text: string, maxLength: number) => {
  if (text.length > maxLength) {
    return `${text.slice(0, maxLength)}...`;
  }
  return text;
};

export const getCurrencyFromCountry = async (
  countryCode: string
): Promise<string> => {
  return countryToCurrency[countryCode] ?? 'NGN'; // Default to NGN
};

export async function getCurrentLocation() {
  const response = await fetch('https://ipapi.co/json/');
  if (!response.ok) throw new Error('Failed to get location');
  const data = await response.json();
  return data.country_name;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDateTime(date?: Date | null): string {
  if (!date) return 'Start date and time';
  return format(date, 'd MMM, yyyy - h:mm a');
}

const formatDate = (dt: Date | string) => {
  const date = new Date(dt);
  return isNaN(date.getTime()) ? 'Invalid date' : format(date, 'PPP');
};

const formatTime = (dt: Date | string) => {
  const date = new Date(dt);
  return isNaN(date.getTime()) ? 'Invalid time' : format(date, 'p');
};

export { formatDate, formatTime };

export const PASSWORD_REGEX = {
  twoLowerCaseRegex: /^(.*[a-z]){2,}.*$/,
  oneUpperCaseRegex: /^(.*[A-Z]){1,}.*$/,
  twoDigitsRegex: /^(.*\d){1,}.*$/,
  oneSpecialCharacter: /^.*[!@#$%^&*()_+{}\\[\]:;<>,.?/~\\-].*$/,
  eightCharacterLong: /^.{8,}$/,
};

export const EMAIL_REGEX =
  /^(([^<>()[\]\\.,;:\s@\\"]+(\.[^<>()[\]\\.,;:\s@\\"]+)*)|(\\".+\\"))@(([^<>()[\]\\.,;:\s@\\"]+\.)+[^<>()[\]\\.,;:\s@\\"]{2,})$/i;

export const USERNAME_REGEX = {
  noStartEndDotUnderscore: /^(?![._])(?!.*[._]$).*$/,
  noConsecutiveDotUnderscore: /^(?!.*[._]{2}).*$/,
  startWithAlphanumeric: /^[a-zA-Z0-9].*$/,
  validCharacters: /^[a-zA-Z0-9._]*$/,
  validDotUnderscoreUsage: /^(?!.*[._]{2})(?!(?!.*\d)[._]).*$/,
};

export const NO_SPACE_REGEX = /^[^\s]*$/;

export function getZodSchemaKeys<T>(
  schema: z.ZodSchema<Partial<T>>
): Path<T>[] {
  if (schema instanceof z.ZodObject) {
    return Object.keys(schema.shape) as Path<T>[];
  }
  return [];
}

import { differenceInSeconds, parseISO } from 'date-fns';
import moment from 'moment';

/**
 * Extracts the first name and last name from a full name string
 * @param {string} fullName - The full name to parse
 * @returns {Object} An object containing firstName and lastName
 */
export const getNameParts = (
  fullName?: string
): {
  firstName: string;
  lastName: string;
} => {
  // Handle empty or invalid input
  if (!fullName || typeof fullName !== 'string') {
    return { firstName: '', lastName: '' };
  }

  // Trim and split the full name by spaces
  const nameParts = fullName.trim().split(/\s+/);

  // If there's only one part, it's considered the first name
  if (nameParts.length === 1) {
    return { firstName: nameParts[0], lastName: '' };
  }

  // First part is the first name
  const firstName = nameParts[0];

  // Everything else is considered the last name (handles multiple last names)
  const lastName = nameParts.slice(1).join(' ');

  return { firstName, lastName };
};

/**
 * Recursively filters out empty properties and undefined values from an object
 * Also cleans nested objects by removing their empty or undefined properties
 * @template T Object type with string keys and various value types
 * @param obj - The object to filter
 * @returns A new object with empty properties and undefined values removed at all levels
 */
export const filterEmptyProperties = <T extends Record<string, any>>(
  obj: T
): Partial<T> => {
  // Create a new object to store filtered properties
  const result: Partial<T> = {} as Partial<T>;

  // Process each property in the object
  for (const [key, value] of Object.entries(obj)) {
    // Skip undefined values
    if (value === undefined) continue;

    // Skip empty strings
    if (value === '') continue;

    // Handle nested objects recursively
    if (typeof value === 'object' && value !== null) {
      // For arrays
      if (Array.isArray(value)) {
        const filteredArray = value.filter(
          (item) =>
            item !== undefined &&
            item !== '' &&
            (typeof item !== 'object' || Object.keys(item).length > 0)
        );

        // Only add non-empty arrays
        if (filteredArray.length > 0) {
          result[key as keyof T] = filteredArray as any;
        }
      }
      // For nested objects
      else {
        const filteredNestedObj = filterEmptyProperties(value);

        // Only add non-empty objects
        if (Object.keys(filteredNestedObj).length > 0) {
          result[key as keyof T] = filteredNestedObj as any;
        }
      }
    }
    // Add primitive values directly
    else {
      result[key as keyof T] = value;
    }
  }

  return result;
};

export const formatAmount = (
  amount: number | bigint,
  locales?: Intl.LocalesArgument,
  currencyCode?: string
) => {
  const formatter = new Intl.NumberFormat(locales || 'en-NG', {
    style: 'currency',
    currency: currencyCode || 'NGN',
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
  }).format(amount);
  return formatter;
};

// Utility function to format numbers with commas
export const formatNumber = (value: number = 0, decimals = 2) => {
  return toAmountInMajor(value).toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

type AmountValue = string | number;

export function toAmountInMinor(amountValue: AmountValue) {
  if (typeof amountValue === 'string') {
    amountValue = amountValue.replace(/,/g, '');
    return Number(amountValue) * 100;
  }
  return amountValue * 100;
}

export function toAmountInMajor(amountValue: AmountValue) {
  if (typeof amountValue === 'string') {
    amountValue = amountValue.replace(/,/g, '');
    return Number(amountValue) / 100;
  }
  return amountValue / 100;
}

// Utility function to format percentage
export const formatPercentage = (num: number): string => {
  return `${num.toFixed(2)}%`;
};

export const formatLastSeen = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const oneMinute = 60 * 1000;
  const oneHour = 60 * oneMinute;
  const oneDay = 24 * oneHour;

  if (diff < oneMinute) {
    return 'just now';
  } else if (diff < oneHour) {
    const minutes = Math.floor(diff / oneMinute);
    return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diff < oneDay) {
    const hours = Math.floor(diff / oneHour);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diff < 2 * oneDay) {
    return 'yesterday';
  } else {
    return moment.utc(date).format('MMM D');
  }
};

export const removeDuplicatesById = <T extends Record<string, any>>(
  array: T[]
): T[] => {
  const map = new Map();
  array.forEach((item) => map.set(item.id, item));
  return Array.from(map.values());
};

export const openInMaps = (address: string) => {
  const formattedAddress = encodeURIComponent(address);
  const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${formattedAddress}`;

  window.open(mapsUrl, '_blank');
};

// Utility function to clean filename
export const removeSpacesAndSymbols = (text: string): string =>
  text.replace(/[^\w\s]/gi, '').replace(/\s+/g, '');

export const formatEventDateTime = (
  startTime: string = '',
  endTime: string = ''
) => {
  const startMoment = moment.utc(startTime);
  const endMoment = moment.utc(endTime);
  const isSameDay = startMoment.isSame(endMoment, 'day');

  if (isSameDay) {
    return `${startMoment.format('dddd, DD MMM YYYY')}\n${startMoment.format(
      'LT'
    )} - ${endMoment.format('LT')}`;
  } else {
    return `${startMoment.format(
      'dddd, DD MMM YYYY LT'
    )} - \n${endMoment.format('dddd, DD MMM YYYY LT')}`;
  }
};

export const isPresaleActive = (presaleTicket: PresaleConfig): boolean => {
  if (!presaleTicket.startDateTime || !presaleTicket.endDateTime) {
    return false;
  }

  const now = dayjs();
  const startDate = dayjs(presaleTicket.startDateTime);
  const endDate = dayjs(presaleTicket.endDateTime);

  return now.isAfter(startDate) && now.isBefore(endDate);
};

export const hasValidPresaleTicketCategories = (
  presaleConfig?: PresaleConfig[]
): boolean => {
  if (!presaleConfig || presaleConfig.length === 0) {
    return false;
  }

  return presaleConfig.some((presaleTicket) => isPresaleActive(presaleTicket));
};

export function maskLast(str: string, count = 4, maskChar = '*') {
  if (str.length <= count) {
    return maskChar.repeat(count);
  }
  return str.slice(0, -count) + maskChar.repeat(count);
}

export function deg2rad(deg: number) {
  return deg * (Math.PI / 180);
}

export function getDistanceFromLatLonInKm(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
) {
  const R = 6371;
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

export function formatEnumLabel(value: string): string {
  return value
    .toLowerCase()
    .split('_')
    .map((s) => s.charAt(0).toUpperCase() + s.slice(1))
    .join(' ');
}

// Type guard to ensure ticketCategories is of correct type
export const hasTicketCategories = (
  categories: TicketCategories | null | undefined
): categories is TicketCategories => {
  return categories !== null && categories !== undefined;
};

export const renderDateTitle = (
  date: string | number | Date | dayjs.Dayjs | null | undefined
): string => {
  const givenDate = dayjs(date);

  const today = dayjs().startOf('day');

  const yesterday = dayjs().subtract(1, 'day').startOf('day');

  if (givenDate.isSame(today, 'day')) {
    return 'Today';
  }

  if (givenDate.isSame(yesterday, 'day')) {
    return 'Yesterday';
  }

  return givenDate.format('MMM DD') + getOrdinalSuffix(givenDate.date());
};

const getOrdinalSuffix = (day: number) => {
  const suffixes = ['th', 'st', 'nd', 'rd'];
  const v = day % 100;
  return suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0];
};

export const isToday = (
  date: string | number | Date | dayjs.Dayjs | null | undefined
): boolean => {
  const givenDate = dayjs(date);

  const today = dayjs().startOf('day');

  return givenDate.isSame(today, 'day');
};

export interface AnalyticsMetrics {
  totalSold: number;
  totalTicketQuantity: number;
  conversionRate: number;
  isSoldOut: boolean;
}

export const computeAnalyticsMetrics = (
  event: ISingleEvent
): AnalyticsMetrics => {
  const { ticketCategories, presaleConfig, registrationCount, maxAttendees } =
    event;
  let totalSold = 0;
  let totalTicketQuantity = 0;

  if (hasTicketCategories(ticketCategories)) {
    for (const { sold = 0, quantity } of Object.values(ticketCategories)) {
      totalSold += sold;
      totalTicketQuantity += sold + (quantity || 0);
    }
  } else {
    totalSold = registrationCount || 0;
    totalTicketQuantity = maxAttendees || 0;
  }

  if (presaleConfig && presaleConfig.length > 0) {
    for (const presale of presaleConfig) {
      const sold = presale.totalTickets - (presale.availableTickets || 0);
      totalSold += sold;
      totalTicketQuantity += presale.totalTickets;
    }
  }

  const isSoldOut =
    totalTicketQuantity > 0 && totalSold === totalTicketQuantity;

  const conversionRate =
    totalTicketQuantity > 0 ? (totalSold / totalTicketQuantity) * 100 : 0;

  return {
    totalSold,
    totalTicketQuantity,
    conversionRate,
    isSoldOut,
  };
};

export const getDateRange = (startDate: Date, endDate: Date): string[] => {
  const dates: string[] = [];
  const current = new Date(startDate);

  while (current <= endDate) {
    dates.push(
      current.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })
    );
    current.setDate(current.getDate() + 1);
  }

  return dates;
};

export const getRemainingTimerSeconds = ({
  expiresAt,
  defaultTimer,
}: {
  expiresAt?: string;
  defaultTimer: number;
}): number => {
  if (!expiresAt) return defaultTimer;
  const expiryDate = parseISO(expiresAt);
  const now = new Date();
  const secondsLeft = differenceInSeconds(expiryDate, now);
  return Math.max(secondsLeft, 0);
};

export function hasProperty<T extends object>(
  obj: T,
  key: PropertyKey
): key is keyof T {
  return key in obj;
}

export const getTimeValue = (time: any) => {
  if (!time) return new Date();
  if (typeof time.toDate === 'function') return time.toDate().getTime();
  if (time instanceof Date) return time.getTime();
  if (typeof time === 'number') return time;
  return new Date();
};
