export * from './blanket';
export * from './logger';
export * from './auth';
export * from './og';
export * from './storage';
export * from './auth';
export * from './location';

export function formatNumberWithCommas(value: string) {
  const numeric = value.replace(/\D/g, '');
  if (!numeric) return '';
  return Number(numeric).toLocaleString();
}

export function kebabToSnakeCase(str: string): string {
  return str.replace(/-/g, '_');
}

export function formatAmounttoNumber(val: string) {
  return Number(val.replace(/,/g, ''));
}

export function downloadICS(event: {
  title: string;
  description: string;
  location: string;
  start: Date;
  end: Date;
}) {
  const start =
    event.start.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  const end = event.end.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

  const ics = `
BEGIN:VCALENDAR
VERSION:2.0
BEGIN:VEVENT
SUMMARY:${event.title}
DESCRIPTION:${event.description}
LOCATION:${event.location}
DTSTART:${start}
DTEND:${end}
END:VEVENT
END:VCALENDAR
  `.trim();

  const blob = new Blob([ics], { type: 'text/calendar;charset=utf-8' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `${event.title}.ics`;
  link.click();

  URL.revokeObjectURL(url);
}
