export const getLatLongFromCountry = async (
  countryName: string
): Promise<{ latitude: number; longitude: number } | null> => {
  try {
    if (!countryName.trim()) return null;

    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?country=${encodeURIComponent(
        countryName
      )}&format=json&limit=1`
    );

    const data = await response.json();

    if (data && data.length > 0) {
      return {
        latitude: parseFloat(data[0].lat),
        longitude: parseFloat(data[0].lon),
      };
    }

    console.warn(`No coordinates found for "${countryName}"`);
    return null;
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
};
