import { withAuth } from 'next-auth/middleware';

export default withAuth(
  function middleware() {
    // custom logic if needed
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        const publicRoutes = [
          '/',
          '/register',
          '/password',
          '/onboarding',
          '/login',
          '/features',
          '/support',
          '/forgot-password',
          '/download',
        ];

        const isPublicEventRoute =
          pathname === '/events' || pathname.startsWith('/events/');

        if (pathname === '/events/create') {
          return !!token;
        }

        if (publicRoutes.includes(pathname)) {
          return true;
        }

        if (isPublicEventRoute) {
          return true;
        }

        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|images|svgs|favicon.ico|public|auth).*)',
  ],
};
