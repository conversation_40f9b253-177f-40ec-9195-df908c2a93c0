@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@theme {
  --animate-background: background-move ease infinite;

  @keyframes background-move {
    0%,
    100% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }
  }
}

@theme {
  --font-primary: var(--font-aeonik);
  --font-aeonik: var(--font-aeonik);
  --font-inter:
    var(--font-inter), ui-sans-serif, system-ui, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-source_sans: SourceSansPro;

  --color-primary: #7257ff;
  --color-primary_dark: #a992f5;
  --color-primary_50: #efe6f0;
  --color-primary_100: #f2e5ff;
  --color-primary_200: #e9d7fe;
  --color-primary_300: #745dbe;
  --color-primary_accent: #f1eff9;
  --color-btn_background: #ffffffb2;
  --color-btn_background_dark: #1a1a1a80;
  --color-secondary: #00d4ff;
  --color-grey_100: #f4f4f5;
  --color-grey_200: #e4e4e7;
  --color-error_400: #fb6f84;
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-dark: #222222;
  --color-tertiary: #1a1a1ab2;
  --color-tertiary_400: #a1a1aa;

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-white: #ffffff;
  --color-black: #000000;
  --color-profile-ring-start: #664fb0;
  --color-profile-ring-end: #a992f5;

  --color-grey-10: #f4f6f7;
  --color-grey-20: #e8ebeb;
  --color-grey-30: #daddde;
  --color-grey-40: #c1c4c6;
  --color-grey-50: #898d8f;
  --color-grey-60: #6e7375;
  --color-grey-70: #53575a;
  --color-grey-80: #2f3133;
  --color-grey-90: #1f2224;
  --color-grey-100: #131214;

  --color-brand-10: #f6f5ff;
  --color-brand-20: #f0edff;
  --color-brand-30: #dbd4ff;
  --color-brand-40: #b4a6ff;
  --color-brand-50: #907aff;
  --color-brand-60: #7257ff;
  --color-brand-70: #5336e2;
  --color-brand-80: #34228f;
  --color-brand-90: #291f61;
  --color-brand-100: #130d33;

  --color-green-10: #e8faf0;
  --color-green-20: #d7f5e5;
  --color-green-30: #9bebbf;
  --color-green-40: #51c285;
  --color-green-50: #23a15d;
  --color-green-60: #008557;
  --color-green-70: #006341;
  --color-green-80: #0d4f2b;
  --color-green-90: #05381d;
  --color-green-100: #021f10;

  --color-yellow-10: #fff9e6;
  --color-yellow-20: #ffefb3;
  --color-yellow-30: #ffd84d;
  --color-yellow-40: #ed9b16;
  --color-yellow-50: #d67507;
  --color-yellow-60: #b26205;
  --color-yellow-70: #824b0d;
  --color-yellow-80: #663c0c;
  --color-yellow-90: #4d2b05;
  --color-yellow-100: #331c03;

  --color-red-10: #fff3f0;
  --color-red-20: #ffe9e3;
  --color-red-30: #ffcec2;
  --color-red-40: #ff9175;
  --color-red-50: #ff5226;
  --color-red-60: #db340b;
  --color-red-70: #ad1d00;
  --color-red-80: #8a1700;
  --color-red-90: #611000;
  --color-red-100: #290800;

  --color-blue-10: #f2f7ff;
  --color-blue-20: #e5f0ff;
  --color-blue-30: #c2dcff;
  --color-blue-40: #75b1ff;
  --color-blue-50: #308aff;
  --color-blue-60: #0a69fa;
  --color-blue-70: #0050c7;
  --color-blue-80: #003c94;
  --color-blue-90: #042961;
  --color-blue-100: #021026;

  --color-fg-base-light: #131214;
  --color-fg-base-dark: #ffffff;

  --color-fg-muted-light: #6e7375;
  --color-fg-muted-dark: #898d8f;

  --color-fg-subtle-light: #898d8f;
  --color-fg-subtle-dark: #6e7375;

  --color-fg-link: #7257ff;

  --color-fg-disabled-light: #898d8f;
  --color-fg-disabled-dark: #6e7375;

  --color-fg-on-contrast-light: #ffffff;
  --color-fg-on-contrast-dark: #131214;

  --color-fg-static-dark: #131214;
  --color-fg-static-light: #ffffff;

  --color-fg-danger-light: #db340b;
  --color-fg-danger-dark: #ff9175;

  --color-fg-success-light: #008557;
  --color-fg-success-dark: #51c285;

  --color-fg-warning-light: #b26205;
  --color-fg-warning-dark: #ffd84d;

  --color-fg-error-light: #db340b;
  --color-fg-error-dark: #ff9175;

  --color-fg-info-light: #0a69fa;
  --color-fg-info-dark: #75b1ff;

  --color-bg-canvas-light: #ffffff;
  --color-bg-canvas-dark: #131214;

  --color-bg-subtle-light: #f4f6f7;
  --color-bg-subtle-dark: #1f2224;

  --color-bg-muted-light: #e8ebeb;
  --color-bg-muted-dark: #2f3133;

  --color-bg-contrast-light: #131214;
  --color-bg-contrast-dark: #ffffff;

  --color-bg-surface-light: #ffffff;
  --color-bg-surface-dark: #2f3133;

  --color-bg-interactive-primary-light: #e8ebeb;
  --color-bg-interactive-primary-dark: #2f3133;

  --color-bg-interactive-secondary-light: #daddde;
  --color-bg-interactive-secondary-dark: #53575a;

  --color-bg-interactive-tertiary-light: #c1c4c6;
  --color-bg-interactive-tertiary-dark: #6e7375;

  --color-bg-success-light: #e8faf0;
  --color-bg-success-dark: #0d4f2b;

  --color-bg-success-contrast-light: #008557;
  --color-bg-success-contrast-dark: #23a15d;

  --color-bg-error-light: #fff3f0;
  --color-bg-error-dark: #8a1700;

  --color-bg-error-contrast-light: #db340b;
  --color-bg-error-contrast-dark: #898d8f;

  --color-bg-warning-light: #fff9e6;
  --color-bg-warning-dark: #663c0c;

  --color-bg-warning-contrast: #ffd84d;

  --color-bg-info-light: #f2f7ff;
  --color-bg-info-dark: #c2dcff;

  --color-bg-info-contrast: #0a69fa;

  --color-bg-overlay-light: rgba(0, 0, 0, 0.45);
  --color-bg-overlay-dark: rgba(0, 0, 0, 0.7);

  --color-bg-notification: #db340b;

  --color-bg-disabled-light: #daddde;
  --color-bg-disabled-dark: #2f3133;

  --color-bg-danger-primary: #ff5226;
  --color-bg-danger-secondary: #db340b;
  --color-bg-danger-tertiary: #ad1d00;

  --color-accent-on-accent: #ffffff;

  --color-accent-subtle-light: #f0edff;
  --color-accent-subtle-dark: #291f61;

  --color-accent-muted-light: #dbd4ff;
  --color-accent-muted-dark: #34228f;

  --color-accent-dim-light: #b4a6ff;
  --color-accent-dim-dark: #5336e2;

  --color-accent-moderate: #7257ff;

  --color-accent-bold-light: #5336e2;
  --color-accent-bold-dark: #b4a6ff;

  --color-accent-strong-light: #34228f;
  --color-accent-strong-dark: #dbd4ff;

  --color-accent-intense-light: #291f61;
  --color-accent-intense-dark: #f0edff;

  --color-border-subtle-light: #e8ebeb;
  --color-border-subtle-dark: #2f3133;

  --color-border-muted-light: #daddde;
  --color-border-muted-dark: #53575a;

  --color-border-interactive-primary-light: #daddde;
  --color-border-interactive-primary-dark: #53575a;

  --color-border-contrast-light: #131214;
  --color-border-contrast-dark: #ffffff;

  --color-border-disabled-light: #c1c4c6;
  --color-border-disabled-dark: #6e7375;

  --color-border-error-light: #ff9175;
  --color-border-error-dark: #ad1d00;

  --color-border-custom: hsl(var(--border));

  --color-social-google-primary: #f4f6f7;
  --color-social-google-secondary: #e8ebeb;
  --color-social-google-tertiary: #daddde;

  --color-social-twitter-primary: #1da1f2;
  --color-social-twitter-secondary: #0c90e1;
  --color-social-twitter-tertiary: #0b84cf;

  --color-social-facebook-primary: #0078ff;
  --color-social-facebook-secondary: #0067db;
  --color-social-facebook-tertiary: #0056b8;

  --color-social-apple-primary-light: #131214;
  --color-social-apple-primary-dark: #ffffff;

  --color-social-apple-secondary-light: #1f2224;
  --color-social-apple-secondary-dark: #f4f6f7;

  --color-social-apple-tertiary-light: #2f3133;
  --color-social-apple-tertiary-dark: #e8ebeb;

  --color-sidebar: hsl(var(--sidebar-background));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));

  --text-color-fg-base-light: #131214;
  --text-color-fg-base-dark: #ffffff;

  --text-color-fg-muted-light: #6e7375;
  --text-color-fg-muted-dark: #898d8f;

  --text-color-fg-subtle-light: #898d8f;
  --text-color-fg-subtle-dark: #6e7375;

  --text-color-fg-link: #7257ff;

  --text-color-fg-disabled-light: #898d8f;
  --text-color-fg-disabled-dark: #6e7375;

  --text-color-fg-on-contrast-light: #ffffff;
  --text-color-fg-on-contrast-dark: #131214;

  --text-color-fg-static-dark: #131214;
  --text-color-fg-static-light: #ffffff;

  --text-color-fg-danger-light: #db340b;
  --text-color-fg-danger-dark: #ff9175;

  --text-color-fg-success-light: #008557;
  --text-color-fg-success-dark: #51c285;

  --text-color-fg-warning-light: #b26205;
  --text-color-fg-warning-dark: #ffd84d;

  --text-color-fg-error-light: #db340b;
  --text-color-fg-error-dark: #ff9175;

  --text-color-fg-info-light: #0a69fa;
  --text-color-fg-info-dark: #75b1ff;

  --text-color-bg-canvas-light: #ffffff;
  --text-color-bg-canvas-dark: #131214;

  --text-color-bg-subtle-light: #f4f6f7;
  --text-color-bg-subtle-dark: #1f2224;

  --text-color-bg-muted-light: #e8ebeb;
  --text-color-bg-muted-dark: #2f3133;

  --text-color-bg-contrast-light: #131214;
  --text-color-bg-contrast-dark: #ffffff;

  --text-color-bg-surface-light: #ffffff;
  --text-color-bg-surface-dark: #2f3133;

  --text-color-bg-interactive-primary-light: #e8ebeb;
  --text-color-bg-interactive-primary-dark: #2f3133;

  --text-color-bg-interactive-secondary-light: #daddde;
  --text-color-bg-interactive-secondary-dark: #53575a;

  --text-color-bg-interactive-tertiary-light: #c1c4c6;
  --text-color-bg-interactive-tertiary-dark: #6e7375;

  --text-color-bg-success-light: #e8faf0;
  --text-color-bg-success-dark: #0d4f2b;

  --text-color-bg-success-contrast-light: #008557;
  --text-color-bg-success-contrast-dark: #23a15d;

  --text-color-bg-error-light: #fff3f0;
  --text-color-bg-error-dark: #8a1700;

  --text-color-bg-error-contrast-light: #db340b;
  --text-color-bg-error-contrast-dark: #898d8f;

  --text-color-bg-warning-light: #fff9e6;
  --text-color-bg-warning-dark: #663c0c;

  --text-color-bg-warning-contrast: #ffd84d;

  --text-color-bg-info-light: #f2f7ff;
  --text-color-bg-info-dark: #c2dcff;

  --text-color-bg-info-contrast: #0a69fa;

  --text-color-bg-overlay-light: rgba(0, 0, 0, 0.45);
  --text-color-bg-overlay-dark: rgba(0, 0, 0, 0.7);

  --text-color-bg-notification: #db340b;

  --text-color-bg-disabled-light: #daddde;
  --text-color-bg-disabled-dark: #2f3133;

  --text-color-bg-danger-primary: #ff5226;
  --text-color-bg-danger-secondary: #db340b;
  --text-color-bg-danger-tertiary: #ad1d00;

  --text-color-accent-on-accent: #ffffff;

  --text-color-accent-subtle-light: #f0edff;
  --text-color-accent-subtle-dark: #291f61;

  --text-color-accent-muted-light: #dbd4ff;
  --text-color-accent-muted-dark: #34228f;

  --text-color-accent-dim-light: #b4a6ff;
  --text-color-accent-dim-dark: #5336e2;

  --text-color-accent-moderate: #7257ff;

  --text-color-accent-bold-light: #5336e2;
  --text-color-accent-bold-dark: #b4a6ff;

  --text-color-accent-strong-light: #34228f;
  --text-color-accent-strong-dark: #dbd4ff;

  --text-color-accent-intense-light: #291f61;
  --text-color-accent-intense-dark: #f0edff;

  --text-color-border-subtle-light: #e8ebeb;
  --text-color-border-subtle-dark: #2f3133;

  --text-color-border-muted-light: #daddde;
  --text-color-border-muted-dark: #53575a;

  --text-color-border-interactive-primary-light: #daddde;
  --text-color-border-interactive-primary-dark: #53575a;

  --text-color-border-contrast-light: #131214;
  --text-color-border-contrast-dark: #ffffff;

  --text-color-border-disabled-light: #c1c4c6;
  --text-color-border-disabled-dark: #6e7375;

  --text-color-border-error-light: #ff9175;
  --text-color-border-error-dark: #ad1d00;

  --text-color-border-custom: hsl(var(--border));

  --text-color-social-google-primary: #f4f6f7;
  --text-color-social-google-secondary: #e8ebeb;
  --text-color-social-google-tertiary: #daddde;

  --text-color-social-twitter-primary: #1da1f2;
  --text-color-social-twitter-secondary: #0c90e1;
  --text-color-social-twitter-tertiary: #0b84cf;

  --text-color-social-facebook-primary: #0078ff;
  --text-color-social-facebook-secondary: #0067db;
  --text-color-social-facebook-tertiary: #0056b8;

  --text-color-social-apple-primary-light: #131214;
  --text-color-social-apple-primary-dark: #ffffff;

  --text-color-social-apple-secondary-light: #1f2224;
  --text-color-social-apple-secondary-dark: #f4f6f7;

  --text-color-social-apple-tertiary-light: #2f3133;
  --text-color-social-apple-tertiary-dark: #e8ebeb;

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  --background-image-signup-bg: url('/svg/Background.svg');
  --background-image-primary-gradient: linear-gradient(
    to right,
    #a992f5,
    #664fb0
  );

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary-a: 254 38% 50%;
    --primary-b: 254 83% 77%;

    --primary: var(--primary-a);
    /* --primary: linear-gradient(
      295deg,
      hsl(254deg 38% 50%) 0%,
      hsl(254deg 43% 53%) 11%,
      hsl(254deg 48% 56%) 22%,
      hsl(254deg 53% 59%) 33%,
      hsl(254deg 58% 62%) 44%,
      hsl(254deg 63% 65%) 56%,
      hsl(254deg 68% 68%) 67%,
      hsl(254deg 73% 71%) 78%,
      hsl(254deg 78% 74%) 89%,
      hsl(254deg 83% 77%) 100%
    ); */
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border-custom;
  }
  body {
    @apply bg-background text-foreground;
  }
}

*::-webkit-scrollbar {
  display: none;
}

@layer utilities {
  .rc-steps-item-title {
    @apply text-white!;
  }

  .rc-steps-item-description {
    @apply text-[#E9D7FE]!;
  }

  .rc-steps-vertical .rc-steps-item {
    @apply mb-5;
  }
  .rc-steps-item-finish .rc-steps-item-tail:after {
    @apply bg-white!;
  }

  .rc-steps-vertical .rc-steps-item-tail:after {
    @apply h-[160%]!;
  }

  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type='number'] {
    -moz-appearance: textfield;
  }

  /* .embla__slide {
    flex: 0 0 100%;
    min-width: 0;
  } */

  .basis-full {
    flex-basis: fit-content;
  }

  .custom-blob {
    border-radius: 45% 55% 60% 40% / 30% 70% 30% 70%;
    filter: blur(50px);
  }
}

@layer base {
  * {
    @apply border-border-custom;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* globals.css */

/* --- React Datepicker Light mode (default) --- */
.react-datepicker {
  @apply bg-white border border-gray-200 text-gray-900 rounded-lg shadow-lg;
}
.react-datepicker__header {
  @apply bg-gray-100 border-b border-gray-200;
}
.react-datepicker__current-month,
.react-datepicker__day-name {
  @apply text-gray-700;
}
.react-datepicker__day {
  @apply text-gray-900 hover:bg-gray-100;
}
.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  @apply bg-accent-moderate text-white;
}
.react-datepicker__time-list {
  @apply bg-white text-gray-900;
}
.react-datepicker__time-list-item {
  @apply hover:bg-gray-100;
}
.react-datepicker__time-list-item--selected {
  @apply bg-accent-moderate text-white;
}

/* --- Dark mode --- */
.dark .react-datepicker {
  @apply bg-gray-800 border border-gray-700 text-gray-100;
}
.dark .react-datepicker__header {
  @apply bg-gray-900 border-b border-gray-700;
}
.dark .react-datepicker__current-month,
.dark .react-datepicker__day-name {
  @apply text-gray-200;
}
.dark .react-datepicker__day {
  @apply text-gray-100 hover:bg-gray-700;
}
.dark .react-datepicker__day--selected,
.dark .react-datepicker__day--keyboard-selected {
  @apply bg-accent-moderate text-white;
}
.dark .react-datepicker__time-list {
  @apply bg-gray-800 text-gray-100;
}

.dark .react-datepicker__time-list-item {
  color: rgb(243 244 246) !important;
}
.dark .react-datepicker__time-list-item:hover {
  background-color: rgb(55 65 81) !important;
}
.dark .react-datepicker__time-list-item--selected {
  background-color: var(--color-accent-moderate) !important;
  color: white !important;
}
.dark .react-datepicker-time__header {
  @apply bg-gray-900 text-gray-200 border-gray-700;
}

@layer base {
  button {
    @apply disabled:border-border-muted-light disabled:dark:border-border-muted-dark disabled:cursor-default disabled:text-fg-muted-light disabled:dark:text-fg-muted-light;
  }
}
