declare global {
  interface LocationObject {
    city: string;
    state: string;
    street: string;
    address: string;
    country: string;
    landmark?: string;
    coordinates: Point;
  }

  interface CustomError {
    message: string;
    statusCode: number;
  }

  interface NavigationItem {
    title: string;
    href: string;
    Icon: string;
  }

  interface FollowingUser {
    id: string;
    displayName: string;
    username: string;
    avatar: string;
  }

  type SidebarSection = {
    title?: string;
    items: NavigationItem[];
    tab?: boolean;
  };

  interface FormData {
    [key: string]: any;
  }
}

export {};
