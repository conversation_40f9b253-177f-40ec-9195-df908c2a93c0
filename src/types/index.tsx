export type TabScreenItem = {
  key: string;
  title: string;
  component: React.ComponentType;
  notificationCount?: number;
  data?: any;
};
export interface AppTabProps {
  items: TabScreenItem[];

  labelStyle?: React.CSSProperties;
  tabBarStyle?: React.CSSProperties;
  indicatorStyle?: React.CSSProperties;
  indicatorContainerStyle?: React.CSSProperties;
  contentContainerStyle?: React.CSSProperties;
  style?: React.CSSProperties;

  tabIndex?: number;
  tabSetIndex?: (i: number) => void;

  containerClassName?: string;
  contentContainerClassName?: string;
  tabBarClassName?: string;
  tabBarFocusedClassName?: string;
  tabBarLabelClassName?: string;
  tabBarLabelActiveClassName?: string;
  tabBarLabelInactiveClassName?: string;
  countContainerClassName?: string;
  countClassName?: string;
}
export * from './form.interface';
export * from './search.interface';
export * from './general.interface';
export * from './general.interface';

export type TransactionType = 'CREDIT' | 'DEBIT';
export type TransactionStatus = 'PENDING' | 'FAILED' | 'SUCCESS';
export type TransactionCategory =
  | 'FUND_WALLET'
  | 'WITHDRAW_WALLET'
  | 'SONG_REQUEST'
  | 'DJ_SESSION_EARNINGS'
  | 'EVENT_TICKET_PURCHASE'
  | 'EVENT_TICKET_EARNINGS';

export interface Transaction {
  id: string;
  amount: number;
  balance: number;
  type: TransactionType;
  category: TransactionCategory;
  status: TransactionStatus;
  meta: Record<string, any>;
  walletId: string;
  userId: string;
  createdAt?: string;
  updatedAt?: string;
  currency?: string;
}

export type Country = {
  name: string;
  code: string; // ISO Alpha-2 code e.g. "AF"
  capital: string;
  region: string; // e.g. "AS"
  currency: {
    code: string;
    name: string;
    symbol: string | null;
  };
  language: {
    code: string | null;
    name: string;
  };
  flag: string; // Unicode flag
  dialling_code: string; // e.g. "+93"
  isoCode: string; // numeric code e.g. "004"
};

export type Countries = Country[];